## Stage 1 : build with maven builder image
FROM maven:3.8.6 AS build
COPY pom.xml /code/
WORKDIR /code
RUN mvn -B org.apache.maven.plugins:maven-dependency-plugin:3.1.2:go-offline
COPY src /code/src
# TEST
RUN rm /code/src/main/resources/application.properties

#RUN mvn package
RUN mvn package -Dmaven.test.skip=true -Dquarkus.swagger-ui.always-include=true -Dquarkus.swagger-ui.urls.direct=/iad-order/q/openapi

## Stage 2 : create the docker final image
FROM registry.access.redhat.com/ubi8/openjdk-17:1.11

ENV LANGUAGE='en_US:en'

# We make four distinct layers so if there are application changes the library layers can be re-used
COPY --from=build --chown=185 /code/target/quarkus-app/lib/ /deployments/lib/
COPY --from=build --chown=185 /code/target/quarkus-app/*.jar /deployments/
COPY --from=build --chown=185 /code/target/quarkus-app/app/ /deployments/app/
COPY --from=build --chown=185 /code/target/quarkus-app/quarkus/ /deployments/quarkus/
COPY --from=build --chown=185 /code/target/quarkus-app/quarkus/ /deployments/quarkus/
COPY --from=build --chown=185 /code/src/main/resources/db/ /deployments/app/db/

EXPOSE 8080
EXPOSE 9000
USER 185
ENV JAVA_OPTS="-Dquarkus.http.host=0.0.0.0 -Djava.util.logging.manager=org.jboss.logmanager.LogManager -Dquarkus.config.locations=/deployments/app/application.properties"
ENV JAVA_APP_JAR="/deployments/quarkus-run.jar"
