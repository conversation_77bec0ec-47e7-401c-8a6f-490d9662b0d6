## Docker related
IMAGE_NAME         	 	 := iad-order
REGISTRY      			 := gitlab.software-inside.it:5050
REPOSITORY 				 := nyp/devops
TOKEN                    := s68omtCoPhoY5QtMyHGz
USER                     := nyp-deploy

run:
	./mvnw quarkus:dev

prepare:
	./mvnw clean package -Pnative   
	
set-version:
	mvn clean install versions:set -DnewVersion=0.70.20 -f pom.xml

build:
	docker build -f Dockerfile.jvm -t ${REGISTRY}/${REPOSITORY}/${IMAGE_NAME}:$(shell mvn help:evaluate -Dexpression=project.version -q -DforceStdout) .

deploy:
	docker login ${REGISTRY} -u ${USER} -p ${TOKEN}
	docker push ${REGISTRY}/${REPOSITORY}/${IMAGE_NAME}:$(shell mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
	
full:
	make set-version build deploy
