# iad-order

## Description

This service is meant for mapping order entity.
Its business logic is common to all the tenants, except for some exposed routes, used for example only by YOLO, etc.;
its database is not common to all the tenants.

## Exposed Path

### YIN ORDER RESOURCE

- PUT /yin/v1/orders/{order_number}/callback_order_updated

**description**:

This endpoint allows you to update a specific order through a callback, using the url specified in the request JSON. The system requires the use of the HTTP PUT method to send the order update data.

**request**:
```
{
  "callback_on_order_updated": "https://example.com/callback"
}
```

- PUT /yin/v1/associate_user_to_order

**description**:

This endpoint accepts a PUT request to associate a user with a specified order. The association details are provided in the request body as a JSON object.

**request**:
```
{
  "order_number": "123",
  "user_id": 456,
  "ndg": "ABC123"
}
```

- PUT /yin/v1/associate_user_to_order/grpc

**description**:

This endpoint accepts a PUT request to associate a user with a specified order using gRPC technology. The association details are provided in the request body as a JSON object.

**request**:
```
{
  "order_number": "123",
  "user_id": 456,
  "ndg": "ABC123"
}
```


- PUT /yin/v1/update-status/items

**description**:

This endpoint accepts a PUT request to update the status of the order items associated to a specific order. The update details are provided in the body of the request as a JSON object.

**request**:
```
{
  "orderNumber": "123",
  "updateStatus": "SHIPPED"
}
```


### V2 ORDER RESOURCE

- POST /v2/order

**description**:

This endpoint accepts a POST request to create a new order. The details of the order to be created are provided in the body of the request as a JSON object. The response returns the newly created order entity, including any additional details such as order history and package information.

**header parameters**: Authorization token

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Draft",
        "orderItem": [
            {
                "product_id": 1,
                "packetId": 1,
                "insured_item": null,
                "instance": null,
                "quantity": 1
            }
        ],
        "productId": 1,
        "packetId": 1
    }
}
```


- GET /v2/order/{id}

**description**:

This endpoint accepts a GET request to retrieve the details of a specific order based on the provided ID. The authorization token is required to authenticate and authorize the request. The response contains the details of the requested order.

**header parameters**: Authorization token

- PUT /v2/order/{order_code}

**description**: This endpoint accepts a PUT request to update a specific order identified by the provided order code. The authorization token is required to authenticate and authorize the request. The response contains the updated order size, including any additional details such as package information.

**header parameters**: Authorization token

**request**:
```

{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Draft",
        "orderItem": [
            {
                "product_id": 1,
                "packetId": 1,
                "insured_item": null,
                "instance": null,
                "quantity": 1
            }
        ],
        "productId": 1,
        "packetId": 1
    }
}
```

- GET /v2/order

**description**: This endpoint accepts a GET request to obtain a list of all orders in the table "orders" of schema "order". The authorization token is required to authenticate and authorize the request. The response contains a list of order entities, converted into an appropriate response format.

**header parameters**: Authorization token

### V3 ORDER RESOURCE

- POST /v3/order

**description**: This endpoint accepts a POST request to create a new order. The authorization token is required to authenticate and authorize the request. The response contains the newly created order entity, including any additional details such as order history and packet information.

**header parameters**: Authorization token
**header parameters**: x-tenant-language

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Draft",
        "orderItem": [
            {
                "product_id": 1,
                "packetId": 1,
                "insured_item": null,
                "instance": null,
                "quantity": 1
            }
        ],
        "productId": 1,
        "packetId": 1
    }
}
```

- GET /v3/order/{id}

**description**: This endpoint accepts a GET request to retrieve the details of a specific order based on the provided ID. The authorization token is required to authenticate and authorize the request. The response contains details of the requested order, including product and packet information.

**header parameters**: Authorization token

- GET /v3/order/unchecked/{id}

**description**: This endpoint accepts a GET request to retrieve the details of a specific unchecked order based on the provided ID. The authorization token is required to authenticate and authorize the request. The response contains details of the requested unchecked order, including product details and packet information.

**header parameters**: Authorization token

- GET /v3/code/{orderCode}

**description**: This endpoint accepts a GET request to retrieve the details of a specific order based on the provided order code. The authorization token is required to authenticate and authorize the request. The response contains the details of the requested order, including product details and packet information.

**header parameters**: Authorization token

- GET /v3/order/unchecked/code/{orderCode}

**description**: This endpoint accepts a GET request to retrieve the details of a specific unchecked order based on the provided order code. The authorization token is required to authenticate and authorize the request. The response contains details of the requested unchecked order, including product details and packet information.

**header parameters**: Authorization token

- PUT /v3/order/{order_code}

**description**: This endpoint accepts a PUT request to update an existing order identified by the specified order code. The authorization token is required to authenticate and authorize the request. The response contains the updated order, including any additional details such as packet information.

**header parameters**: Authorization token
**header parameters**: x-tenant-language

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Elaboration",
        "orderItem": [
            {
                "product_id": 1,
                "insured_item": {
                    "name": "ABCDEF",
                    "surname": "ABCDEFGHIJKLMNOPQRSTUV",
                    "tax_code": "ABCDEFGHIJKLMNOPQRST",
                    "date_of_birth": "ABCDEFGHIJKLMNOPQRSTU"
                }
            }
        ],
        "productId": 1
    }
}
```

- PUT /v3/order/yin/{order_code}

**description**: This endpoint accepts a PUT request to update an existing order identified by the specified order code, with Yin-specific logic. The authorization token is required to authenticate and authorize the request. The response contains the updated order, including any additional details such as packet information.

**header parameters**: Authorization token
**header parameters**: x-tenant-language

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Elaboration",
        "orderItem": [
            {
                "product_id": 1,
                "insured_item": {
                    "name": "ABCDEF",
                    "surname": "ABCDEFGHIJKLMNOPQRSTUV",
                    "tax_code": "ABCDEFGHIJKLMNOPQRST",
                    "date_of_birth": "ABCDEFGHIJKLMNOPQRSTU"
                }
            }
        ],
        "productId": {{productId}}
    }
}
```

- PUT /v3/order/unchecked/{order_code}

**description**: This endpoint accepts a PUT request to update an existing unchecked order identified by the specified order code. The authorization token is required to authenticate and authorize the request. The response contains the updated unchecked order, including any additional details such as packet information.

**header parameters**: Authorization token
**header parameters**: x-tenant-language

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Elaboration",
        "orderItem": [
            {
                "product_id": 1,
                "insured_item": {
                    "name": "ABCDEF",
                    "surname": "ABCDEFGHIJKLMNOPQRSTUV",
                    "tax_code": "ABCDEFGHIJKLMNOPQRST",
                    "date_of_birth": "ABCDEFGHIJKLMNOPQRSTU"
                }
            }
        ],
        "productId": {{productId}}
    }
}
```

- PUT /v3/order/quotation/{order_code}

**description**: This endpoint accepts a PUT request to update an existing quotation order identified by the specified order code. The authorization token is required to authenticate and authorize the request. The response contains the order entity with updated quotation.

**header parameters**: Authorization token

**request**:
```
{
  "data": [
    {
      "discountedTotal": 50.0
    }
  ]
}
```

- GET /v3/order

**description**: This endpoint accepts a GET request to get a list of order entities. The authorization token is required to authenticate and authorize the request. The response contains a list of order entities and related information.
the ndg is retrieved from the json web token, the customer by ndg is retrieved, and a list of orders by cutomerId is returned. 

**header parameters**: Authorization token

- POST /v3/order/emission

**description**: This endpoint accepts a POST request to update the emission of an order item associated to an order retrieved by order_code. The authorization token is required to authenticate and authorize the request. the updated order is returned in the response after the update.

**header parameters**: Authorization token

**request**:
```
{
  "data": {
    "orderCode": "ABC123",
    "id": 456,
    "emission": {}
  }
}
```

- POST /v3/order/{order_code}/positive_adjustment

**description**: This endpoint accepts a POST request to update an order with a positive price adjustment (the order is retrieved by order_code, the order item is retrieved by order_id, and the price of the order item is updated as result of the sum between the price and the price_to_adjust provided in the req JSON). The authorization token is required to authenticate and authorize the request. The response contains an empty JSON object.

**header parameters**: Authorization token

**request**:
```
{
  "price_to_adjust": 10.0
}
```

- GET /v3/estimates

**description**: This endpoint accepts a GET request to obtain a list of order entities. The authorization token is required to authenticate and authorize the request.
the ndg is retrieved by the json web token, the customer is retrieved by ndg, and a list of orders with the customer's id, the anag state "Estimate" and the created_at date between 30 days ago and today is retrieved; for each order of this list, a product by product id is retrieved and the product code is assigned to the order. finally, the order list is returned in the response.

**header parameters**: Authorization token

- PUT /v3/estimates/{order_code}

**description**: This endpoint accepts a PUT request to update an order by order_code. The authorization token is required to authenticate and authorize the request. after retrieving the order by order_code, the anag state of this order is set to "Estimate", the ndg is retrieved from json web token, the customer id is retrieved by ndg; if the ndg is equal to the tch.usr specified in the application.properties file and the customer id of the order retrieved from db is equal to the customer id of the customer retrieved from customer ms, a business logic for anonymous (not logged) users is performed; finally, the order is updated into db, and then the order is returned in the response.

**header parameters**: Authorization token
**header parameters**: x-tenant-language

**request**:
```
{
	"data": {
		"customerId": 123,
		"orderCode": "ABC123",
		"policyCode": "POL001",
		"anagStatesId": 456,
		"packetId": 789,
		"productId": 101,
		"asset": {},
		"brokerId": 102,
		"companyId": 103,
		"insuredItem": {},
		"insurancePremium": 1500.5,
		"createdBy": "user123",
		"updatedBy": "admin",
		"createdAt": "2023-12-24T12:30:00",
		"updatedAt": "2023-12-24T15:45:00",
		"packet": {
			//Packet fields...
		},
		"fieldToRecover": {},
		"orderItem": [
			{
				//OrderItemEntity fields...
			},
			{
				//OrderItemEntity fields...
			},
			//other OrderItemEntity objects...
		],
		"paymentTransactionId": 204,
		"paymentType": "Credit Card",
		"paymentToken": "token123",
		"productType": "TypeA",
		"discount": "10%",
		"anagState": "Active",
		"packetDurationDescription": "1 year",
		"chosenWarranties": {},
		"utmSource": "newUtmSource",
		"agenziaDiRiferimento": "AgencyA",
		"resPg": {},
		"anonymousUserData": {}
	}
}
```

- GET /v3/estimates/{order_code}

**description**: This endpoint accepts a GET request to obtain a certificate response. The authorization token is required to authenticate and authorize the request. the order is retrieved by order_code, and then an anonymous user logic is performed, with certificate generation from the iad-document ms; the certificate response is finally returned in the response. 

**header parameters**: Authorization token

- POST /v3/order/duplicated-order

**description**: This endpoint accepts a POST request to create a new order entity by duplicating an existing order identified by its code. The authorization token is required to authenticate and authorize the request. The response contains the details of the newly created order entity.

**header parameters**: Authorization token

**request**:
```
{
	"data": {
		"customerId": 123,
		"orderCode": "ABC123",
		"policyCode": "POL001",
		"anagStatesId": 456,
		"packetId": 789,
		"productId": 101,
		"asset": {},
		"brokerId": 102,
		"companyId": 103,
		"insuredItem": {},
		"insurancePremium": 1500.5,
		"createdBy": "user123",
		"updatedBy": "admin",
		"createdAt": "2023-12-24T12:30:00",
		"updatedAt": "2023-12-24T15:45:00",
		"packet": {
			//Packet fields...
		},
		"fieldToRecover": {},
		"orderItem": [
			{
				//OrderItemEntity fields...
			},
			{
				//OrderItemEntity fields...
			},
			//other OrderItemEntity objects...
		],
		"paymentTransactionId": 204,
		"paymentType": "Credit Card",
		"paymentToken": "token123",
		"productType": "TypeA",
		"discount": "10%",
		"anagState": "Active",
		"packetDurationDescription": "1 year",
		"chosenWarranties": {},
		"utmSource": "newUtmSource",
		"agenziaDiRiferimento": "AgencyA",
		"resPg": {},
		"anonymousUserData": {}
	}
}
```

### V1 ANAG STATES RESOURCE

- GET /v1/anagStates/{id}

**description**: This endpoint accepts a GET request to obtain information about an anag state entity identified by a specific ID. The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the anag state with the specified {id}.

**header parameters**: Authorization token

### V1 ORDER RECONCILIATION RESOURCE

- POST /v1/reconciliation

**description**: This RESTful endpoint, within the OrderReconciliationResources class, facilitates the order reconciliation process. For the tenants where reconciliation is enabled, it is not necessary to have an account to complete an order, so if an account is created, the orders associated with the email entered during registration are retrieved and associated with the newly registered customer.

**header parameters**: Authorization token

**request**:
```
{
  "data": {
    "email": "<EMAIL>",
    "customerId": 123
  }
}
```

### V1 ORDER RESOURCE

- POST /v1/order

**description**: This POST endpoint, implemented in the OrderResource class, allows the creation of a new order into DB. The newly created order is returned in the response.

**header parameters**: Authorization token

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Draft",
        "orderItem": [
            {
                "product_id": 1,
                "packetId": 1,
                "insured_item": null,
                "instance": null,
                "quantity": 1
            }
        ],
        "productId": 1,
        "packetId": 1
    }
}
```

- GET /v1/order/{id}

**description**: This endpoint accepts a GET request to retrieve the details of an order identified by the specified ID. The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the order details.

**header parameters**: Authorization token

- GET /v1/order/code/{orderCode}

**description**: This endpoint accepts a GET request to retrieve the details of an order identified by the specified order code. The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the order details.

**header parameters**: Authorization token

- PUT /v1/order/failed?orderCode=ORD456

**description**: This endpoint accepts a PUT request to update the anag state of the order with the specified orderCode to the "Failed" value. The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the updated order details.

**header parameters**: Authorization token

- PUT /v1/order/{order_code}

**description**: This endpoint accepts a PUT request to update an existing order identified by its order code. The authorization token is required to authenticate and authorize the request. The request must contain the order data to be updated in the body of the request. The response contains a JSON object representing the updated order details.

**header parameters**: Authorization token

**request**:
```
{
    "data": {
        "customerId": 1,
        "createdBy": "API",
        "updatedBy": "API",
        "anagState": "Elaboration",
        "orderItem": [
            {
                "product_id": 1,
                "insured_item": {
                    "name": "ABCDEF",
                    "surname": "ABCDEFGHIJKLMNOPQRSTUV",
                    "tax_code": "ABCDEFGHIJKLMNOPQRST",
                    "date_of_birth": "ABCDEFGHIJKLMNOPQRSTU"
                }
            }
        ],
        "productId": 1
    }
}
```

- PUT /v1/order/emission

**description**: This endpoint accepts a PUT request to update the emission of an order. The authorization token is required to authenticate and authorize the request. The request must contain the data to be updated in the request body. The response contains a JSON object representing the updated order details.

**header parameters**: Authorization token

**request**:
```
{
  "data": {
    "orderCode": "ABC123",
    "id": 456,
    "emission": {}
  }
}
```

- GET /v1/order

**description**: This endpoint accepts a GET request to obtain a list of all orders. The authorization token is required to authenticate and authorize the request. The response contains a list of JSON objects representing order details.

**header parameters**: Authorization token

- GET /v1/order/{customerId}/{orderCode}

**description**: This endpoint accepts a GET request to obtain the details of a specific order associated with a customer identified by his ID (customerId) and order code (orderCode). The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the order details.

**header parameters**: Authorization token

- GET /v1/order/details/order/{orderCode}

**description**: This endpoint accepts a GET request to obtain the details of a specific order identified by its order code (orderCode). The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the order details, including any associated packet or product details.

**header parameters**: Authorization token

- GET /v1/order/details/{orderItemId}

**description**: This endpoint accepts a GET request to obtain the details of a specific order associated to an order item identified by its order item ID (orderItemId). The authorization token is required to authenticate and authorize the request. The response contains a JSON object representing the order details associated with the specified order item.

**header parameters**: Authorization token

- PUT /v1/order/orderItem/price

**description**: This endpoint accepts a PUT request to update the prices of the specified order items. The authorization token is required to authenticate and authorize the request. The request must contain a list of OrderItemBoundaryRequest objects that represent the order items to update.

**header parameters**: Authorization token

**request**:
```
{
	"data": [
		{
			"id": 123,
			"price": "49.99",
			"product_id": 456,
			"policy_number": "POL123",
			"master_policy_number": "MASTERPOL456",
			"external_id": "EXT789",
			"state": "ACTIVE",
			"start_date": "2023-01-01T00:00:00",
			"expiration_date": "2023-12-31T23:59:59",
			"insured_item": {},
			"order": {
				//OrderEntity fields...
			},
			"quantity": 2,
			"emission": {},
			"promotion": {}
		},
		//other OrderItemBoundaryRequest objects...
	]
}
```

- PUT /v1/order/updateOrder/estimate

**description**: This endpoint accepts a PUT request to update an order, providing an orderCode (to find the order by orderCode), and an estimateId, which is added into the "instance" object contained in the order item associated to the order. The authorization token is required to authenticate and authorize the request. The request must contain a JSON object (JsonNode) that represents the information needed to update the order.

**header parameters**: Authorization token

**request**:
```
{
  "orderCode": "ABC123",
  "estimateId": "E123"
}
```

## External Communication

this microservice communicates externally with the following microservices:

- Communication Manager ms
- Document ms
- Catalog ms
- Customer ms
- Product ms
- Policy ms
- Backoffice ms
- YIN Client, for the old products of TIM Customer

## Entity

This ms handle all the order schema, with the following tables:

- anag_states (AnagStatesEntity)
- order_history (OrderHistoryEntity)
- order_item (OrderItemEntity)
- orders (OrderEntity)
- workflow (WorkflowEntity)

## Swagger URL

By clicking on this link, you can get to the swagger documentation to be able to use the microservice endpoints:

http://tenant-api.yoloassicurazioni.it/iad-order/openapi/swagger