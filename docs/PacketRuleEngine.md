# Documentazione Tecnica: PacketRuleEngine

## Introduzione

Il `PacketRuleEngine` è un componente fondamentale del sistema che implementa un motore di regole per filtrare i pacchetti assicurativi in base a criteri di business specifici. Questo documento fornisce una descrizione dettagliata del suo funzionamento, della sua architettura e delle sue funzionalità principali.

## Architettura

Il `PacketRuleEngine` è implementato come un bean con scope `ApplicationScoped`, il che significa che esiste una singola istanza condivisa all'interno dell'applicazione. Questo approccio garantisce efficienza e coerenza nell'applicazione delle regole di business.

```java
@ApplicationScoped
@Slf4j
public class PacketRuleEngine {
    // Implementazione
}
```

## Funzionalità Principali

### 1. Filtraggio dei Pacchetti

Il metodo principale `filterPackets` accetta una lista di pacchetti e un'entità ordine, e restituisce una lista filtrata di pacchetti che soddisfano le regole di business definite.

### 2. Applicazione delle Regole di Business

Il metodo `applyBusinessRules` applica le regole di business specifiche ai pacchetti, utilizzando i dati dell'ordine e dell'elemento assicurato.

### 3. Accesso Diretto ai Campi

Il motore di regole accede direttamente ai campi dell'elemento assicurato utilizzando il metodo `at()` di JsonNode, che supporta la notazione JsonPointer. Questo approccio è molto più flessibile perché non richiede di aggiungere manualmente ogni nuovo campo di filtro.

### 4. Valutazione dei Filtri

Il metodo `matchesFilter` verifica se un pacchetto soddisfa i criteri di filtro, supportando diverse logiche di inclusione ed esclusione.

### 5. Valutazione delle Condizioni

Il metodo `evaluateCondition` valuta una singola condizione di filtro, supportando vari operatori di confronto.

### 6. Valutazione dei Gruppi di Regole

I metodi `evaluateRuleGroups` e `evaluateRuleGroup` valutano gruppi di regole con operatori logici diversi.

## Formati di Configurazione Supportati

Il `PacketRuleEngine` supporta due formati principali di configurazione delle regole, offrendo flessibilità nella definizione delle logiche di filtro.

### 1. Formato Inclusione/Esclusione

```json
{
  "inclusionRules": [
    {
      "field": "tipologiaTitoloAbitazione",
      "value": "proprietario",
      "operator": "eq"
    }
  ],
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "lt"
    }
  ],
  "logicOperator": "AND"
}
```

In questo formato:
- `exclusionRules`: Se una qualsiasi di queste regole è soddisfatta, il pacchetto viene escluso
- `inclusionRules`: Regole che devono essere soddisfatte per includere il pacchetto
- `logicOperator`: Operatore logico da applicare tra le regole di inclusione (AND/OR)

### 2. Formato Gruppi di Regole

```json
{
  "ruleGroups": [
    {
      "type": "inclusion",
      "operator": "OR",
      "rules": [
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "proprietario",
          "operator": "eq"
        },
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "affittuario",
          "operator": "eq"
        }
      ]
    },
    {
      "type": "exclusion",
      "operator": "AND",
      "rules": [
        {
          "field": "company.scoreESG",
          "value": "3",
          "operator": "lt"
        },
        {
          "field": "tipologiaUsoAbitazione",
          "value": "secondaria",
          "operator": "eq"
        }
      ]
    }
  ],
  "groupLogicOperator": "AND"
}
```

Questo formato permette di definire gruppi di regole con operatori logici diversi all'interno di ciascun gruppo.

## Operatori di Confronto Supportati

Il `PacketRuleEngine` supporta i seguenti operatori di confronto:

| Operatore | Descrizione | Esempio |
|-----------|-------------|---------|
| `eq` | Uguale a | `{"field": "tipologiaTitoloAbitazione", "value": "proprietario", "operator": "eq"}` |
| `ne` | Diverso da | `{"field": "tipologiaTitoloAbitazione", "value": "affittuario", "operator": "ne"}` |
| `gt` | Maggiore di | `{"field": "company.scoreESG", "value": "3", "operator": "gt"}` |
| `lt` | Minore di | `{"field": "company.scoreESG", "value": "3", "operator": "lt"}` |
| `gte` | Maggiore o uguale a | `{"field": "company.scoreESG", "value": "3", "operator": "gte"}` |
| `lte` | Minore o uguale a | `{"field": "company.scoreESG", "value": "3", "operator": "lte"}` |
| `contains` | Contiene | `{"field": "destination", "value": "estero", "operator": "contains"}` |

Inoltre, è possibile negare il risultato di una condizione utilizzando il campo `negate`:

```json
{
  "field": "company.scoreESG",
  "value": "3",
  "operator": "lt",
  "negate": true
}
```

Questo esempio includerà il pacchetto se lo score ESG NON è minore di 3 (quindi se è maggiore o uguale a 3).

## Campi Supportati

Il `PacketRuleEngine` supporta qualsiasi campo presente nell'elemento assicurato, senza necessità di modificare il codice. È sufficiente specificare il percorso del campo nel formato JsonPointer o utilizzando la notazione a punti.

### Esempi di Campi

| Campo | Descrizione | Esempio |
|-------|-------------|---------|
| `tipologiaTitoloAbitazione` | Tipologia del titolo dell'abitazione | `"proprietario"`, `"affittuario"` |
| `tipologiaUsoAbitazione` | Tipologia dell'uso dell'abitazione | `"principale"`, `"secondaria"` |
| `tipologiaCostruttivaAbitazione` | Tipologia costruttiva dell'abitazione | `"muratura"`, `"legno"` |
| `destination` | Destinazione | `"italia"`, `"estero"` |
| `company.scoreESG` | Score ESG dell'azienda | `"1"`, `"2"`, `"3"`, `"4"`, `"5"` |

### Formati di Percorso Supportati

1. **Notazione a Punti**: `company.scoreESG`
2. **JsonPointer**: `/company/scoreESG`

Entrambi i formati vengono automaticamente gestiti dal motore di regole.

## Esempi di Configurazione

### Esempio 1: Esclusione Semplice

```json
{
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "lt"
    }
  ]
}
```
Questo escluderà il pacchetto se lo score ESG è minore di 3.

### Esempio 2: Inclusione con Condizioni Multiple (AND)

```json
{
  "inclusionRules": [
    {
      "field": "tipologiaTitoloAbitazione",
      "value": "proprietario",
      "operator": "eq"
    },
    {
      "field": "tipologiaUsoAbitazione",
      "value": "principale",
      "operator": "eq"
    }
  ],
  "logicOperator": "AND"
}
```
Questo includerà il pacchetto solo se entrambe le condizioni sono soddisfatte.

### Esempio 3: Inclusione con Condizioni Alternative (OR)

```json
{
  "inclusionRules": [
    {
      "field": "tipologiaTitoloAbitazione",
      "value": "proprietario",
      "operator": "eq"
    },
    {
      "field": "tipologiaTitoloAbitazione",
      "value": "affittuario",
      "operator": "eq"
    }
  ],
  "logicOperator": "OR"
}
```
Questo includerà il pacchetto se almeno una delle condizioni è soddisfatta.

### Esempio 4: Combinazione di Inclusione ed Esclusione

```json
{
  "inclusionRules": [
    {
      "field": "tipologiaTitoloAbitazione",
      "value": "proprietario",
      "operator": "eq"
    }
  ],
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "lt"
    }
  ]
}
```
Questo includerà il pacchetto se è per proprietari, ma lo escluderà se lo score ESG è minore di 3.

### Esempio 5: Utilizzo di Gruppi di Regole Complessi

```json
{
  "ruleGroups": [
    {
      "type": "inclusion",
      "operator": "OR",
      "rules": [
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "proprietario",
          "operator": "eq"
        },
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "affittuario",
          "operator": "eq"
        }
      ]
    },
    {
      "type": "exclusion",
      "operator": "AND",
      "rules": [
        {
          "field": "company.scoreESG",
          "value": "3",
          "operator": "lt"
        },
        {
          "field": "tipologiaUsoAbitazione",
          "value": "secondaria",
          "operator": "eq"
        }
      ]
    }
  ],
  "groupLogicOperator": "AND"
}
```
Questo includerà il pacchetto se è per proprietari O affittuari, ma lo escluderà se ENTRAMBE le condizioni sono vere: lo score ESG è minore di 3 E l'abitazione è secondaria.

## Logging

Il `PacketRuleEngine` utilizza SLF4J per il logging, fornendo informazioni dettagliate sul processo di filtraggio:

- Numero di pacchetti da filtrare
- Numero di pacchetti filtrati
- Pacchetti che non soddisfano le condizioni di filtro
- Errori durante il processo di filtraggio
- Valutazione delle condizioni e dei gruppi di regole

Il livello di log `DEBUG` fornisce informazioni dettagliate sul processo di valutazione delle regole, mentre il livello `INFO` fornisce informazioni di alto livello sul processo di filtraggio.

## Integrazione con il Sistema

Il `PacketRuleEngine` è integrato con il sistema attraverso il metodo `checkPacketsFromOrderItem` in `OrderService`, che:

1. Ottiene i pacchetti per un determinato ID prodotto
2. Filtra i pacchetti utilizzando il `PacketRuleEngine`
3. Aggiorna il nodo `instance.filterPackets` dell'elemento ordine con i pacchetti filtrati

## Estensibilità

Il `PacketRuleEngine` è progettato per essere facilmente estensibile:

- È possibile accedere a qualsiasi campo dell'elemento assicurato senza modificare il codice
- È possibile aggiungere nuovi operatori di confronto modificando il metodo `evaluateCondition`
- È possibile aggiungere nuovi formati di configurazione modificando il metodo `matchesFilter`

## Considerazioni sulle Prestazioni

Per ottimizzare le prestazioni del `PacketRuleEngine`:

- Le regole di esclusione vengono valutate per prime, in modo da escludere rapidamente i pacchetti che non soddisfano i criteri
- Il motore utilizza JsonPointer per accedere direttamente ai campi, evitando la necessità di costruire una mappa di filtri
- Il logging dettagliato può essere disabilitato in produzione per migliorare le prestazioni

## Casi d'Uso Reali

Questa sezione illustra alcuni casi d'uso reali del `PacketRuleEngine` per aiutare a comprendere meglio come utilizzarlo in scenari pratici.

### Caso d'Uso 1: Filtraggio per Tipologia di Abitazione

In questo scenario, abbiamo due pacchetti con regole diverse per la tipologia di abitazione:

**Pacchetto 1:**
```json
{
  "ruleGroups": [
    {
      "type": "inclusion",
      "operator": "AND",
      "rules": [
        {
          "field": "tipologiaUsoAbitazione",
          "value": "S",
          "operator": "eq"
        },
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "C",
          "operator": "eq"
        }
      ]
    },
    {
      "type": "inclusion",
      "operator": "OR",
      "rules": [
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "A",
          "operator": "eq"
        },
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "VSCU",
          "operator": "eq"
        },
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "VCS",
          "operator": "eq"
        }
      ]
    }
  ],
  "groupLogicOperator": "AND"
}
```

**Pacchetto 2:**
```json
{
  "ruleGroups": [
    {
      "type": "inclusion",
      "operator": "AND",
      "rules": [
        {
          "field": "tipologiaUsoAbitazione",
          "value": "S",
          "operator": "eq"
        },
        {
          "field": "tipologiaTitoloAbitazione",
          "value": "PL",
          "operator": "eq"
        }
      ]
    },
    {
      "type": "inclusion",
      "operator": "OR",
      "rules": [
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "A",
          "operator": "eq"
        },
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "VSCU",
          "operator": "eq"
        },
        {
          "field": "tipologiaCostruttivaAbitazione",
          "value": "VCS",
          "operator": "eq"
        }
      ]
    }
  ],
  "groupLogicOperator": "AND"
}
```

In questo caso:
- Il Pacchetto 1 è destinato a abitazioni con uso "S", titolo "C" e tipologia costruttiva "A", "VSCU" o "VCS"
- Il Pacchetto 2 è destinato a abitazioni con uso "S", titolo "PL" e tipologia costruttiva "A", "VSCU" o "VCS"

Quando l'elemento assicurato ha i valori `{"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "C", "tipologiaCostruttivaAbitazione": "A"}`, solo il Pacchetto 1 viene incluso.

### Caso d'Uso 2: Esclusione basata su Score ESG

In questo scenario, abbiamo un pacchetto che viene escluso se lo score ESG dell'azienda è inferiore a 3:

**Pacchetto con Esclusione:**
```json
{
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "lt"
    }
  ]
}
```

Comportamento atteso:
- Se `company.scoreESG` è maggiore o uguale a 3, il pacchetto viene incluso
- Se `company.scoreESG` è minore di 3, il pacchetto viene escluso
- Se `company.scoreESG` è null o mancante, il pacchetto viene incluso (il motore gestisce i valori null in modo sicuro)

Questo caso d'uso è particolarmente utile per escludere pacchetti per aziende con score ESG bassi, implementando così politiche di sostenibilità.

## Gestione dei Valori Null

Il `PacketRuleEngine` gestisce in modo sicuro i valori null o mancanti:

1. **Campo Null o Mancante**: Se un campo specificato in una regola è null o mancante nell'elemento assicurato, la regola non viene applicata
2. **Oggetto Annidato Mancante**: Se un oggetto annidato (es. `company` in `company.scoreESG`) è mancante, la regola non viene applicata
3. **Elemento Assicurato Null**: Se l'intero elemento assicurato è null, tutte le regole vengono ignorate e tutti i pacchetti vengono inclusi

Questo comportamento garantisce che il motore di regole sia robusto e non generi errori in presenza di dati incompleti.

## Conclusione

Il `PacketRuleEngine` è un componente potente e flessibile che consente di filtrare i pacchetti assicurativi in base a criteri di business specifici. La sua architettura modulare e il suo design estensibile lo rendono facilmente adattabile a nuove esigenze di business. I casi d'uso reali illustrati in questa documentazione mostrano come il motore possa essere utilizzato per implementare logiche di business complesse in modo chiaro e manutenibile.
