# WarrantyRuleEngine - Motore di Regole per Garanzie

## Panoramica

Il `WarrantyRuleEngine` è un motore di regole completamente data-driven che permette di configurare dinamicamente le regole di business per le garanzie assicurative. Il motore supporta logiche complesse di inclusione, esclusione e calcolo della durata massima senza richiedere modifiche al codice.

## Caratteristiche Principali

- **Completamente Data-Driven**: Tutte le regole sono definite in formato JSON
- **Supporto per Regole Condizionali**: Logiche if/then annidate e complesse
- **Calcolo Dinamico della Durata**: Formule matematiche e regole condizionali per la durata massima
- **Gruppi di Regole**: Organizzazione delle regole con operatori logici (AND/OR)
- **Tipi di Regole Multiple**: Inclusione, esclusione e calcolo durata massima
- **Accesso a Campi Annidati**: Supporto per campi come `customer.age`, `orderItem.productType`

## Struttura delle Regole

### Schema Base della Garanzia

```json
{
  "id": "GARANZIA_ID",
  "name": "Nome Garanzia",
  "warrantyType": "TIPO_GARANZIA",
  "rule": {
    "ruleLogicOperator": "AND|OR",
    "warrantyRuleGroups": [
      {
        "type": "inclusion|exclusion|maxDuration",
        "logicOperator": "AND|OR",
        "rules": [
          // Regole specifiche
        ]
      }
    ]
  }
}
```

### Tipi di Gruppi di Regole

#### 1. Regole di Inclusione (`inclusion`)
Definiscono quando una garanzia è applicabile:

```json
{
  "type": "inclusion",
  "logicOperator": "AND",
  "rules": [
    {
      "field": "customer.age",
      "operator": "gte",
      "value": "18"
    },
    {
      "field": "customer.age",
      "operator": "lt",
      "value": "65"
    }
  ]
}
```

#### 2. Regole di Esclusione (`exclusion`)
Definiscono quando una garanzia deve essere esclusa:

```json
{
  "type": "exclusion",
  "logicOperator": "OR",
  "rules": [
    {
      "field": "customer.healthConditions",
      "operator": "contains",
      "value": "diabetes"
    },
    {
      "field": "orderItem.riskCategory",
      "operator": "eq",
      "value": "HIGH_RISK"
    }
  ]
}
```

#### 3. Regole di Durata Massima (`maxDuration`)
Definiscono la durata massima della polizza:

```json
{
  "type": "maxDuration",
  "logicOperator": "AND",
  "rules": [
    {
      "maxDuration": 10,
      "condition": {
        "field": "customer.age",
        "operator": "lte",
        "value": "55"
      }
    }
  ]
}
```

## Operatori Supportati

- `eq`: Uguale
- `ne`: Diverso
- `gt`: Maggiore di
- `lt`: Minore di
- `gte`: Maggiore o uguale
- `lte`: Minore o uguale
- `contains`: Contiene (per stringhe e array)

## Regole di Durata Avanzate

### Formula Matematica
```json
{
  "durationFormula": "65-customer.age"
}
```

### Regole Condizionali
```json
{
  "conditions": [
    {
      "if": {
        "field": "customer.age",
        "operator": "lte",
        "value": "55"
      },
      "then": {
        "maxDuration": 10
      }
    },
    {
      "if": {
        "field": "customer.age",
        "operator": "gt",
        "value": "55"
      },
      "then": {
        "durationFormula": "65-customer.age"
      }
    }
  ]
}
```

### Condizioni Annidate
```json
{
  "conditions": [
    {
      "if": {
        "field": "customer.age",
        "operator": "lt",
        "value": "65"
      },
      "then": {
        "conditions": [
          {
            "if": {
              "field": "customer.age",
              "operator": "lte",
              "value": "55"
            },
            "then": {
              "maxDuration": 10
            }
          },
          {
            "if": {
              "field": "customer.age",
              "operator": "gt",
              "value": "55"
            },
            "then": {
              "durationFormula": "65-customer.age"
            }
          }
        ]
      }
    }
  ]
}
```

## Configurazione delle Garanzie Reali

### 1. TTD (Temporary Total Disability)

**Regole di Business:**
- Età minima: 18 anni
- Età massima: 64 anni
- Durata massima: 10 anni per età ≤ 55, altrimenti 65-età

```json
{
  "id": "TTD_STANDARD",
  "name": "TTD - Incapacità Temporanea Totale",
  "warrantyType": "TTD",
  "rule": {
    "ruleLogicOperator": "AND",
    "warrantyRuleGroups": [
      {
        "type": "inclusion",
        "logicOperator": "AND",
        "rules": [
          {
            "field": "customer.age",
            "operator": "gte",
            "value": "18"
          },
          {
            "field": "customer.age",
            "operator": "lt",
            "value": "65"
          }
        ]
      },
      {
        "type": "maxDuration",
        "logicOperator": "AND",
        "rules": [
          {
            "conditions": [
              {
                "if": {
                  "field": "customer.age",
                  "operator": "lte",
                  "value": "55"
                },
                "then": {
                  "maxDuration": 10
                }
              },
              {
                "if": {
                  "field": "customer.age",
                  "operator": "gt",
                  "value": "55"
                },
                "then": {
                  "durationFormula": "65-customer.age"
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 2. ILOE (Involuntary Loss of Employment)

**Regole di Business:**
- Età minima: 18 anni
- Età massima: 64 anni
- Deve essere dipendente a tempo indeterminato
- Durata massima: 10 anni per età ≤ 55, altrimenti 65-età

```json
{
  "id": "ILOE_STANDARD",
  "name": "ILOE - Perdita Involontaria del Lavoro",
  "warrantyType": "ILOE",
  "rule": {
    "ruleLogicOperator": "AND",
    "warrantyRuleGroups": [
      {
        "type": "inclusion",
        "logicOperator": "AND",
        "rules": [
          {
            "field": "customer.age",
            "operator": "gte",
            "value": "18"
          },
          {
            "field": "customer.age",
            "operator": "lt",
            "value": "65"
          },
          {
            "field": "customer.employmentType",
            "operator": "eq",
            "value": "PERMANENT_EMPLOYEE"
          }
        ]
      },
      {
        "type": "exclusion",
        "logicOperator": "OR",
        "rules": [
          {
            "field": "customer.employmentType",
            "operator": "eq",
            "value": "FREELANCER"
          },
          {
            "field": "customer.employmentType",
            "operator": "eq",
            "value": "TEMPORARY"
          }
        ]
      },
      {
        "type": "maxDuration",
        "logicOperator": "AND",
        "rules": [
          {
            "conditions": [
              {
                "if": {
                  "field": "customer.age",
                  "operator": "lte",
                  "value": "55"
                },
                "then": {
                  "maxDuration": 10
                }
              },
              {
                "if": {
                  "field": "customer.age",
                  "operator": "gt",
                  "value": "55"
                },
                "then": {
                  "durationFormula": "65-customer.age"
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 3. HOSP (Hospitalization)

**Regole di Business:**
- Età minima: 18 anni
- Età massima: 79 anni
- Per età < 65: applica regole TTD/ILOE
- Per età ≥ 65: durata = 80-età

```json
{
  "id": "HOSP_STANDARD",
  "name": "HOSP - Ospedalizzazione",
  "warrantyType": "HOSP",
  "rule": {
    "ruleLogicOperator": "AND",
    "warrantyRuleGroups": [
      {
        "type": "inclusion",
        "logicOperator": "AND",
        "rules": [
          {
            "field": "customer.age",
            "operator": "gte",
            "value": "18"
          },
          {
            "field": "customer.age",
            "operator": "lt",
            "value": "80"
          }
        ]
      },
      {
        "type": "maxDuration",
        "logicOperator": "AND",
        "rules": [
          {
            "conditions": [
              {
                "if": {
                  "field": "customer.age",
                  "operator": "lt",
                  "value": "65"
                },
                "then": {
                  "conditions": [
                    {
                      "if": {
                        "field": "customer.age",
                        "operator": "lte",
                        "value": "55"
                      },
                      "then": {
                        "maxDuration": 10
                      }
                    },
                    {
                      "if": {
                        "field": "customer.age",
                        "operator": "gt",
                        "value": "55"
                      },
                      "then": {
                        "durationFormula": "65-customer.age"
                      }
                    }
                  ]
                }
              },
              {
                "if": {
                  "field": "customer.age",
                  "operator": "gte",
                  "value": "65"
                },
                "then": {
                  "durationFormula": "80-customer.age"
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 4. LCE (Life Changing Event)

**Regole di Business:**
- Età minima: 18 anni
- Età massima: 64 anni
- Condizioni di salute specifiche
- Durata fissa: 5 anni

```json
{
  "id": "LCE_STANDARD",
  "name": "LCE - Evento che Cambia la Vita",
  "warrantyType": "LCE",
  "rule": {
    "ruleLogicOperator": "AND",
    "warrantyRuleGroups": [
      {
        "type": "inclusion",
        "logicOperator": "AND",
        "rules": [
          {
            "field": "customer.age",
            "operator": "gte",
            "value": "18"
          },
          {
            "field": "customer.age",
            "operator": "lt",
            "value": "65"
          },
          {
            "field": "customer.healthProfile",
            "operator": "eq",
            "value": "STANDARD"
          }
        ]
      },
      {
        "type": "exclusion",
        "logicOperator": "OR",
        "rules": [
          {
            "field": "customer.healthConditions",
            "operator": "contains",
            "value": "chronic_disease"
          },
          {
            "field": "customer.healthProfile",
            "operator": "eq",
            "value": "HIGH_RISK"
          }
        ]
      },
      {
        "type": "maxDuration",
        "logicOperator": "AND",
        "rules": [
          {
            "maxDuration": 5
          }
        ]
      }
    ]
  }
}
```

## Esempi di Utilizzo

### Filtraggio delle Garanzie

```java
// Crea un order item con i dati del cliente
OrderItemEntity orderItem = new OrderItemEntity();
orderItem.setCustomerAge(30);
orderItem.setCustomerEmploymentType("PERMANENT_EMPLOYEE");

// Lista delle garanzie disponibili
List<JsonNode> availableWarranties = loadWarrantiesFromConfig();

// Filtra le garanzie applicabili e calcola le durate massime
WarrantyRuleEngine.WarrantyFilterResult result = 
    warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, availableWarranties, token);

// Ottieni le garanzie filtrate
List<JsonNode> applicableWarranties = result.getFilteredWarranties();

// Ottieni le durate massime per ciascuna garanzia
Map<String, Integer> maxDurations = result.getMaxDurations();
```

### Aggiungere una Nuova Garanzia

1. **Definire le regole di business** per la nuova garanzia
2. **Creare la configurazione JSON** seguendo il schema documentato
3. **Aggiungere la configurazione** al sistema (database, file di configurazione, etc.)
4. **Testare** con diversi scenari di clienti

### Test delle Regole

È consigliabile creare test automatizzati per ogni garanzia:

```java
@Test
public void testTTD_YoungCustomer() {
    OrderItemEntity orderItem = createOrderItemWithCustomer(25, "PERMANENT_EMPLOYEE");
    
    List<JsonNode> warranties = List.of(createTTDWarranty());
    WarrantyRuleEngine.WarrantyFilterResult result = 
        warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
    
    assertEquals(1, result.getFilteredWarranties().size());
    assertEquals(Integer.valueOf(10), result.getMaxDurations().get("TTD_STANDARD"));
}
```

## Metodi di Utilizzo del WarrantyRuleEngine

Il `WarrantyRuleEngine` offre diversi metodi per filtrare le garanzie in base alle esigenze:

### 1. `filterWarranties()` - Metodo Legacy con Enhancement
Metodo ottimizzato per l'uso in API public:
- Filtra le garanzie in base alle regole di business
- **Aggiunge automaticamente il campo `maxDuration`** a ogni garanzia filtrata
- **Rimuove il campo `rule`** dalle garanzie nella response (per sicurezza)

```java
List<JsonNode> warranties = warrantyRuleEngine.filterWarranties(orderItem, allWarranties, token);
// Ogni warranty nella lista avrà:
// - Tutti i campi originali (id, name, warrantyType, ecc.)
// - Campo aggiuntivo "maxDuration" con la durata calcolata
// - Campo "rule" rimosso dalla response
```

### 2. `filterWarrantiesWithDurations()` - Metodo con Durate Separate
Metodo per ottenere garanzie filtrate e durate in strutture separate:
- Mantiene le garanzie originali (con le regole)
- Fornisce le durate massime in una mappa separata

```java
WarrantyFilterResult result = warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, allWarranties, token);
List<JsonNode> filteredWarranties = result.getFilteredWarranties(); // Con le regole originali
Map<String, Integer> maxDurations = result.getMaxDurations(); // Durate separate
```

### 3. `filterWarrantiesWithDurations(boolean removeRulesFromResponse)` - Metodo Configurabile
Versione avanzata che permette di controllare il comportamento:

```java
// Per uso interno (mantiene le regole)
WarrantyFilterResult result = warrantyRuleEngine.filterWarrantiesWithDurations(
    orderItem, allWarranties, token, false);

// Per API esterne (rimuove le regole e aggiunge maxDuration)
WarrantyFilterResult result = warrantyRuleEngine.filterWarrantiesWithDurations(
    orderItem, allWarranties, token, true);
```
