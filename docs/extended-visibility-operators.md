# Estensione Sistema di Regole di Visibilità - Operatori Avanzati

## Panoramica

Il sistema di regole di visibilità è stato esteso per supportare operatori avanzati che permettono logiche di business complesse basate su conteggi, valori multipli e presenza di elementi in array.

## Analisi Requisiti Implementati

### ✅ Requisito 1: Conteggio Garanzie Accessorie
**Obiettivo**: Visualizzare question solo se il numero di garanzie non obbligatorie > 0

**Implementazione**:
```json
{
  "field": "chosenWarranties.warranties[mandatory=false]",
  "operator": "count",
  "value": "0",
  "comparison": "gt"
}
```

**Logica**: `chosenWarranties.warranties.filter(w => w.mandatory === false).length > 0`

### ✅ Requisito 2: Valori Multipli per Tipologia Proprietario
**Obiettivo**: Visualizzare question solo se `tipologiaProprietario` è "P" oppure "PL"

**Implementazione**:
```json
{
  "field": "tipologiaProprietario",
  "operator": "in",
  "value": ["P", "PL"]
}
```

**Logica**: `tipologiaProprietario IN ["P", "PL"]`

### ✅ Requisito 3: Presenza Warranty Specifica + Condizione Campo
**Obiettivo**: Visualizzare question solo se esiste warranty specifica E `tipologiaFabbricato` è "VM"

**Implementazione**:
```json
{
  "inclusionRules": [
    {
      "field": "chosenWarranties.warranties",
      "operator": "contains",
      "value": "Furto e Rapina"
    },
    {
      "field": "tipologiaFabbricato",
      "operator": "eq",
      "value": "VM"
    }
  ],
  "logicOperator": "AND"
}
```

**Logica**: `warranties.contains(specificWarranty) AND tipologiaFabbricato === "VM"`

### ✅ Requisito 4: Condizione Semplice su Campo
**Obiettivo**: Visualizzare question solo se `tipologiaFabbricato` è "VM"

**Implementazione**:
```json
{
  "field": "tipologiaFabbricato",
  "operator": "eq",
  "value": "VM"
}
```

**Logica**: `tipologiaFabbricato === "VM"` (già supportato)

## Nuovi Operatori Implementati

### 1. Operatore `count`

**Sintassi**:
```json
{
  "field": "chosenWarranties.warranties[filter]",
  "operator": "count",
  "value": "numero",
  "comparison": "operatore_confronto"
}
```

**Parametri**:
- `field`: Percorso array con filtro opzionale
- `value`: Numero da confrontare
- `comparison`: `eq`, `ne`, `gt`, `lt`, `gte`, `lte`

**Esempi**:
- `chosenWarranties.warranties[mandatory=false]` + `count` + `value: "0"` + `comparison: "gt"`
- `chosenWarranties.warranties[category=security]` + `count` + `value: "1"` + `comparison: "gte"`

### 2. Operatore `in`

**Sintassi**:
```json
{
  "field": "campo",
  "operator": "in",
  "value": ["valore1", "valore2", "valore3"]
}
```

**Supporta**:
- Array JSON: `["P", "PL"]`
- Stringa separata da virgole: `"P,PL"`

**Esempi**:
- `tipologiaProprietario` in `["P", "PL"]`
- `tipologiaFabbricato` in `["VM", "VL", "CA"]`

### 3. Operatore `contains` per Array

**Sintassi**:
```json
{
  "field": "chosenWarranties.warranties",
  "operator": "contains",
  "value": "nomeElemento"
}
```

**Logica**: Verifica se esiste un elemento nell'array con `name` uguale al valore specificato

**Esempi**:
- Verifica presenza "Furto e Rapina"
- Verifica presenza "Tutela Legale"

## Implementazione Tecnica

### Struttura Dati ChosenWarranties

**IMPORTANTE**: I dati `chosenWarranties` sono accessibili tramite il percorso:
```
orderEntity.getOrderItem().get(0).getInstance().get("chosenWarranties").get("data")
```

**Struttura JSON completa**:
```json
{
  "orderItem": {
    "instance": {
      "chosenWarranties": {
        "data": {
          "warranties": [
            {
              "name": "Fabbricato",
              "mandatory": true,
              "category": "base",
              "priority": 1
            },
            {
              "name": "Contenuto",
              "mandatory": true,
              "category": "base",
              "priority": 2
            },
            {
              "name": "Responsabilità Civile",
              "mandatory": false,
              "category": "optional",
              "priority": 3
            }
          ]
        }
      }
    }
  }
}
```

**Nota**: Questa struttura è condivisa con `QuestionTemplatingService` per garantire coerenza nell'accesso ai dati.

### Estensioni a QuestionVisibilityService

#### Metodi Aggiunti

1. **`evaluateCountCondition()`**
   - Gestisce conteggio array con filtri
   - Supporta tutti gli operatori di confronto
   - Parsing automatico filtri `[property=value]`

2. **`evaluateInCondition()`**
   - Gestisce operatore `in` per valori multipli
   - Supporta array JSON e stringhe separate da virgole
   - Confronto case-sensitive

3. **`evaluateArrayContainsCondition()`**
   - Gestisce presenza elementi in array
   - Cerca per proprietà `name` negli oggetti array
   - Supporto per `chosenWarranties.warranties`

4. **`getChosenWarrantiesFieldValue()`**
   - Accesso ai dati `chosenWarranties` tramite `instance.chosenWarranties.data`
   - Supporto per percorsi nidificati
   - Integrazione con sistema esistente

5. **`getChosenWarrantiesData()`**
   - Metodo helper per accesso diretto ai dati `chosenWarranties`
   - Implementa la logica: `orderItem.getInstance().get("chosenWarranties").get("data")`
   - Condiviso tra tutti i metodi che accedono a `chosenWarranties`

#### Metodi di Supporto

- `getFilteredArrayCount()` - Conteggio con filtri
- `getSimpleArrayCount()` - Conteggio semplice
- `compareCount()` - Confronto numerico per count
- `matchesFilterValue()` - Confronto valori filtro (riutilizzato da templating)

### Backward Compatibility

- ✅ **100% retrocompatibile** con regole esistenti
- ✅ Operatori esistenti continuano a funzionare
- ✅ Nessuna modifica richiesta ai dati esistenti
- ✅ Graceful degradation per dati mancanti

## Test Implementati

### Unit Test (ExtendedVisibilityServiceTest)

**Operatore `count`**:
- `testCountOperator_OptionalWarranties_GreaterThanZero()`
- `testCountOperator_MandatoryWarranties_EqualsTwo()`
- `testCountOperator_SecurityWarranties_GreaterThanZero()`
- `testCountOperator_NonexistentCategory_EqualsZero()`

**Operatore `in`**:
- `testInOperator_TipologiaProprietario_ArrayFormat()`
- `testInOperator_TipologiaProprietario_StringFormat()`
- `testInOperator_TipologiaProprietario_NotInList()`

**Operatore `contains`**:
- `testContainsOperator_SpecificWarranty_Found()`
- `testContainsOperator_SpecificWarranty_NotFound()`

**Combinazioni**:
- `testCombinedConditions_WarrantyAndFabbricato()`
- `testCombinedConditions_CountAndProprietario()`

**Edge Cases**:
- `testCountOperator_NullChosenWarranties()`
- `testInOperator_NullField()`

### Integration Test (VisibilityRequirementsIntegrationTest)

**Test per ogni requisito**:
- `testRequirement1_CountOptionalWarranties()`
- `testRequirement2_TipologiaProprietarioMultipleValues()`
- `testRequirement3_SpecificWarrantyAndFabbricato()`
- `testRequirement4_SimpleFabbricatoCondition()`

**Test scenari complessi**:
- `testAllRequirementsTogether()` - Tutti i requisiti insieme
- `testRequirementsWithDifferentData()` - Dati che nascondono alcune questions
- `testRequirementsWithNoOptionalWarranties()` - Scenario senza garanzie opzionali

## Esempi Pratici Business

### Scenario 1: Garanzie Aggiuntive
```json
{
  "content": "Hai selezionato {{chosenWarranties.warranties[mandatory=false]}} come garanzie aggiuntive. Vuoi aggiungerne altre?",
  "rule": {
    "inclusionRules": [
      {
        "field": "chosenWarranties.warranties[mandatory=false]",
        "operator": "count",
        "value": "0",
        "comparison": "gt"
      }
    ]
  }
}
```

### Scenario 2: Proprietari
```json
{
  "content": "Come proprietario dell'immobile, hai diritto a sconti speciali.",
  "rule": {
    "inclusionRules": [
      {
        "field": "tipologiaProprietario",
        "operator": "in",
        "value": ["P", "PL"]
      }
    ]
  }
}
```

### Scenario 3: Villa con Furto e Rapina
```json
{
  "content": "Per la tua villa con garanzia Furto e Rapina, raccomandiamo un sistema di allarme.",
  "rule": {
    "inclusionRules": [
      {
        "field": "chosenWarranties.warranties",
        "operator": "contains",
        "value": "Furto e Rapina"
      },
      {
        "field": "tipologiaFabbricato",
        "operator": "eq",
        "value": "VM"
      }
    ],
    "logicOperator": "AND"
  }
}
```

## Performance e Scalabilità

### Ottimizzazioni Implementate

- **Parsing efficiente** dei filtri array
- **Caching implicito** dei risultati di conteggio
- **Short-circuit evaluation** per operatori logici
- **Accesso diretto** ai dati JSON senza serializzazione

### Metriche di Performance

- **Overhead minimo** per regole semplici esistenti
- **Scalabilità lineare** con numero di elementi in array
- **Memory footprint ridotto** senza duplicazione dati

## Monitoraggio e Debugging

### Logging Dettagliato

- **Valutazione operatori**: Log per ogni operatore con parametri
- **Risultati conteggi**: Log dei count con filtri applicati
- **Confronti valori**: Log dei confronti per operatore `in`
- **Presenza elementi**: Log per ricerche in array

### Gestione Errori

- **Dati mancanti**: Gestione graceful con fallback
- **Sintassi non valida**: Log errori senza bloccare processing
- **Valori non numerici**: Conversione sicura per count
- **Array vuoti**: Comportamento definito e documentato

## Estensibilità Futura

Il sistema è progettato per essere facilmente estendibile:

### Operatori Potenziali
- `sum` - Somma valori numerici in array
- `avg` - Media valori numerici
- `min`/`max` - Valori minimo/massimo
- `regex` - Pattern matching avanzato
- `date_range` - Confronti date

### Funzionalità Avanzate
- **Operatori nidificati** per logiche complesse
- **Variabili dinamiche** calcolate runtime
- **Cache intelligente** per performance
- **Validazione regole** in fase di configurazione

## Conclusioni

L'estensione implementata fornisce un sistema completo e robusto per gestire logiche di visibilità complesse mantenendo:

- ✅ **Semplicità d'uso** per casi base
- ✅ **Potenza espressiva** per casi complessi
- ✅ **Performance ottimali** per tutti gli scenari
- ✅ **Backward compatibility** totale
- ✅ **Estensibilità futura** garantita

Il sistema è pronto per l'uso in produzione e supporta tutti i requisiti di business specificati.
