{"warranties": [{"id": "TTD_STANDARD", "name": "TTD - Inabilità Temporanea Totale", "warrantyType": "TTD", "description": "Garanzia per inabilità temporanea totale al lavoro (Data di Nascita > 18 anni e < 65 anni compiuti)", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gt", "value": "18"}, {"field": "customer.age", "operator": "lt", "value": "65"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "55"}, "then": {"maxDuration": 10}}, {"if": {"field": "customer.age", "operator": "gt", "value": "55"}, "then": {"durationFormula": "65-customer.age"}}]}]}]}}, {"id": "ILOE_STANDARD", "name": "ILOE - Perdita Involontaria del Lavoro", "warrantyType": "ILOE", "description": "Garanzia per perdita involontaria del lavoro (DDN > 18 anni e < 65 anni, attività lavorativa = S, periodo preavviso = N, pensione = N)", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gt", "value": "18"}, {"field": "customer.age", "operator": "lt", "value": "65"}, {"field": "customer.hasWorkActivity", "operator": "eq", "value": "S"}, {"field": "customer.isInNoticePeriod", "operator": "eq", "value": "N"}, {"field": "customer.isRetired", "operator": "eq", "value": "N"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "55"}, "then": {"maxDuration": 10}}, {"if": {"field": "customer.age", "operator": "gt", "value": "55"}, "then": {"durationFormula": "65-customer.age"}}]}]}]}}, {"id": "HOSP_STANDARD", "name": "HOSP - Ospedalizzazione", "warrantyType": "HOSP", "description": "Garanzia per ricovero ospedaliero (DDN > 18 anni e < 80 anni compiuti)", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gt", "value": "18"}, {"field": "customer.age", "operator": "lt", "value": "80"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "customer.age", "operator": "lt", "value": "65"}, "then": {"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "55"}, "then": {"maxDuration": 10}}, {"if": {"field": "customer.age", "operator": "gt", "value": "55"}, "then": {"durationFormula": "65-customer.age"}}]}}, {"if": {"field": "customer.age", "operator": "gte", "value": "65"}, "then": {"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "70"}, "then": {"maxDuration": 10}}, {"if": {"field": "customer.age", "operator": "gt", "value": "70"}, "then": {"durationFormula": "80-customer.age"}}]}}]}]}]}}, {"id": "LCE_STANDARD", "name": "LCE - Evento che Cambia la Vita", "warrantyType": "LCE", "description": "Garanzia per eventi critici che cambiano la vita (DDN > 18 anni e < 65 anni, coniuge <= 80 anni OR figlio <= 21 anni)", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gt", "value": "18"}, {"field": "customer.age", "operator": "lt", "value": "65"}, {"logicOperator": "OR", "rules": [{"field": "customer.spouseAge", "operator": "lte", "value": "80"}, {"field": "customer.hasChildUnder21", "operator": "eq", "value": "S"}]}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "55"}, "then": {"maxDuration": 10}}, {"if": {"field": "customer.age", "operator": "gt", "value": "55"}, "then": {"durationFormula": "65-customer.age"}}]}]}]}}, {"id": "TTD_PREMIUM", "name": "TTD Premium - Incapacità Temporanea Totale", "warrantyType": "TTD", "description": "Versione premium della garanzia TTD con copertura estesa", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gte", "value": "18"}, {"field": "customer.age", "operator": "lt", "value": "70"}, {"field": "orderItem.productType", "operator": "eq", "value": "PREMIUM"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "customer.age", "operator": "lte", "value": "60"}, "then": {"maxDuration": 15}}, {"if": {"field": "customer.age", "operator": "gt", "value": "60"}, "then": {"durationFormula": "75-customer.age"}}]}]}]}}, {"id": "HOSP_SENIOR", "name": "HOSP Senior - Ospedalizzazione per Over 65", "warrantyType": "HOSP", "description": "Garanzia ospedalizzazione specializzata per clienti over 65", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gte", "value": "65"}, {"field": "customer.age", "operator": "lt", "value": "85"}]}, {"type": "exclusion", "logicOperator": "OR", "rules": [{"field": "customer.healthConditions", "operator": "contains", "value": "severe_disability"}, {"field": "customer.healthProfile", "operator": "eq", "value": "UNINSURABLE"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"durationFormula": "85-customer.age"}]}]}}, {"id": "FAMILY_PROTECTION", "name": "Protezione Famiglia", "warrantyType": "FAMILY", "description": "Garanzia famiglia con copertura multipla", "rule": {"ruleLogicOperator": "AND", "warrantyRuleGroups": [{"type": "inclusion", "logicOperator": "AND", "rules": [{"field": "customer.age", "operator": "gte", "value": "25"}, {"field": "customer.age", "operator": "lte", "value": "55"}, {"field": "customer.familyStatus", "operator": "eq", "value": "MARRIED_WITH_CHILDREN"}, {"field": "orderItem.familyMembers", "operator": "gte", "value": "2"}]}, {"type": "maxDuration", "logicOperator": "AND", "rules": [{"conditions": [{"if": {"field": "orderItem.familyMembers", "operator": "eq", "value": "2"}, "then": {"maxDuration": 15}}, {"if": {"field": "orderItem.familyMembers", "operator": "gte", "value": "3"}, "then": {"maxDuration": 20}}]}]}]}}], "metadata": {"version": "1.0", "lastUpdated": "2025-06-17", "description": "Configurazioni di esempio per il WarrantyRuleEngine", "supportedOperators": ["eq", "ne", "gt", "lt", "gte", "lte", "contains"], "supportedGroupTypes": ["inclusion", "exclusion", "maxDuration"], "supportedLogicOperators": ["AND", "OR"]}}