apiVersion: apps/v1
kind: Deployment
metadata:
  name: PROJECT_NAME
  namespace: yolo-service
spec:
  selector:
    matchLabels:
      app: PROJECT_NAME
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      name: PROJECT_NAME
      labels:
        app: PROJECT_NAME
    spec:
      nodeSelector:
        tenant: generic1
      automountServiceAccountToken: false
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - PROJECT_NAME
                topologyKey: kubernetes.io/hostname
      containers:
      - name: PROJECT_NAME
        image: KUBERNETES_IMAGE:IMAGE_TAG
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /q/health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /q/health/ready
            port: http
          initialDelaySeconds: 25
          periodSeconds: 20
          timeoutSeconds: 15
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 50m
            memory: 50Mi
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9000
          name: grpc
        volumeMounts:
          - name: PROJECT_NAME-config
            mountPath: /deployments/app/application.properties
            subPath: application.properties
          - name: private-key
            mountPath: /deployments/app/privateKey.pem
            subPath: privateKey.pem
          - name: private-key-encrypt
            mountPath: /deployments/app/privateKeyEncrypt.pem
            subPath: privateKeyEncrypt.pem
          - name: public-key-encrypt
            mountPath: /deployments/app/publicKeyEncrypt.pem
            subPath: publicKeyEncrypt.pem
          - name: public-key
            mountPath: /deployments/app/publicKey.pem
            subPath: publicKey.pem
      volumes:
        - name: PROJECT_NAME-config
          secret:
            secretName: PROJECT_NAME-config
        - name: private-key
          secret:
            secretName: private-key
        - name: private-key-encrypt
          secret:
            secretName: private-key-encrypt
        - name: public-key-encrypt
          secret:
            secretName: public-key-encrypt
        - name: public-key
          secret:
            secretName: public-key
---
apiVersion: v1
kind: Service
metadata:
  name: PROJECT_NAME
  namespace: yolo-service
  labels:
    app: PROJECT_NAME
spec:
  ports:
  - port: 8080
    targetPort: http
    name: http
  - port: 9000
    targetPort: 9000
    name: grpc
  selector:
    app: PROJECT_NAME
