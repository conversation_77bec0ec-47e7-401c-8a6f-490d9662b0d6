# TOOL
The tool to be used will be **Liquibase**.

# FOLDER CONTENT

The structure of this folder has been estabilshed by UCS and Oracle.

* The **changelog-master.xml** with includeAll tag and the CustomXMLFilter (external class needed by Liquibase for filtering XML resources)
* `releases` folder containing subfolders that must match the YYYY_MM_DD format
    * Each `YYYY_MM_DD` folder contains:
      * SQL files for changes and rollback with naming convention: 
        * **YYYMMDD_HHMM_briefDescription.sql** (applying scripts)
        * **YYYMMDD_HHMM_briefDescription-rollback.sql** (rollback)
      * **changelog.xml** describing which files have to be applied for the release and the respective rollback ones 

