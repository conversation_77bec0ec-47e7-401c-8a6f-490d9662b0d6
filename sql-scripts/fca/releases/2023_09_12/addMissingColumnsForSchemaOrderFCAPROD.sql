ALTER TABLE "order".order_item ADD COLUMN IF NOT EXISTS quotation jsonb NULL DEFAULT '{}'::jsonb;
ALTER TABLE "order".order_item ADD COLUMN IF NOT EXISTS packet_id int4 NULL;
ALTER TABLE "order".order_item ADD COLUMN IF NOT EXISTS emission jsonb NULL DEFAULT '{}'::jsonb;
ALTER TABLE "order".order_item ADD COLUMN IF NOT EXISTS "instance" jsonb NULL;
ALTER TABLE "order".order_item ADD COLUMN IF NOT EXISTS promotion jsonb NULL DEFAULT '{}'::jsonb;

ALTER TABLE "order".orders ADD COLUMN IF NOT EXISTS payment_type varchar NULL;
ALTER TABLE "order".orders ADD COLUMN IF NOT EXISTS "language" VARCHAR NULL;
ALTER TABLE "order".orders ADD COLUMN IF NOT EXISTS agenzia_di_riferimento varchar;
ALTER TABLE "order".orders ADD COLUMN IF NOT EXISTS utm_source varchar;
ALTER TABLE "order".orders ADD COLUMN IF NOT EXISTS pid int4 NULL;