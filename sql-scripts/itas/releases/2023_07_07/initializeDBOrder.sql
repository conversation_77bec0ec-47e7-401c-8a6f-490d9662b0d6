CREATE SCHEMA IF NOT EXISTS "order";

-- "order".anag_states definition

-- Drop table

-- DROP TABLE "order".anag_states;

CREATE TABLE "order".anag_states (
	id serial4 NOT NULL,
	state varchar NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NOT NULL DEFAULT now(),
	CONSTRAINT anag_states_pkey PRIMARY KEY (id),
	CONSTRAINT uk_orders_anag_states UNIQUE (state)
);

-- "order".order_history definition

-- Drop table

-- DROP TABLE "order".order_history;

CREATE TABLE "order".order_history (
	id serial4 NOT NULL,
	order_code varchar NULL,
	step_state varchar NULL,
	order_state varchar NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL,
	CONSTRAINT order_history_pkey PRIMARY KEY (id)
);

-- "order".order_item definition

-- Drop table

-- DROP TABLE "order".order_item;

CREATE TABLE "order".order_item (
	id serial4 NOT NULL,
	price varchar NULL,
	product_id int4 NULL,
	policy_number varchar NULL,
	master_policy_number varchar NULL,
	external_id varchar NULL,
	state varchar NULL,
	start_date timestamp NULL,
	expiration_date timestamp NULL,
	insured_item jsonb NULL DEFAULT '{}'::jsonb,
	order_id int4 NULL,
	quantity int4 NULL,
	quotation jsonb NULL DEFAULT '{}'::jsonb,
	packet_id int4 NULL,
	emission jsonb NULL,
	CONSTRAINT order_item_pkey PRIMARY KEY (id)
);

-- "order".orders definition

-- Drop table

-- DROP TABLE "order".orders;

CREATE TABLE "order".orders (
	id serial4 NOT NULL,
	order_code varchar NULL,
	policy_code varchar NULL,
	anag_state_id int4 NOT NULL,
	packet_id int4 NULL,
	product_id int4 NOT NULL,
	broker_id int4 NULL,
	company_id int4 NULL,
	customer_id int4 NOT NULL,
	insurance_premium numeric(10, 4) NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NOT NULL DEFAULT now(),
	payment_transaction_id int4 NULL,
	payment_token varchar NULL,
	product_type varchar NULL,
	payment_type varchar NULL,
    field_to_recover jsonb NULL,
	CONSTRAINT orders_pkey PRIMARY KEY (id),
	CONSTRAINT uk_orders_order_code UNIQUE (order_code)
);


-- "order".orders foreign keys

ALTER TABLE "order".orders ADD CONSTRAINT orders_anag_state_id_fkey FOREIGN KEY (anag_state_id) REFERENCES "order".anag_states(id);

-- "order".workflow definition

-- Drop table

-- DROP TABLE "order".workflow;

CREATE TABLE "order".workflow (
	id serial4 NOT NULL,
	product_id int4 NULL,
	workflow varchar NULL,
	"enable" bool NULL DEFAULT true,
	CONSTRAINT workflow_pkey PRIMARY KEY (id)
);

INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(1, 'Draft', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(2, 'Deleted', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(3, 'Confirmed', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(4, 'Failed', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(5, 'Elaboration', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(6, 'Completed', 'admin', 'admin', '2023-05-24 15:59:40.826', '2023-05-24 15:59:40.826');

INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(1, 1, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(2, 2, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(4, 4, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(5, 5, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(6, 6, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(7, 7, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);
INSERT INTO "order".workflow
(id, product_id, workflow, "enable")
VALUES(3, 3, 'INSURANCE_INFO;ADDRESS;SURVEY;PAYMENT;COMPLETE', true);

COMMIT;
