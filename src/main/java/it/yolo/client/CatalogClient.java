package it.yolo.client;

import it.yolo.client.response.client.catalog.CatalogResponse;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;

@Path("/v1/catalogs")
@RegisterRestClient(configKey="catalog")
public interface CatalogClient {


    @GET
    @Path("{product_code}")
    CatalogResponse findByProductCode(@PathParam("product_code")  String id);

}
