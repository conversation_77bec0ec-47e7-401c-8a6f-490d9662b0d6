package it.yolo.client;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

@Path("/v1/customers")
@RegisterRestClient(configKey = "iad-customer")
public interface CustomerClient {

    @GET
    @Path("in/ndg/{ndg}")
    Response findByNdg(@PathParam("ndg") String ndg);
    @GET
    @Path("ids/{id}")
    Response findByIdToTaxCodeAndEmailIds(@PathParam("id") Long id, @HeaderParam("Authorization") String token);

    @GET
    @Path("/taxcode/ids/{id}")
    Response findByIdToTaxCode(@PathParam("id") Long id, @HeaderParam("Authorization") String token);

    @GET
    @Path("/{id}")
    Response findById(@PathParam("id") Integer id, @HeaderParam("Authorization") String token);
}
