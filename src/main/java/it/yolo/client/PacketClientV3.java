package it.yolo.client;


import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.packet.PacketsResponse;
import it.yolo.model.TipologiaRequest;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;

@Path("/v3/packets")
@RegisterRestClient(configKey="product")
public interface PacketClientV3 {

    @GET
    @Path("{id}")
    PacketResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token);

    @GET
    @Path("{id}")
    PacketResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token, @RestHeader("x-tenant-language") String language);


    @POST
    @Path("tipolgia")
    PacketResponse findByTipologia(TipologiaRequest request, @RestHeader("Authorization") String token);

    @GET
    @Path("{id}/product")
    PacketsResponse findByProductId(@PathParam("id") Long productId, @RestHeader("Authorization") String token);

}
