package it.yolo.client;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import it.yolo.client.request.PgwRequest;
import it.yolo.client.response.PgwEstimateResponse;

import javax.json.Json;
import javax.ws.rs.BadRequestException;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey="pgw")
@Path("/v2/estimate")
public interface PgwClient {

    @POST
    @Path("/full")
    PgwEstimateResponse estimateFull(PgwRequest request);

    @ClientExceptionMapper
    static WebApplicationException toException(Response response) {
        if (response.getStatus() == 422) {
            return new BadRequestException(String.valueOf(response.readEntity(JsonNode.class)));
        }
        return null;
    }
}
