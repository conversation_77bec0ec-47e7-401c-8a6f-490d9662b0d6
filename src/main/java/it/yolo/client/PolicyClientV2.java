package it.yolo.client;

import it.yolo.model.CheckExistingReq;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v2")
@RegisterRestClient(configKey="iad-policy")
public interface PolicyClientV2 {

    @GET
    @Path("policies/order/{orderId}")
    Response readPolicyByOrderId(@RestHeader("Authorization") String token, Long orderId);

    @POST
    @Path("policies/checkExistingPolicy")
    Response checkExistingPolicy(@RestHeader("Authorization") String token, CheckExistingReq request );

}
