package it.yolo.client;

import it.yolo.client.response.client.product.ProductResponse;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.*;
import javax.ws.rs.GET;
import javax.ws.rs.Path;

@Path("/v1/products")
@RegisterRestClient(configKey="product")
public interface ProductClient {

    @GET
    @Path("{id}")
    ProductResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token);

}
