package it.yolo.client;
import it.yolo.client.response.client.product.ProductResponse;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

@Path("/v3/products")
@RegisterRestClient(configKey="product")
public interface ProductClientV3 {

    @GET
    @Path("{id}")
    ProductResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token);

    @GET
    @Path("{id}")
    ProductResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token, @RestHeader("x-tenant-language") String language );

    @GET
    @Path("{id}")
    Response findByIdJSON(@PathParam("id") Long id, @RestHeader("Authorization") String token);

}
