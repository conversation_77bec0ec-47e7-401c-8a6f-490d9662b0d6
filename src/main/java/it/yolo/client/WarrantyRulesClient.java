package it.yolo.client;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.rest.client.reactive.ClientExceptionMapper;
import it.yolo.exception.WarrantyRulesClientException;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v1")
@RegisterRestClient(configKey="warranty-rules")
public interface WarrantyRulesClient {


    @POST
    @Path("relatedWarranties/validate")
    Response valideteWarranties(JsonNode req, @RestHeader("Authorization") String token);

    @ClientExceptionMapper
    static RuntimeException toException(Response response) {
        if (response.getStatus() == 422 || response.getStatus() == 400) {
            JsonNode jsonRes=response.readEntity(JsonNode.class);
            String error=null;
            JsonNode body=null;
            if(jsonRes.has("message") && jsonRes.has("details")){
                error=jsonRes.get("message").asText();
            }
            body=jsonRes.get("error");
            return new WarrantyRulesClientException(error, null, body);
        }
        return null;
    }

}
