package it.yolo.client;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import com.fasterxml.jackson.databind.JsonNode;

import javax.ws.rs.GET;
import javax.ws.rs.*;



@Path("/api/orders/core/")
@RegisterRestClient(configKey = "align-yin")
public interface YinClient {

    @GET
    @Path("{apiKey}/align/{order_number}")
    JsonNode getAlignByOrderNumber(@PathParam("apiKey") String apiKey,@PathParam("order_number") String order_number);
}
