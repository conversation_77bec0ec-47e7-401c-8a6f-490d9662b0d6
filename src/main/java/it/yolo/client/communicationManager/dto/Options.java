package it.yolo.client.communicationManager.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Options {

    private String fromemail;
    private String fromname;
    private String replyto;
    private String toMail;
    private List<String> cc;
    private String subject;
    private String name;
    private String surname;
    private String messagebody;
    private String messaggetype;
    private String language;
    private String emailtype;
    private Object fromNumberPhone;
    private Object toNumberPhone;
    @JsonProperty("template-placeholder")
    private List<TemplatePlaceholder> templatePlaceholder=new ArrayList<>();

    public String getFromemail() {
        return fromemail;
    }

    public void setFromemail(String fromemail) {
        this.fromemail = fromemail;
    }

    public String getFromname() {
        return fromname;
    }

    public void setFromname(String fromname) {
        this.fromname = fromname;
    }

    public String getReplyto() {
        return replyto;
    }

    public void setReplyto(String replyto) {
        this.replyto = replyto;
    }

    public String getToMail() {
        return toMail;
    }

    public void setToMail(String toMail) {
        this.toMail = toMail;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getMessagebody() {
        return messagebody;
    }

    public void setMessagebody(String messagebody) {
        this.messagebody = messagebody;
    }

    public String getMessaggetype() {
        return messaggetype;
    }

    public void setMessaggetype(String messaggetype) {
        this.messaggetype = messaggetype;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getEmailtype() {
        return emailtype;
    }

    public void setEmailtype(String emailtype) {
        this.emailtype = emailtype;
    }

    public Object getFromNumberPhone() {
        return fromNumberPhone;
    }

    public void setFromNumberPhone(Object fromNumberPhone) {
        this.fromNumberPhone = fromNumberPhone;
    }

    public Object getToNumberPhone() {
        return toNumberPhone;
    }

    public void setToNumberPhone(Object toNumberPhone) {
        this.toNumberPhone = toNumberPhone;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public List<TemplatePlaceholder> getTemplatePlaceholder() {
        return templatePlaceholder;
    }

    public void setTemplatePlaceholder(List<TemplatePlaceholder> templatePlaceholder) {
        this.templatePlaceholder = templatePlaceholder;
    }
}
