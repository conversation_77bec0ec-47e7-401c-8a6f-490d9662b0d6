package it.yolo.client.communicationManager.template.dto;

import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.client.communicationManager.dto.CertificateResponseDto;
import it.yolo.entity.OrderEntity;

public class TemplateRequest {
    private OrderEntity orderResponse;
    private ProductResponse productResponse;
    private CertificateResponseDto certificateResponseDto;

    private String primary_mail;

    public OrderEntity getOrderResponse() {
        return orderResponse;
    }

    public void setOrderResponse(OrderEntity orderResponse) {
        this.orderResponse = orderResponse;
    }

    public ProductResponse getProductResponse() {
        return productResponse;
    }

    public void setProductResponse(ProductResponse productResponse) {
        this.productResponse = productResponse;
    }

    public CertificateResponseDto getCertificateResponseDto() {
        return certificateResponseDto;
    }

    public void setCertificateResponseDto(CertificateResponseDto certificateResponseDto) {
        this.certificateResponseDto = certificateResponseDto;
    }

    public String getPrimary_mail() {
        return primary_mail;
    }

    public void setPrimary_mail(String primary_mail) {
        this.primary_mail = primary_mail;
    }
}
