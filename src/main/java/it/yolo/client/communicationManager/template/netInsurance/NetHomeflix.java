package it.yolo.client.communicationManager.template.netInsurance;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;

import javax.enterprise.context.RequestScoped;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class NetHomeflix implements Template {

    private static String CONTENT_TYPE_PDF="application/pdf";

    private static String CERTIFICATO_FILE_NAME ="PREVENTIVO";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {

        TemplateResponseDto responseDto=new TemplateResponseDto();
        responseDto.getOptions().setToMail(templateRequest.getPrimary_mail());
        responseDto.getOptions().setMessaggetype("html");
        responseDto.getOptions().setLanguage(templateRequest.getOrderResponse().getLanguage());
        responseDto.getOptions().setName("");
        responseDto.getOptions().setSurname("");
        TemplatePlaceholder name = new TemplatePlaceholder();
        name.setKey("nome");
        name.setValue("");
        TemplatePlaceholder productType = new TemplatePlaceholder();
        productType.setKey("productName");
        TemplatePlaceholder frontEndUrl = new TemplatePlaceholder();
        frontEndUrl.setKey("frontEndUrl");
        frontEndUrl.setValue(templateRequest.getProductResponse().getData().getConfiguration().getProperties().get("feUrl").asText());
        productType.setValue(templateRequest.getOrderResponse().getProductType());
        responseDto.getOptions().getTemplatePlaceholder().add(name);
        responseDto.getOptions().getTemplatePlaceholder().add(productType);
        responseDto.getOptions().getTemplatePlaceholder().add(frontEndUrl);
        List<Attachments> attachmentsList=new ArrayList<>();
        attachmentsList.add(new Attachments(CERTIFICATO_FILE_NAME+"."+templateRequest.getCertificateResponseDto().getType(),
                templateRequest.getCertificateResponseDto().getType().contains("pdf") ? CONTENT_TYPE_PDF :
                        templateRequest.getCertificateResponseDto().getType(), templateRequest.getCertificateResponseDto().getFile()));

        responseDto.setAttachment(attachmentsList);
        responseDto.getMessage().setKey("PREVENTIVO");
        return responseDto;
    }
}
