package it.yolo.client.communicationManager.utils;

import it.yolo.client.communicationManager.dto.Attachments;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

public class AttachmentUtils {

    public static Attachments getAttachment(String link, String attachmentName, String contentType) throws IOException {
            URL url = new URL(link);
            InputStream in = new BufferedInputStream(url.openStream());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            in.transferTo(Base64.getEncoder().wrap(out));
            return new Attachments(attachmentName, contentType, out.toString());
    }
}
