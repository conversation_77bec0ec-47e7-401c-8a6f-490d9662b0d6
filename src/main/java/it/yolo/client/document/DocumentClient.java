package it.yolo.client.document;

import it.yolo.client.document.dto.CertificateRequestDto;
import it.yolo.client.document.dto.UploadCertificateRequestDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@RegisterRestClient(configKey = "iad-document")
public interface DocumentClient {

    @POST
    @Path("/generate")
    Response generateCertficate(CertificateRequestDto certificateRequestDto);

    @POST
    @Path("/upload")
    Response uploadCertificate(UploadCertificateRequestDto uploadCertificateRequestDto);

}
