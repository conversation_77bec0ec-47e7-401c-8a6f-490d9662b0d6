package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DataCustomerResponseDto {
    @JsonProperty("primary_phone")
    private String primaryPhone;

    @JsonProperty("primary_mail")
    private String primaryMail;

    @JsonProperty("primary_phone")
    public String getPrimaryPhone() {
        return primaryPhone;
    }

    @JsonProperty("primary_phone")
    public void setPrimaryPhone(String primaryPhone) {
        this.primaryPhone = primaryPhone;
    }

    @JsonProperty("primary_mail")
    public String getPrimaryMail() {
        return primaryMail;
    }

    @JsonProperty("primary_mail")
    public void setPrimaryMail(String primaryMail) {
        this.primaryMail = primaryMail;
    }
}
