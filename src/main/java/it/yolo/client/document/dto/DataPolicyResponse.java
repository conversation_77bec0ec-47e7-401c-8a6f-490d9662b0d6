package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

public class DataPolicyResponse {
    @JsonProperty("policyCode")
    private String policyCode;
    @JsonProperty("endDate")
    private LocalDateTime endDate;

    @JsonProperty("nextBillingDate")
    private LocalDateTime nextBillingDate;

    @JsonProperty("policyCode")
    public String getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @JsonProperty("endDate")
    public LocalDateTime getEndDate() {
        return endDate;
    }

    @JsonProperty("endDate")
    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("nextBillingDate")
    public LocalDateTime getNextBillingDate() {
        return nextBillingDate;
    }

    @JsonProperty("nextBillingDate")
    public void setNextBillingDate(LocalDateTime nextBillingDate) {
        this.nextBillingDate = nextBillingDate;
    }
}
