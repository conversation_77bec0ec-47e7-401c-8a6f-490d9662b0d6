package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.model.OrderBoundaryResponse;

import java.util.HashMap;
import java.util.Map;

public class OrderResponseDto {

    @JsonProperty("data")
    private OrderBoundaryResponse response;

    @JsonProperty("additionalInfo")
    private Object additionalInfo;

    @JsonProperty("discount")
    private String discount;

    @JsonProperty("version")
    private String version;

    @JsonProperty("fieldToRecover")
    private JsonNode fieldToRecover;

    @JsonProperty("data")
    public OrderBoundaryResponse getResponse() {
        return response;
    }

    @JsonProperty("data")
    public void setResponse(OrderBoundaryResponse response) {
        this.response = response;
    }

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    @JsonProperty("additionalInfo")
    public Object getAdditionalInfo() {
        return additionalInfo;
    }

    @JsonProperty("additionalInfo")
    public void setAdditionalInfo(Object additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @JsonProperty("version")
    public String getVersion() {
        return version;
    }

    @JsonProperty("version")
    public void setVersion(String version) {
        this.version = version;
    }

    @JsonProperty("discount")
    public String getDiscount() {
        return discount;
    }

    @JsonProperty("discount")
    public void setDiscount(String discount) {
        this.discount = discount;
    }


    @JsonProperty("fieldToRecover")
    public JsonNode getFieldToRecover() {
        return fieldToRecover;
    }
    @JsonProperty("fieldToRecover")
    public void setFieldToRecover(JsonNode fieldToRecover) {
        this.fieldToRecover = fieldToRecover;
    }
}
