package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class PolicyResponseDto implements Serializable {
    @JsonProperty("data")
    private DataPolicyResponse data;

    @JsonProperty("data")
    public DataPolicyResponse getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(DataPolicyResponse data) {
        this.data = data;
    }
}