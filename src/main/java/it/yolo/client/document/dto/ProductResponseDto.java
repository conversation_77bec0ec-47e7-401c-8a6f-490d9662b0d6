package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.response.client.product.ProductResponse;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductResponseDto {

    @JsonProperty("data")
    private ProductResponse dataProduct;

    @JsonProperty("data")
    public ProductResponse getDataProduct() {
        return dataProduct;
    }

    @JsonProperty("data")
    public void setDataProduct(ProductResponse dataProduct) {
        this.dataProduct = dataProduct;
    }
}
