package it.yolo.client.document.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UploadCertificateRequestDto {
    @NotNull
    @JsonProperty("nome_file")
    private String nomeFile;

    @NotNull
    @JsonProperty("file")
    private String file;

   
    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getNomeFile() {
        return nomeFile;
    }

    public void setNomeFile(String nomeFile) {
        this.nomeFile = nomeFile;
    }

}
