package it.yolo.client.grpc;

import be.digitech.iadtoken.grpc.Empty;
import be.digitech.iadtoken.grpc.IamGrpc;
import be.digitech.iadtoken.grpc.Token;
import io.quarkus.grpc.GrpcClient;

import javax.enterprise.context.ApplicationScoped;
import javax.ws.rs.GET;

@ApplicationScoped
public class IadTokenGrpcClient {

    @GrpcClient
    IamGrpc tokenService;

    @GET
    public Token technicalLogin(Empty empty) {
        return tokenService.technicalToken(empty).await().indefinitely();
    }

    @GET
    public Token intermediaryTechnicalLogin(Empty empty) {
        return tokenService.intermediaryTechnicalToken(empty).await().indefinitely();
    }
}
