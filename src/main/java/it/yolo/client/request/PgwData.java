package it.yolo.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;


public class PgwData {

    @JsonProperty("customer")
    private JsonNode customer;

    @JsonProperty("order")
    private JsonNode order;

    @JsonProperty("product")
    private JsonNode product;

    @JsonProperty("customer")
    public JsonNode getCustomer() {
        return customer;
    }

    @JsonProperty("customer")
    public void setCustomer(JsonNode customer) {
        this.customer = customer;
    }

    @JsonProperty("order")
    public JsonNode getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(JsonNode order) {
        this.order = order;
    }

    @JsonProperty("product")
    public JsonNode getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(JsonNode product) {
        this.product = product;
    }

    public PgwData(JsonNode customer, JsonNode order, JsonNode product) {
        this.customer = customer;
        this.order = order;
        this.product = product;
    }

    public PgwData() {
    }
}
