package it.yolo.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PgwRequest {

    @JsonProperty("data")
    private PgwData data;

    @JsonProperty("tenant")
    private String tenant;


    @JsonProperty("data")
    public PgwData getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(PgwData data) {
        this.data = data;
    }

    @JsonProperty("tenant")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("tenant")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
