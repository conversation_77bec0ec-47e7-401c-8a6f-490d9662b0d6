
package it.yolo.client.response;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.JsonNode;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "total",
    "currency",
    "estimateNumber",
    "estimateID",
    "base64pdf",
    "providerResponse"
})
public class PgwEstimateResponse {

    @JsonProperty("total")
    private String total;
    
    @JsonProperty("currency")
    private String currency;

    @NotNull
    @JsonProperty("estimateID")
    private String estimateID;

    @NotNull
    @JsonProperty("estimateNumber")
    private String estimateNumber;

    @NotNull
    @JsonProperty("base64pdf")
    private String base64pdf;

    @NotNull
    @JsonProperty("providerResponse")
    private JsonNode providerResponse;


    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getEstimateNumber() {
        return estimateNumber;
    }

    public void setEstimateNumber(String estimateNumber) {
        this.estimateNumber = estimateNumber;
    }

    public String getBase64pdf() {
        return base64pdf;
    }

    public void setBase64pdf(String base64pdf) {
        this.base64pdf = base64pdf;
    }

    public JsonNode getProviderResponse() {
        return providerResponse;
    }

    public void setProviderResponse(JsonNode providerResponse) {
        this.providerResponse = providerResponse;
    }

    public String getEstimateID() {
        return estimateID;
    }

    public void setEstimateID(String estimateID) {
        this.estimateID = estimateID;
    }
    
    

}
