
package it.yolo.client.response.client.catalog;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "productId",
    "productName"
})
public class Properties {

    @JsonProperty("productId")
    private ProductId productId;
    @JsonProperty("productName")
    private ProductName productName;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("productId")
    public ProductId getProductId() {
        return productId;
    }

    @JsonProperty("productId")
    public void setProductId(ProductId productId) {
        this.productId = productId;
    }

    @JsonProperty("productName")
    public ProductName getProductName() {
        return productName;
    }

    @JsonProperty("productName")
    public void setProductName(ProductName productName) {
        this.productName = productName;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
