package it.yolo.client.response.client.packet;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Configuration {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("emission")
    private String emission;

    @JsonProperty("emissionPrefix")
    private String emissionPrefix;

    @JsonProperty("certificate")
    private String certificate;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("emission")
    public String getEmission() {
        return emission;
    }

    @JsonProperty("emission")
    public void setEmission(String emission) {
        this.emission = emission;
    }

    @JsonProperty("emissionPrefix")
    public String getEmissionPrefix() {
        return emissionPrefix;
    }

    @JsonProperty("emissionPrefix")
    public void setEmissionPrefix(String emissionPrefix) {
        this.emissionPrefix = emissionPrefix;
    }

    @JsonProperty("certificate")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificate")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }
}
