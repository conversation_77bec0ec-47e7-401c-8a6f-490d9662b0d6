
package it.yolo.client.response.client.packet;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.client.product.Question;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "name",
        "broker",
        "product",
        "warranties",
        "sku",
        "externalCode",
        "business",
        "titleProd",
        "short_description",
        "conditions",
        "information_package",
        "conditions_package",
        "display_price",
        "price",
        "only_contractor",
        "maximum_insurable",
        "can_open_claim",
        "holder_maximum_age",
        "holder_minimum_age",
        "show_in_dashboard",
        "images",
        "packet_premium",
        "description",
        "duration",
        "duration_type",
        "fixedStartDate",
        "fixedEndDate"
})
public class Data {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("broker")
    private Broker broker;
    @JsonProperty("product")
    private Product product;
    @JsonProperty("warranties")
    private JsonNode warranties;
    @JsonProperty("sku")
    private String sku;
    @JsonProperty("externalCode")
    private Object externalCode;
    @JsonProperty("business")
    private Boolean business;
    @JsonProperty("titleProd")
    private Object titleProd;
    @JsonProperty("short_description")
    private String shortDescription;
    @JsonProperty("conditions")
    private String conditions;
    @JsonProperty("information_package")
    private String informationPackage;
    @JsonProperty("conditions_package")
    private String conditionsPackage;
    @JsonProperty("display_price")
    private String displayPrice;
    @JsonProperty("price")
    private Integer price;
    @JsonProperty("packetPremium")
    private Double packetPremium;
    @JsonProperty("description")
    private String description;
    @JsonProperty("only_contractor")
    private Boolean onlyContractor;
    @JsonProperty("maximum_insurable")
    private Integer maximumInsurable;
    @JsonProperty("can_open_claim")
    private Boolean canOpenClaim;
    @JsonProperty("holder_maximum_age")
    private Integer holderMaximumAge;
    @JsonProperty("holder_minimum_age")
    private Integer holderMinimumAge;
    @JsonProperty("show_in_dashboard")
    private Boolean showInDashboard;
    @JsonProperty("images")
    private Images images;
    @JsonProperty("duration")
    private String duration;
    @JsonProperty("duration_type")
    private String durationType;
    @JsonProperty("fixedStartDate")
    private LocalDateTime fixedStartDate;
    @JsonProperty("fixedEndDate")
    private LocalDateTime fixedEndDate;
    @JsonProperty("packet_duration")
    private JsonNode packetDuration;
    @JsonProperty("packet_condition")
    private JsonNode packetCondition;
    @JsonProperty("configuration")
    private JsonNode configuration;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();
    @JsonProperty("questions")
    private List<Question> questions;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("broker")
    public Broker getBroker() {
        return broker;
    }

    @JsonProperty("broker")
    public void setBroker(Broker broker) {
        this.broker = broker;
    }

    @JsonProperty("product")
    public Product getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(Product product) {
        this.product = product;
    }

    @JsonProperty("warranties")
    public JsonNode getWarranties() {
        return warranties;
    }

    @JsonProperty("warranties")
    public void setWarranties(JsonNode warranties) {
        this.warranties = warranties;
    }

    @JsonProperty("sku")
    public String getSku() {
        return sku;
    }

    @JsonProperty("sku")
    public void setSku(String sku) {
        this.sku = sku;
    }

    @JsonProperty("externalCode")
    public Object getExternalCode() {
        return externalCode;
    }

    @JsonProperty("externalCode")
    public void setExternalCode(Object externalCode) {
        this.externalCode = externalCode;
    }

    @JsonProperty("business")
    public Boolean getBusiness() {
        return business;
    }

    @JsonProperty("business")
    public void setBusiness(Boolean business) {
        this.business = business;
    }

    @JsonProperty("titleProd")
    public Object getTitleProd() {
        return titleProd;
    }

    @JsonProperty("titleProd")
    public void setTitleProd(Object titleProd) {
        this.titleProd = titleProd;
    }

    @JsonProperty("short_description")
    public String getShortDescription() {
        return shortDescription;
    }

    @JsonProperty("short_description")
    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    @JsonProperty("conditions")
    public String getConditions() {
        return conditions;
    }

    @JsonProperty("conditions")
    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    @JsonProperty("information_package")
    public String getInformationPackage() {
        return informationPackage;
    }

    @JsonProperty("information_package")
    public void setInformationPackage(String informationPackage) {
        this.informationPackage = informationPackage;
    }

    @JsonProperty("conditions_package")
    public String getConditionsPackage() {
        return conditionsPackage;
    }

    @JsonProperty("conditions_package")
    public void setConditionsPackage(String conditionsPackage) {
        this.conditionsPackage = conditionsPackage;
    }

    @JsonProperty("display_price")
    public String getDisplayPrice() {
        return displayPrice;
    }

    @JsonProperty("display_price")
    public void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    @JsonProperty("price")
    public Integer getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Integer price) {
        this.price = price;
    }

    @JsonProperty("only_contractor")
    public Boolean getOnlyContractor() {
        return onlyContractor;
    }

    @JsonProperty("only_contractor")
    public void setOnlyContractor(Boolean onlyContractor) {
        this.onlyContractor = onlyContractor;
    }

    @JsonProperty("maximum_insurable")
    public Integer getMaximumInsurable() {
        return maximumInsurable;
    }

    @JsonProperty("maximum_insurable")
    public void setMaximumInsurable(Integer maximumInsurable) {
        this.maximumInsurable = maximumInsurable;
    }

    @JsonProperty("can_open_claim")
    public Boolean getCanOpenClaim() {
        return canOpenClaim;
    }

    @JsonProperty("can_open_claim")
    public void setCanOpenClaim(Boolean canOpenClaim) {
        this.canOpenClaim = canOpenClaim;
    }

    @JsonProperty("holder_maximum_age")
    public Integer getHolderMaximumAge() {
        return holderMaximumAge;
    }

    @JsonProperty("holder_maximum_age")
    public void setHolderMaximumAge(Integer holderMaximumAge) {
        this.holderMaximumAge = holderMaximumAge;
    }

    @JsonProperty("holder_minimum_age")
    public Integer getHolderMinimumAge() {
        return holderMinimumAge;
    }

    @JsonProperty("holder_minimum_age")
    public void setHolderMinimumAge(Integer holderMinimumAge) {
        this.holderMinimumAge = holderMinimumAge;
    }

    @JsonProperty("show_in_dashboard")
    public Boolean getShowInDashboard() {
        return showInDashboard;
    }

    @JsonProperty("show_in_dashboard")
    public void setShowInDashboard(Boolean showInDashboard) {
        this.showInDashboard = showInDashboard;
    }

    @JsonProperty("images")
    public Images getImages() {
        return images;
    }

    @JsonProperty("images")
    public void setImages(Images images) {
        this.images = images;
    }

    public Double getPacketPremium() {
        return packetPremium;
    }

    public void setPacketPremium(Double packetPremium) {
        this.packetPremium = packetPremium;
    }

    public String getDescription() {
        return description;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    @JsonProperty("duration_type")
    public String getDurationType() {
        return durationType;
    }

    @JsonProperty("duration_type")
    public void setDurationType(final String durationType) {
        this.durationType = durationType;
    }

    public LocalDateTime getFixedStartDate() {
        return fixedStartDate;
    }

    public void setFixedStartDate(LocalDateTime fixedStartDate) {
        this.fixedStartDate = fixedStartDate;
    }

    public LocalDateTime getFixedEndDate() {
        return fixedEndDate;
    }

    public void setFixedEndDate(LocalDateTime fixedEndDate) {
        this.fixedEndDate = fixedEndDate;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("packet_duration")
    public JsonNode getPacketDuration() {
        return packetDuration;
    }

    @JsonProperty("packet_duration")
    public void setPacketDuration(JsonNode packetDuration) {
        this.packetDuration = packetDuration;
    }

    @JsonProperty("packet_condition")
    public JsonNode getPacketCondition() {
        return packetCondition;
    }
    @JsonProperty("packet_condition")
    public void setPacketCondition(JsonNode packetCondition) {
        this.packetCondition = packetCondition;
    }

    @JsonProperty("configuration")
    public JsonNode getConfiguration() {
        return configuration;
    }

    @JsonProperty("configuration")
    public void setConfiguration(JsonNode configuration) {
        this.configuration = configuration;
    }

    @JsonProperty("questions")
    public List<Question> getQuestions() {
        return questions;
    }

    @JsonProperty("questions")
    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
