
package it.yolo.client.response.client.packet;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "name",
    "externalCode",
    "asset",
    "brokerId",
    "sku",
    "business",
    "title_prod",
    "short_description",
    "conditions",
    "information_package",
    "conditions_package",
    "display_price",
    "price",
    "only_contractor",
    "maximum_insurable",
    "can_open_claim",
    "holder_maximum_age",
    "holder_minimum_age",
    "warranties",
    "images"
})
@Generated("jsonschema2pojo")
public class Packet {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("externalCode")
    private Object externalCode;
    @JsonProperty("asset")
    private Object asset;
    @JsonProperty("brokerId")
    private Integer brokerId;
    @JsonProperty("sku")
    private String sku;
    @JsonProperty("business")
    private Boolean business;
    @JsonProperty("title_prod")
    private String titleProd;
    @JsonProperty("short_description")
    private String shortDescription;
    @JsonProperty("conditions")
    private String conditions;
    @JsonProperty("information_package")
    private String informationPackage;
    @JsonProperty("conditions_package")
    private String conditionsPackage;
    @JsonProperty("display_price")
    private String displayPrice;
    @JsonProperty("price")
    private Integer price;
    @JsonProperty("only_contractor")
    private Boolean onlyContractor;
    @JsonProperty("maximum_insurable")
    private Integer maximumInsurable;
    @JsonProperty("can_open_claim")
    private Boolean canOpenClaim;
    @JsonProperty("holder_maximum_age")
    private Integer holderMaximumAge;
    @JsonProperty("holder_minimum_age")
    private Integer holderMinimumAge;
    @JsonProperty("warranties")
    private List<Warranty> warranties = null;
    @JsonProperty("images")
    private Images images;
    @JsonProperty("packetPremium")
    private String packetPremium;
    @JsonProperty("description")
    private String description;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("externalCode")
    public Object getExternalCode() {
        return externalCode;
    }

    @JsonProperty("externalCode")
    public void setExternalCode(Object externalCode) {
        this.externalCode = externalCode;
    }

    @JsonProperty("asset")
    public Object getAsset() {
        return asset;
    }

    @JsonProperty("asset")
    public void setAsset(Object asset) {
        this.asset = asset;
    }

    @JsonProperty("brokerId")
    public Integer getBrokerId() {
        return brokerId;
    }

    @JsonProperty("brokerId")
    public void setBrokerId(Integer brokerId) {
        this.brokerId = brokerId;
    }

    @JsonProperty("sku")
    public String getSku() {
        return sku;
    }

    @JsonProperty("sku")
    public void setSku(String sku) {
        this.sku = sku;
    }

    @JsonProperty("business")
    public Boolean getBusiness() {
        return business;
    }

    @JsonProperty("business")
    public void setBusiness(Boolean business) {
        this.business = business;
    }

    @JsonProperty("title_prod")
    public String getTitleProd() {
        return titleProd;
    }

    @JsonProperty("title_prod")
    public void setTitleProd(String titleProd) {
        this.titleProd = titleProd;
    }

    @JsonProperty("short_description")
    public String getShortDescription() {
        return shortDescription;
    }

    @JsonProperty("short_description")
    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    @JsonProperty("conditions")
    public String getConditions() {
        return conditions;
    }

    @JsonProperty("conditions")
    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    @JsonProperty("information_package")
    public String getInformationPackage() {
        return informationPackage;
    }

    @JsonProperty("information_package")
    public void setInformationPackage(String informationPackage) {
        this.informationPackage = informationPackage;
    }

    @JsonProperty("conditions_package")
    public String getConditionsPackage() {
        return conditionsPackage;
    }

    @JsonProperty("conditions_package")
    public void setConditionsPackage(String conditionsPackage) {
        this.conditionsPackage = conditionsPackage;
    }

    @JsonProperty("display_price")
    public String getDisplayPrice() {
        return displayPrice;
    }

    @JsonProperty("display_price")
    public void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    @JsonProperty("price")
    public Integer getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Integer price) {
        this.price = price;
    }

    @JsonProperty("only_contractor")
    public Boolean getOnlyContractor() {
        return onlyContractor;
    }

    @JsonProperty("only_contractor")
    public void setOnlyContractor(Boolean onlyContractor) {
        this.onlyContractor = onlyContractor;
    }

    @JsonProperty("maximum_insurable")
    public Integer getMaximumInsurable() {
        return maximumInsurable;
    }

    @JsonProperty("maximum_insurable")
    public void setMaximumInsurable(Integer maximumInsurable) {
        this.maximumInsurable = maximumInsurable;
    }

    @JsonProperty("can_open_claim")
    public Boolean getCanOpenClaim() {
        return canOpenClaim;
    }

    @JsonProperty("can_open_claim")
    public void setCanOpenClaim(Boolean canOpenClaim) {
        this.canOpenClaim = canOpenClaim;
    }

    @JsonProperty("holder_maximum_age")
    public Integer getHolderMaximumAge() {
        return holderMaximumAge;
    }

    @JsonProperty("holder_maximum_age")
    public void setHolderMaximumAge(Integer holderMaximumAge) {
        this.holderMaximumAge = holderMaximumAge;
    }

    @JsonProperty("holder_minimum_age")
    public Integer getHolderMinimumAge() {
        return holderMinimumAge;
    }

    @JsonProperty("holder_minimum_age")
    public void setHolderMinimumAge(Integer holderMinimumAge) {
        this.holderMinimumAge = holderMinimumAge;
    }

    @JsonProperty("warranties")
    public List<Warranty> getWarranties() {
        return warranties;
    }

    @JsonProperty("warranties")
    public void setWarranties(List<Warranty> warranties) {
        this.warranties = warranties;
    }

    @JsonProperty("images")
    public Images getImages() {
        return images;
    }

    @JsonProperty("images")
    public void setImages(Images images) {
        this.images = images;
    }

    public String getPacketPremium() {
        return packetPremium;
    }

    public void setPacketPremium(String packetPremium) {
        this.packetPremium = packetPremium;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
