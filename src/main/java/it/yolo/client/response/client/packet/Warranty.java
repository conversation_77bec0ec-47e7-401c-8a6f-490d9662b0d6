
package it.yolo.client.response.client.packet;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "packetId",
    "categoryId",
    "warrantiesId",
    "parentId",
    "insurancePremium",
    "startDate",
    "endDate",
    "recurring",
    "rule",
    "externalCode",
    "name",
    "ceilings",
    "images",
    "branch",
    "mandatory"
})
@Generated("jsonschema2pojo")
public class Warranty {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("packetId")
    private Object packetId;
    @JsonProperty("categoryId")
    private Object categoryId;
    @JsonProperty("warrantiesId")
    private Object warrantiesId;
    @JsonProperty("parentId")
    private Object parentId;
    @JsonProperty("insurancePremium")
    private Object insurancePremium;
    @JsonProperty("startDate")
    private Object startDate;
    @JsonProperty("endDate")
    private Object endDate;
    @JsonProperty("recurring")
    private Boolean recurring;
    @JsonProperty("rule")
    private Rule rule;
    @JsonProperty("externalCode")
    private Object externalCode;
    @JsonProperty("name")
    private Object name;
    @JsonProperty("ceilings")
    private Object ceilings;
    @JsonProperty("images")
    private Object images;
    @JsonProperty("branch")
    private Object branch;
    @JsonProperty("mandatory")
    private Boolean mandatory;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("packetId")
    public Object getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(Object packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("categoryId")
    public Object getCategoryId() {
        return categoryId;
    }

    @JsonProperty("categoryId")
    public void setCategoryId(Object categoryId) {
        this.categoryId = categoryId;
    }

    @JsonProperty("warrantiesId")
    public Object getWarrantiesId() {
        return warrantiesId;
    }

    @JsonProperty("warrantiesId")
    public void setWarrantiesId(Object warrantiesId) {
        this.warrantiesId = warrantiesId;
    }

    @JsonProperty("parentId")
    public Object getParentId() {
        return parentId;
    }

    @JsonProperty("parentId")
    public void setParentId(Object parentId) {
        this.parentId = parentId;
    }

    @JsonProperty("insurancePremium")
    public Object getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(Object insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    @JsonProperty("startDate")
    public Object getStartDate() {
        return startDate;
    }

    @JsonProperty("startDate")
    public void setStartDate(Object startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public Object getEndDate() {
        return endDate;
    }

    @JsonProperty("endDate")
    public void setEndDate(Object endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("recurring")
    public Boolean getRecurring() {
        return recurring;
    }

    @JsonProperty("recurring")
    public void setRecurring(Boolean recurring) {
        this.recurring = recurring;
    }

    @JsonProperty("rule")
    public Rule getRule() {
        return rule;
    }

    @JsonProperty("rule")
    public void setRule(Rule rule) {
        this.rule = rule;
    }

    @JsonProperty("externalCode")
    public Object getExternalCode() {
        return externalCode;
    }

    @JsonProperty("externalCode")
    public void setExternalCode(Object externalCode) {
        this.externalCode = externalCode;
    }

    @JsonProperty("name")
    public Object getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(Object name) {
        this.name = name;
    }

    @JsonProperty("ceilings")
    public Object getCeilings() {
        return ceilings;
    }

    @JsonProperty("ceilings")
    public void setCeilings(Object ceilings) {
        this.ceilings = ceilings;
    }

    @JsonProperty("images")
    public Object getImages() {
        return images;
    }

    @JsonProperty("images")
    public void setImages(Object images) {
        this.images = images;
    }

    @JsonProperty("branch")
    public Object getBranch() {
        return branch;
    }

    @JsonProperty("branch")
    public void setBranch(Object branch) {
        this.branch = branch;
    }

    @JsonProperty("mandatory")
    public Boolean getMandatory() {
        return mandatory;
    }

    @JsonProperty("mandatory")
    public void setMandatory(Boolean mandatory) {
        this.mandatory = mandatory;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
