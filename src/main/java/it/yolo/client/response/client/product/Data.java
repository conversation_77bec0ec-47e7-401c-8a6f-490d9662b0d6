
package it.yolo.client.response.client.product;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import it.yolo.client.response.client.packet.PacketResponse;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "code",
        "description",
        "startDate",
        "endDate",
        "recurring",
        "externalId",
        "splits",
        "questions",
        "categories",
        "asset",
        "insuranceCompany",
        "additionalProductInfo",
        "duration_type",
        "holder_maximum_age",
        "holder_minimum_age"
})
public class Data {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
    @JsonProperty("startDate")
    private String startDate;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("endDate")
    private Object endDate;
    @JsonProperty("recurring")
    private Boolean recurring;
    @JsonProperty("externalId")
    private Object externalId;
    @JsonProperty("splits")
    private List<Object> splits = null;
    @JsonProperty("questions")
    private List<Question> questions = null;
    @JsonProperty("categories")
    private List<Category> categories = null;
    @JsonProperty("properties")
    private JsonNode properties;
    @JsonProperty("insuranceCompany")
    private String insuranceCompany;
    @JsonProperty("duration")
    private Integer duration;
    @JsonProperty("duration_type")
    private String durationType;
    @JsonProperty("configuration")
    private Configuration configuration;
    @JsonProperty("additionalProductInfo")
    private Object additionalProductInfo;
    @JsonProperty("productType")
    private String productType;
    @JsonProperty("legacy")
    private JsonNode legacy;
    @JsonProperty("packet")
    private PacketResponse packet;
    @JsonProperty("packets")
    private ArrayNode packets;
    @JsonProperty("utmSources")
    private Set<UtmSourcesDto> utmSources;
    @JsonProperty("holder_minimum_age")
    private Long minAge;
    @JsonProperty("holder_maximum_age")
    private Long maxAge;
    @JsonProperty("informativeSet")
    private String informativeSet;
    @JsonProperty("privacyDocumentationLink")
    private String privacyDocumentationLink;
    @JsonProperty("attachment3_4")
    private String attachment_3_4_4Ter;

    @JsonProperty("packets")
    public ArrayNode getPackets() {
        return packets;
    }

    @JsonProperty("packets")
    public void setPackets(ArrayNode packets) {
        this.packets = packets;
    }

    public PacketResponse getPacket() {
        return packet;
    }

    public void setPacket(PacketResponse packet) {
        this.packet = packet;
    }

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("startDate")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("startDate")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public Object getEndDate() {
        return endDate;
    }

    @JsonProperty("endDate")
    public void setEndDate(Object endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("recurring")
    public Boolean getRecurring() {
        return recurring;
    }

    @JsonProperty("recurring")
    public void setRecurring(Boolean recurring) {
        this.recurring = recurring;
    }

    @JsonProperty("externalId")
    public Object getExternalId() {
        return externalId;
    }

    @JsonProperty("externalId")
    public void setExternalId(Object externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("splits")
    public List<Object> getSplits() {
        return splits;
    }

    @JsonProperty("splits")
    public void setSplits(List<Object> splits) {
        this.splits = splits;
    }

    @JsonProperty("questions")
    public List<Question> getQuestions() {
        return questions;
    }

    @JsonProperty("questions")
    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }

    @JsonProperty("categories")
    public List<Category> getCategories() {
        return categories;
    }

    @JsonProperty("categories")
    public void setCategories(List<Category> categories) {
        this.categories = categories;
    }

    @JsonProperty("properties")
    public JsonNode getProperties() {
        return properties;
    }

    @JsonProperty("properties")
    public void setProperties(JsonNode properties) {
        this.properties = properties;
    }

    @JsonProperty("insuranceCompany")
    public String getInsuranceCompany() {
        return insuranceCompany;
    }

    @JsonProperty("insuranceCompany")
    public void setInsuranceCompany(String insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }

    @JsonProperty("duration")
    public Integer getDuration() {
        return duration;
    }

    @JsonProperty("duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @JsonProperty("duration_type")
    public String getDurationType() {
        return durationType;
    }

    @JsonProperty("duration_type")
    public void setDurationType(final String durationType) {
        this.durationType = durationType;
    }

    @JsonProperty("configuration")
    public Configuration getConfiguration() {
        return configuration;
    }

    @JsonProperty("configuration")
    public void setConfiguration(Configuration configuration) {
        this.configuration = configuration;
    }

    @JsonProperty("utmSources")
    public Set<UtmSourcesDto> getUtmSources() {
        return utmSources;
    }

    @JsonProperty("utmSources")
    public void setUtmSources(Set<UtmSourcesDto> utmSources) {
        this.utmSources = utmSources;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public JsonNode getLegacy() {
        return legacy;
    }

    public void setLegacy(JsonNode legacy) {
        this.legacy = legacy;
    }

    public Long getMinAge() {
        return minAge;
    }

    public void setMinAge(Long minAge) {
        this.minAge = minAge;
    }

    public Long getMaxAge() {
        return maxAge;
    }

    public void setMaxAge(Long maxAge) {
        this.maxAge = maxAge;
    }

    @JsonProperty("additionalProductInfo")
    public Object getAdditionalProductInfo() {
        return additionalProductInfo;
    }

    @JsonProperty("additionalProductInfo")
    public void setAdditionalProductInfo(Object additionalProductInfo) {
        this.additionalProductInfo = additionalProductInfo;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("informativeSet")
    public String getInformativeSet() {
        return informativeSet;
    }
    @JsonProperty("informativeSet")
    public void setInformativeSet(String informativeSet) {
        this.informativeSet = informativeSet;
    }
    @JsonProperty("privacyDocumentationLink")
    public String getPrivacyDocumentationLink() {
        return privacyDocumentationLink;
    }
    @JsonProperty("privacyDocumentationLink")
    public void setPrivacyDocumentationLink(String privacyDocumentationLink) {
        this.privacyDocumentationLink = privacyDocumentationLink;
    }
    @JsonProperty("attachment3_4")
    public String getAttachment_3_4_4Ter() {
        return attachment_3_4_4Ter;
    }
    @JsonProperty("attachment3_4")
    public void setAttachment_3_4_4Ter(String attachment_3_4_4Ter) {
        this.attachment_3_4_4Ter = attachment_3_4_4Ter;
    }

}
