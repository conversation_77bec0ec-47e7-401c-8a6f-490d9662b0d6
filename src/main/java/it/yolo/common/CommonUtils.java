package it.yolo.common;

import it.yolo.entity.WorkflowEntity;
import it.yolo.repository.WorkflowRepository;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;

@ApplicationScoped
public class CommonUtils {

    @Inject
    WorkflowRepository workflowRepo;

    public List<String> getStepState(Integer productId) {

        WorkflowEntity workflow = workflowRepo.findByProductId(productId);
        return Arrays.asList(workflow.getWorkflow().split(";"));
    }
}
