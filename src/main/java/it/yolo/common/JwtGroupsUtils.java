package it.yolo.common;

import io.quarkus.logging.Log;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;

import javax.json.JsonArray;
import javax.json.JsonString;
import javax.json.JsonValue;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class JwtGroupsUtils {

    public static String getGroup(JsonWebToken jwt, String tchGroup, String intermediaryGroup){
        Log.info("Start getGroup");
        JsonValue claimValue = jwt.getClaim("cognito:groups");
        if (claimValue == null) {
            return null;
        }

        List<String> groupList = extractClaimGroups(claimValue);

        Set<String> targetSet = Arrays.stream(new String[]{tchGroup, intermediaryGroup})
                .filter(word -> word != null && !word.trim().isEmpty())
                .map(String::trim)
                .collect(Collectors.toSet());

        return groupList.stream()
                .map(String::trim)
                .filter(targetSet::contains)
                .findFirst()
                .orElse("null");
    }

    private static List<String> extractClaimGroups(JsonValue claimValue) {
        Log.info("Start extractClaimGroups");
        if (claimValue == null || claimValue.getValueType() != JsonValue.ValueType.ARRAY) {
            throw new IllegalArgumentException("Claim value is not a valid json array");
        }

        JsonArray jsonArray = claimValue.asJsonArray();

        return jsonArray.stream()
                .filter(value -> value.getValueType() == JsonValue.ValueType.STRING)
                .map(value -> ((JsonString) value).getString())
                .collect(Collectors.toList());
    }
}
