package it.yolo.common;

import java.time.LocalDate;

public class SeasonDates {
    private LocalDate preseasonStart;
    private LocalDate preseasonEnd;
    private LocalDate seasonStart;
    private LocalDate seasonEnd;

    public LocalDate getPreseasonStart() {
        return preseasonStart;
    }

    public void setPreseasonStart(LocalDate preseasonStart) {
        this.preseasonStart = preseasonStart;
    }

    public LocalDate getPreseasonEnd() {
        return preseasonEnd;
    }

    public void setPreseasonEnd(LocalDate preseasonEnd) {
        this.preseasonEnd = preseasonEnd;
    }

    public LocalDate getSeasonStart() {
        return seasonStart;
    }

    public void setSeasonStart(LocalDate seasonStart) {
        this.seasonStart = seasonStart;
    }

    public LocalDate getSeasonEnd() {
        return seasonEnd;
    }

    public void setSeasonEnd(LocalDate seasonEnd) {
        this.seasonEnd = seasonEnd;
    }

    public SeasonDates(LocalDate preseasonStart, LocalDate preseasonEnd, LocalDate seasonStart, LocalDate seasonEnd) {
        this.preseasonStart = preseasonStart;
        this.preseasonEnd = preseasonEnd;
        this.seasonStart = seasonStart;
        this.seasonEnd = seasonEnd;
    }

    public SeasonDates() {
    }
}