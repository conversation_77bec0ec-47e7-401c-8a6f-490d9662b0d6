package it.yolo.common;

import java.time.LocalDate;
import java.util.Map;

public class TaxCodeUtils {

    private static final Map<Character, Integer> MONTH_MAP = Map.ofEntries(
            Map.entry('A', 1), Map.entry('B', 2), Map.entry('C', 3),
            Map.entry('D', 4), Map.entry('E', 5), Map.entry('H', 6),
            Map.entry('L', 7), Map.entry('M', 8), Map.entry('P', 9),
            Map.entry('R', 10), Map.entry('S', 11), Map.entry('T', 12)
    );

    /**
     * Estrae la data di nascita da un codice fiscale.
     *
     * @param taxCode il codice fiscale (16 caratteri)
     * @return la data di nascita come {@link LocalDate}
     * @throws IllegalArgumentException se il codice fiscale è nullo, troppo corto o ha un formato non valido
     */
    public static LocalDate extractBirthDate(String taxCode) {
        if (taxCode == null || taxCode.length() < 16) {
            throw new IllegalArgumentException("Invalid tax code: " + taxCode);
        }

        // Estrazione dei componenti del codice fiscale
        int year = Integer.parseInt(taxCode.substring(6, 8));
        char monthChar = taxCode.charAt(8);
        int day = Integer.parseInt(taxCode.substring(9, 11));

        // Recupero del mese dal carattere
        Integer month = MONTH_MAP.get(monthChar);
        if (month == null) {
            throw new IllegalArgumentException("Invalid month in tax code: " + taxCode);
        }

        // Per le donne il giorno è >= 40
        if (day > 40) {
            day -= 40;
        }

        // Determina il secolo (ipotesi: 1900-1999 o 2000-2099)
        int currentYear = LocalDate.now().getYear();
        year += (year <= (currentYear % 100) ? 2000 : 1900);

        return LocalDate.of(year, month, day);
    }
}