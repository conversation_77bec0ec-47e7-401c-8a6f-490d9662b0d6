package it.yolo.common;

import com.ezylang.evalex.EvaluationException;
import com.ezylang.evalex.Expression;
import com.ezylang.evalex.parser.ParseException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.product.Configuration;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.ValidationStructureProductException;
import it.yolo.model.CalculateCeilingsResponse;
import it.yolo.records.Packet;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.ConfigProvider;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class Utility {

    final static ObjectMapper MAPPER = new ObjectMapper();

    private Utility() {
        //does nothing
    }

    public static void validationProductStructure(JsonNode schemagoods, JsonNode goods) throws ValidationStructureProductException {
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V201909);
        JsonSchema jsonSchema = factory.getSchema(schemagoods);
        Set<ValidationMessage> errors = jsonSchema.validate(goods);
        if (!errors.isEmpty()) {
            String error = errors.stream().toList().get(0).toString();
            throw new ValidationStructureProductException("Validation Error", error);
        }
    }


    public static String generateOrderCode() {
        /*
        int orderCode = (int) (Math.random() * 100);
        String orderC = String.valueOf(orderCode);
         */
        UUID uuid = UUID.randomUUID();
        String uuidCode = String.valueOf(uuid);
        String sub = uuidCode.substring(1, 8);
//        String siglaOrderCode = "Y-";
        String siglaOrderCode = ConfigProvider.getConfig().getValue("order.prefix-code", String.class);
        return siglaOrderCode.concat(sub);
    }


    public static String generateHolderUnicode() {
        return UUID.randomUUID().toString().substring(0, 11).replace("-", "");
    }

    public static LocalDateTime getOrderDuration(final LocalDateTime startDate, final int duration, final String type) {
        if (StringUtils.isBlank(type)) {
            return startDate;
        }
        LocalDateTime endDate = null;
        switch (type) {
            case "day" -> {
                endDate = startDate.plusDays(duration);
            }
            case "month" -> {
                endDate = startDate.plusMonths(duration);
            }
            case "year" -> {
                endDate = startDate.plusYears(duration);
            }
        }
        if (endDate != null) {
            endDate = endDate.truncatedTo(ChronoUnit.DAYS).plusDays(1).minus(1, ChronoUnit.MILLIS);
        }
        return endDate;
    }

    public static LocalDateTime getOrderDurationV2(final LocalDateTime startDate, final int duration, final String type) {
        if (StringUtils.isBlank(type)) {
            return startDate;
        }
        LocalDateTime endDate = null;
        switch (type) {
            case "day" -> {
                endDate = startDate.plusDays(duration);
            }
            case "month" -> {
                endDate = startDate.plusMonths(duration);
            }
            case "year" -> {
                endDate = startDate.plusYears(duration);
            }
        }
        if (endDate != null) {
            endDate = endDate.minus(1, ChronoUnit.MILLIS);
        }
        return endDate;
    }

    public static LocalDateTime setStartDate(final String productCode) {
        switch (productCode) {
            case "ergo-mountain-silver":
            case "ergo-mountain-gold":
                return LocalDateTime.now();
        }
        if (productCode.startsWith("tim-for-ski")) {
            return LocalDateTime.now();
        }
        return LocalDateTime.now().plusDays(1);
    }

    //war.get("externalCode")
    public static CalculateCeilingsResponse calculateCeilings(List<OrderItemEntity> orderItems, JsonNode insuredItem, PacketResponse packet) {
        ArrayNode warrantiesNode = JsonNodeFactory.instance.arrayNode();
        AtomicBoolean updated = new AtomicBoolean(false);

        // Cache configuration access - avoid repeated navigation
        JsonNode configuration = packet.getData().getConfiguration();
        Configuration productConfig = packet.getData().getProduct().getConfiguration();
        JsonNode warranties = packet.getData().getWarranties();

        // Cache premium limits - avoid repeated has() and get() calls
        Double maxInsurancePremium = configuration.has("maxInsurancePremium") ?
                configuration.get("maxInsurancePremium").asDouble() : null;
        Double minInsurancePremium = configuration.has("minInsurancePremium") ?
                configuration.get("minInsurancePremium").asDouble() : null;

        // Pre-calculate available values once
        List<String> availableValues = new ArrayList<>();
        boolean shouldSetAvailableValues =
                productConfig.getProperties().has("availableValues") &&
                productConfig.getProperties().get("availableValues").asBoolean();

        if (shouldSetAvailableValues) {
            setAvailableValues(insuredItem, availableValues, configuration);
        }

        // Pre-convert to JsonNode to avoid multiple conversions
        ArrayNode jsonAvailableValues = shouldSetAvailableValues ? MAPPER.valueToTree(availableValues) : null;

        // Process warranties efficiently
        processWarranties(warranties, insuredItem, orderItems, warrantiesNode, updated,
                maxInsurancePremium, minInsurancePremium, jsonAvailableValues);

        // Clean up warranty nodes in a single pass
        cleanupWarrantyNodes(warrantiesNode);

        return new CalculateCeilingsResponse(updated.get(), warrantiesNode);
    }

    static void processWarranties(JsonNode warranties, JsonNode insuredItem, List<OrderItemEntity> orderItems,
                                          ArrayNode warrantiesNode, AtomicBoolean updated,
                                          Double maxInsurancePremium, Double minInsurancePremium,
                                          ArrayNode jsonAvailableValues) {

        // Cache the theft warranty name to avoid string creation in loop
        final String THEFT_WARRANTY_NAME = "CMS_yhomeTheftRobberyName";

        warranties.forEach(warranty -> {
            JsonNode ceilings = warranty.get("ceilings");
            JsonNode anagWarranty = warranty.get("anagWarranty");

            // Handle theft warranty special case
            if (jsonAvailableValues != null &&
                    anagWarranty != null &&
                    anagWarranty.has("name") &&
                    THEFT_WARRANTY_NAME.equals(anagWarranty.get("name").asText())) {
                ((ObjectNode) ceilings).set("available", jsonAvailableValues);
            }

            // Process expression-based warranties
            if (insuredItem != null && ceilings.has("exp")) {
                ArrayNode expressionNode = ceilings.withArray("exp");
                expressionNode = calculateExpressionForPriorityFilter(ceilings, orderItems);
                evaluateExp(warranty, insuredItem, maxInsurancePremium, minInsurancePremium,
                        warrantiesNode, expressionNode, updated);
            } else if (!ceilings.has("exp")) {
                // Add warranties without expressions directly
                warrantiesNode.add(warranty);
            }
        });
    }

    static void cleanupWarrantyNodes(ArrayNode warrantiesNode) {
        warrantiesNode.forEach(warranty -> {
            JsonNode ceilings = warranty.get("ceilings");
            ObjectNode ceilingsObj = (ObjectNode) ceilings;

            // Remove cleanup fields in single pass
            if (ceilings.hasNonNull("priorityFilter")) {
                ceilingsObj.remove("priorityFilter");
            }
            if (ceilings.hasNonNull("filterCielingByOrder")) {
                ceilingsObj.remove("filterCielingByOrder");
            }
        });
    }

    static void evaluateExp(JsonNode warranties, JsonNode insuredItem, Double maxInsurancePremium,
                            Double minInsurancePremium, ArrayNode warrantiesNode, ArrayNode expressionNode, AtomicBoolean updated) {

        JsonNode ceilings = warranties.get("ceilings");

        if (ceilings.has("range")) {
            processRangeExpressions(ceilings, insuredItem, maxInsurancePremium, minInsurancePremium, expressionNode, updated);
        } else if (ceilings.has("available")) {
            processAvailableExpressions(ceilings, insuredItem, maxInsurancePremium, minInsurancePremium, expressionNode, updated);
        }

        warrantiesNode.add(warranties);
    }

    private static void processRangeExpressions(JsonNode ceilings, JsonNode insuredItem, Double maxInsurancePremium,
                                                Double minInsurancePremium, ArrayNode expressionNode, AtomicBoolean updated) {

        List<String> possibleValues = new ArrayList<>();
        // Initialize possibleValues with existing range values
        ceilings.withArray("range").forEach(v -> possibleValues.add(v.asText()));

        final Double[] preselectedValue = {null};
        final Double[] minRangeValue = {null};

        expressionNode.forEach(exp -> {
            if (!exp.get("isOp").asBoolean()) {
                return;
            }

            Double result = calculateExpressionResult(exp, insuredItem, minInsurancePremium, maxInsurancePremium);
            if (result == null) {
                return;
            }

            String resultStr = String.valueOf(result.intValue());

            // Process based on expression type - avoid duplicate checks
            boolean isMin = exp.has("min") && exp.get("min").isEmpty();
            boolean isMax = exp.has("max") && exp.get("max").isEmpty();
            boolean isPreselected = exp.has("preselected") && exp.get("preselected").isEmpty();

            if (isMin || isMax || isPreselected) {
                possibleValues.add(resultStr);

                // Track minimum value
                if (minRangeValue[0] == null || result < minRangeValue[0]) {
                    minRangeValue[0] = result;
                }

                // Store preselected value if this is a preselected expression
                if (isPreselected) {
                    preselectedValue[0] = result;
                }

                updated.set(true);
            }
        });

        // Set final values
        setRangeValues(ceilings, possibleValues, preselectedValue[0], minRangeValue[0]);
    }

    private static void processAvailableExpressions(JsonNode ceilings, JsonNode insuredItem, Double maxInsurancePremium,
                                                    Double minInsurancePremium, ArrayNode expressionNode, AtomicBoolean updated) {

        List<String> availValues = new ArrayList<>();

        // Process all expressions first, then set values once
        expressionNode.forEach(exp -> {
            if (exp.get("isOp").asBoolean()) {
                Double result = calculateExpressionResult(exp, insuredItem, minInsurancePremium, maxInsurancePremium);
                if (result != null) {
                    // Apply only max limit for available expressions (as per original logic)
                    if (maxInsurancePremium != null && result > maxInsurancePremium) {
                        result = maxInsurancePremium;
                    }
                    availValues.add(String.valueOf(result.intValue()));
                    updated.set(true);
                }
            } else if (exp.get("formula").isArray()) {
                exp.withArray("formula").forEach(v -> availValues.add(v.asText()));
                updated.set(true);
            }
        });

        // Set values only once after processing all expressions
        if (!availValues.isEmpty()) {
            ArrayNode availables = MAPPER.valueToTree(availValues);
            ((ObjectNode) ceilings).set("preselected", availables.get(0));
            ((ObjectNode) ceilings).set("available", availables);
        }
    }

    private static Double calculateExpressionResult(JsonNode exp, JsonNode insuredItem,
                                                    Double minInsurancePremium, Double maxInsurancePremium) {

        String coefficientKey = exp.get("coefficientKey").asText();
        String operator = exp.get("operator").asText();
        String coefficientVal = exp.get("coefficientVal").asText();

        // Build expression string
        String baseExpression;
        if (coefficientKey.startsWith("/")) {
            baseExpression = insuredItem.at(coefficientKey).asText() + operator + coefficientVal;
        } else {
            baseExpression = insuredItem.get(coefficientKey).asText() + operator + coefficientVal;
        }
        String cleanExpression = StringUtils.remove(baseExpression, "\"");

        // Validate expression parts
        String operatorPart = StringUtils.substringAfter(cleanExpression, operator);
        String operandPart = StringUtils.substringBefore(cleanExpression, operator);

        if (StringUtils.isBlank(operatorPart) || StringUtils.isBlank(operandPart)) {
            return null;
        }

        // Calculate and apply limits
        try {
            Expression expression = new Expression(cleanExpression);
            Double result = expression.evaluate().getNumberValue().doubleValue();

            // Apply limits
            if (minInsurancePremium != null && result < minInsurancePremium) {
                result = minInsurancePremium;
            }
            if (maxInsurancePremium != null && result > maxInsurancePremium) {
                result = maxInsurancePremium;
            }

            return result;

        } catch (EvaluationException | ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private static void setRangeValues(JsonNode ceilings, List<String> possibleValues,
                                       Double preselectedValue, Double minRangeValue) {

        ArrayNode ranges = MAPPER.valueToTree(possibleValues);

        // Determine preselected value efficiently
        JsonNode valueToPreselect;
        if (preselectedValue != null) {
            valueToPreselect = MAPPER.valueToTree(String.valueOf(preselectedValue.intValue()));
        } else if (minRangeValue != null) {
            valueToPreselect = MAPPER.valueToTree(String.valueOf(minRangeValue.intValue()));
        } else if (!possibleValues.isEmpty()) {
            valueToPreselect = MAPPER.valueToTree(possibleValues.get(0));
        } else {
            valueToPreselect = ranges.get(0);
        }

        // Set values once
        ((ObjectNode) ceilings).set("preselected", valueToPreselect);
        ((ObjectNode) ceilings).set("range", ranges);
    }

    static ArrayNode calculateExpressionForPriorityFilter(JsonNode ceilings, List<OrderItemEntity> orderItems) {
        if (ceilings.hasNonNull("priorityFilter") && orderItems.size() > 0) { // skip for empty orderItem during update payment details
            Map<String, String> filterMap = new HashMap<>();

            ArrayNode priorityFilter = ceilings.withArray("priorityFilter");
            for (int i = 0; i < priorityFilter.size(); i++) {
                JsonNode filter = priorityFilter.get(i);
                extractValueFromFilter(filter, orderItems.get(0).getInsured_item(), filterMap);
            }
            return filterCeilings(filterMap, ceilings.withArray("filterCielingByOrder"), orderItems.get(0), orderItems.get(0).getInsured_item());
//        } else if(){
//            return ceilings.withArray("exp");
        } else {
            return ceilings.withArray("exp");
        }
    }


    private static void extractValueFromFilter(JsonNode filter, JsonNode insuredItem, Map<String, String> filterMap) {
        String fieldFilter = filter.get("fieldFilter").asText();
        if (fieldFilter.startsWith("/")) {
            if (fieldFilter.contains("arr_index")) {
                List<String> values = new ArrayList<>();
                //array stream
                String split = fieldFilter.split("/arr_index")[0];
                if (insuredItem.at(split).isEmpty()) {
                    return;
                }
                ArrayNode array = (ArrayNode) insuredItem.at(split);
                for (int i = 0; i < array.size(); i++) {
                    String currentPath = fieldFilter.replace("arr_index", Integer.toString(i));
                    values.add(insuredItem.at(currentPath).asText());
                }
                ArrayNode priorities = filter.withArray("values");
                for (int i = 0; i < priorities.size(); i++) {
                    if (values.contains(priorities.get(i).asText())) {
                        filterMap.put(fieldFilter, priorities.get(i).asText());
                        break;
                    }
                }
            } else {
                filterMap.put(fieldFilter, insuredItem.at(fieldFilter).asText());
            }
        }
    }

    private static ArrayNode filterCeilings(Map<String, String> filtermap, ArrayNode ceilingsToFilter, OrderItemEntity orderItem, JsonNode insuredItem) {
        //LIMITAZIONE: GESTITE SOLO 2 FILTER CONDITION IN AND
        ArrayNode possibleCeilings = JsonNodeFactory.instance.arrayNode();
        for (int i = 0; i < ceilingsToFilter.size(); i++) {
            JsonNode ceilToFilter = ceilingsToFilter.get(i);
            ArrayNode conditions = ceilToFilter.withArray("filterOrderCondition");
            Boolean accepted = Boolean.FALSE;
            for (int j = 0; j < conditions.size(); j++) {
                JsonNode condition = conditions.get(j);
                accepted = evaluateCondition(condition, filtermap, orderItem, insuredItem);
                if (!accepted) {
                    //next available ceilings
                    break;
                }
            }
            if (accepted) {
                possibleCeilings = ceilToFilter.withArray("availableCeilings");
                break;
            }
        }
        return possibleCeilings;
    }

    private static Boolean evaluateCondition(JsonNode condition, Map<String, String> filterMap, OrderItemEntity orderItem, JsonNode insuredItem) {
        String fieldFilter = condition.get("fieldFilter").asText();
        Boolean accepted = Boolean.FALSE;
        if (fieldFilter.startsWith("/")) {
            List<String> conditionFieldValues = new ArrayList<>();
            condition.withArray("fieldValue").forEach(v -> conditionFieldValues.add(v.asText()));
            if (filterMap.containsKey(fieldFilter) && conditionFieldValues.contains(filterMap.get(fieldFilter))) {
                accepted = true;
            }
        } else if (fieldFilter.equalsIgnoreCase("RULE_DURATION")) {
            String stringExp = Long.toString(ChronoUnit.DAYS.between(orderItem.getStart_date(), orderItem.getExpiration_date().plusDays(1)))
                    .concat(condition.get("fieldOperator").asText()).concat(condition.get("fieldValue").asText());
            Expression expression = new Expression(stringExp);
            try {
                accepted = expression.evaluate().getBooleanValue();
            } catch (EvaluationException | ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return accepted;
    }

    public static void setAvailableValues(JsonNode insuredItem, List<String> availableValues, JsonNode packetConfiguration) {
        String tipologiaUsoAbitazione = insuredItem.get("tipologiaUsoAbitazione").asText();
        String tipologiaCostruttivaAbitazione = insuredItem.get("tipologiaCostruttivaAbitazione").asText();
        if (tipologiaUsoAbitazione != null && tipologiaCostruttivaAbitazione != null && packetConfiguration.has(tipologiaUsoAbitazione)) {
            if (packetConfiguration.get(tipologiaUsoAbitazione).isArray()) {
                packetConfiguration.withArray(tipologiaUsoAbitazione).forEach(v -> {
                    availableValues.add(v.textValue());
                });
            } else if (packetConfiguration.get(tipologiaUsoAbitazione).has(tipologiaCostruttivaAbitazione)) {
                packetConfiguration.get(tipologiaUsoAbitazione).withArray(tipologiaCostruttivaAbitazione).forEach(v -> {
                    availableValues.add(v.textValue());
                });
            }
        }
    }


    public static PacketResponse setPacketWithCieling(Packet packet, OrderItemEntity orderItem) {
        if (orderItem.getInstance() != null && orderItem.getInstance().get("ceilings") != null) {
            packet.packetResponse().getData().setWarranties(orderItem.getInstance().get("ceilings"));
        }
        return packet.packetResponse();
    }


    public static String convertToDateFormat(String inputDate) {
        String[] formats = {
                "dd/MM/yyyy",
                "yyyy-MM-dd",
                "MM/dd/yyyy",
                "yyyy/MM/dd",
                "dd-MM-yyyy",
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        };
        LocalDate date = null;
        for (String format : formats) {
            try {
                if (format.equals("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")) {
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(inputDate);
                    date = offsetDateTime.toLocalDate();
                } else {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                    date = LocalDate.parse(inputDate, formatter);
                }
                break;
            } catch (Exception ignored) {
            }
        }

        if (date != null) {
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            return null;
        }
    }

    /**
     * Calcola il prezzo finale applicando la commissione YIN se presente
     *
     * @param basePrice prezzo base
     * @param yinCommission commissione YIN da aggiungere (può essere null)
     * @return prezzo finale come stringa
     */
    public static String calculatePriceWithYinCommission(double basePrice, Double yinCommission) {
        if (yinCommission != null) {
            return String.valueOf(basePrice + yinCommission);
        }
        return String.valueOf(basePrice);
    }

    /**
     * Estrae la commissione YIN dall'instance dell'order item
     *
     * @param instance JsonNode dell'instance
     * @return commissione YIN o null se non presente
     */
    public static Double extractYinCommission(JsonNode instance) {
        if (instance != null && instance.has("yin_commission")) {
            return instance.get("yin_commission").asDouble();
        }
        return null;
    }

    /**
     * Determina se l'ordine è in modalità OneShot
     *
     * @param insuredItem JsonNode dell'item assicurato
     * @return true se è OneShot, false altrimenti
     */
    public static boolean isOneShotMode(JsonNode insuredItem) {
        return insuredItem != null &&
               insuredItem.has("isOneShot") &&
               insuredItem.get("isOneShot").asBoolean();
    }

    /**
     * Gestisce il pricing OneShot impostando price e annualPrice
     *
     * @param orderItem OrderItemEntity da aggiornare
     * @param oneShotQuote JsonNode con i dati OneShot
     * @param yinCommission commissione YIN (può essere null)
     */
    public static void processOneShotPricing(OrderItemEntity orderItem, JsonNode oneShotQuote, Double yinCommission) {
        if (oneShotQuote != null && oneShotQuote.has("total")) {
            double basePrice = oneShotQuote.get("total").asDouble();
            String finalPrice = calculatePriceWithYinCommission(basePrice, yinCommission);
            orderItem.setPrice(finalPrice);
            orderItem.setAnnualPrice(finalPrice); // Stesso valore per OneShot
        }
    }

    /**
     * Gestisce il pricing mensile impostando price e annualPrice
     *
     * @param orderItem OrderItemEntity da aggiornare
     * @param monthlyQuote JsonNode con i dati mensili
     * @param yinCommission commissione YIN (può essere null)
     */
    public static void processMonthlyPricing(OrderItemEntity orderItem, JsonNode monthlyQuote, Double yinCommission) {
        if (monthlyQuote != null) {
            // Imposta il prezzo mensile
            if (monthlyQuote.has("total")) {
                double basePrice = monthlyQuote.get("total").asDouble();
                String finalPrice = calculatePriceWithYinCommission(basePrice, yinCommission);
                orderItem.setPrice(finalPrice);
            }

            // Imposta il prezzo annuale da oneShotTotal
            if (monthlyQuote.has("oneShotTotal")) {
                orderItem.setAnnualPrice(monthlyQuote.get("oneShotTotal").asText());
            }
        }
    }

    /**
     * Gestisce il pricing legacy da array con singolo elemento
     *
     * @param orderItem OrderItemEntity da aggiornare
     * @param dataElement JsonNode del primo elemento dell'array
     * @param yinCommission commissione YIN (può essere null)
     */
    public static void processLegacyArrayPricing(OrderItemEntity orderItem, JsonNode dataElement, Double yinCommission) {
        if (dataElement == null) return;

        // Priorità a discountedTotal, fallback su total
        JsonNode priceNode = dataElement.get("discountedTotal");
        if (priceNode == null || priceNode.isNull()) {
            priceNode = dataElement.get("total");
        }

        if (priceNode != null && !priceNode.isNull()) {
            double basePrice = priceNode.asDouble();
            String finalPrice = calculatePriceWithYinCommission(basePrice, yinCommission);
            orderItem.setPrice(finalPrice);
        }

        // Impostazione prezzo annuale se presente
        if (dataElement.has("annualTotal") && !dataElement.get("annualTotal").isNull()) {
            orderItem.setAnnualPrice(dataElement.get("annualTotal").asText());
        }
    }

    /**
     * Gestisce l'aggiornamento dei ceiling warranties
     *
     * @param orderItem OrderItemEntity contenente l'instance
     * @param cielingWarrantyName nome della garanzia ceiling
     * @param cielingValue valore del ceiling
     */
    public static void processCeilingWarranties(OrderItemEntity orderItem, String cielingWarrantyName, long cielingValue) {
        if (orderItem.getInstance() == null) return;

        JsonNode chosenWarranties = orderItem.getInstance().get("chosenWarranties");
        if (chosenWarranties == null || !chosenWarranties.has("data")) return;

        ArrayNode warranties = chosenWarranties.get("data").withArray("warranties");
        warranties.forEach(warranty -> {
            JsonNode anagWarranty = warranty.get("anagWarranty");
            if (anagWarranty != null && anagWarranty.get("name") != null) {
                String warrantyName = anagWarranty.get("name").asText();

                if (warrantyName.equalsIgnoreCase(cielingWarrantyName)) {
                    JsonNode ceilingsNode = warranty.get("ceilings");
                    if (ceilingsNode != null) {
                        ArrayNode ceilingsArray = ceilingsNode.withArray("ceilings");
                        ceilingsArray.add(cielingValue);
                    }
                }
            }
        });
    }

    /**
     * Aggiorna il premium assicurativo dell'ordine
     *
     * @param order OrderEntity da aggiornare
     * @param price prezzo come stringa
     * @throws IllegalStateException se il formato del prezzo non è valido
     */
    public static void updateInsurancePremium(OrderEntity order, String price) {
        if (price != null) {
            try {
                BigDecimal premium = new BigDecimal(price);
                order.setInsurancePremium(premium);
            } catch (NumberFormatException e) {
                throw new IllegalStateException("Invalid price format: " + price, e);
            }
        }
    }

    public static SeasonDates calculateSeasonDates(String preseasonStartStr, String preseasonEndStr, String seasonStartStr, String seasonEndStr){
        LocalDate today=LocalDate.now();
        LocalDate preseasonStart=LocalDate.parse(today.getYear()+preseasonStartStr);
        LocalDate preseasonEnd=LocalDate.parse(today.getYear()+preseasonEndStr);
        if(today.isAfter(preseasonEnd) && today.isBefore(preseasonStart)) {
            //sono fuori da periodo vendibilità
            throw new RuntimeException("fuori periodo vendibilità pacchetto");
        } else if (today.isBefore(preseasonEnd) || today.equals(preseasonEnd)) {
            //sono in anno di fine stagione
            preseasonStart= preseasonStart.minusYears(1);
        } else {
            //sono in anno inizio stagione
            preseasonEnd= preseasonEnd.plusYears(1);
        }
        LocalDate seasonStart=LocalDate.parse(preseasonStart.getYear()+seasonStartStr);
        LocalDate seasonEnd=LocalDate.parse(preseasonEnd.getYear()+seasonEndStr);
        return new SeasonDates(preseasonStart, preseasonEnd, seasonStart, seasonEnd);
    }
}
