package it.yolo.constants;

//RAPPRESENTAZIONE DEGLI STATI DELL'ORDINE A DB.
/*
 * 
 * ATTENZIONE : TENERE AGGIORNATA L'ENUM RISPETTO AL DB
 * 
 */
public enum OrderStateEnum {
    
    CONFIRMED("Confirmed", 3),
    FAILED("Failed", 4);

    private final String key;
    private final Integer value;

    OrderStateEnum(String key, Integer value){
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }
    public Integer getValue() {
        return value;
    }

}
