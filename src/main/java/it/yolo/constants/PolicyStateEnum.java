package it.yolo.constants;

//RAPPRESENTAZIONE DEGLI STATI DELLA POLICY A DB.
/*
 * 
 * ATTENZIONE : TENERE AGGIORNATA L'ENUM RISPETTO AL DB
 * 
 */
public enum PolicyStateEnum {
    
    ACTIVE("Active", 2),
    FAILED("Failed", 6);

    private final String key;
    private final Integer value;

    PolicyStateEnum(String key, Integer value){

        this.key = key;
        this.value = value;

    }

    public String getKey() {
        return key;
    }
    public Integer getValue() {
        return value;
    }

}
