package it.yolo.constants;

import org.apache.commons.lang3.StringUtils;

public enum StepStateEnum {
    INSURANCE_INFO(0),
	WARRANTIES(1),
    ADDRESS(2),
	ADDITIONAL_INSURANCE_INFO(3),
    SURVEY(4),
    PAYMENT(5),
    CONFIRM(6),
    COMPLETE(7);
    
    private final Integer weight;
	
	StepStateEnum(Integer weight) {
		this.weight = weight;
	}
	
	public Integer getWeight() {
		return weight;
	}

	/**
	 * restituisce un enum dal suo valore
	 * @param value
	 * @return
	 */
	public static StepStateEnum getByValue(Integer value) {
		if(value == null || value < 0) return INSURANCE_INFO;
		if(value >= 7) return COMPLETE;
		
		for(StepStateEnum val : StepStateEnum.values()) {
			if(value.intValue() == val.getWeight().intValue()) return val;
		}
		
		return null;
	}
	
	public static StepStateEnum getByString(String value) {
		if(StringUtils.isBlank(value)) return INSURANCE_INFO;
		
		switch (value) {
			case "INSURANCE_INFO", "insurance_info":
				return INSURANCE_INFO;
			case "WARRANTIES", "warranties":
				return WARRANTIES;
			case "ADDRESS", "address":
				return ADDRESS;
			case "ADDITIONAL_INSURANCE_INFO", "additional_insurance_info":
				return ADDITIONAL_INSURANCE_INFO;
			case "SURVEY", "survey":
				return SURVEY;
			case "PAYMENT", "payment":
				return PAYMENT;
			case "CONFIRM", "confirm":
				return CONFIRM;
			case "COMPLETE", "complete":
				return COMPLETE;
			default:
				return INSURANCE_INFO;
		}
	}
}
