package it.yolo.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "anag_states", schema = "`order`", uniqueConstraints = @UniqueConstraint(columnNames = { "state" }))
public class AnagStatesEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "state", nullable = false)
    private String state;

    @Getter
    @Setter
    @Column(name = "created_by")
    private String createdBy;

    @Getter
    @Setter
    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @OneToMany(mappedBy = "anagStates")
    List<OrderEntity> list= new ArrayList<>();

}
