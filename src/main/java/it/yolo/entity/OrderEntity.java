package it.yolo.entity;

import javax.persistence.*;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkiverse.hibernate.types.json.JsonTypes;
import it.yolo.model.AnswerResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@ToString
@Table(name = "orders", schema = "\"order\"")
public class OrderEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "order_code", nullable = false)
    private String orderCode;

    @Getter
    @Setter
    @Column(name = "policy_code", nullable = false)
    private String policyCode;

    @Getter
    @Setter
    @ManyToOne
    @JoinColumn(name = "anag_state_id", nullable = false, columnDefinition = "int4")
    private AnagStatesEntity anagStates;

    @Getter
    @Setter
    @Column(name = "packet_id")
    private Integer packetId;

    @Getter
    @Setter
    @Column(name = "product_id", nullable = false)
    private Integer productId;



    @Getter
    @Setter
    @Column(name = "broker_id", nullable = false)
    private int brokerId;

    @Getter
    @Setter
    @Column(name = "company_id", nullable = false)
    private int companyId;

    @Getter
    @Setter
    @Column(name = "customer_id", nullable = false)
    private Integer customerId;

    @Getter
    @Setter
    @Column(name = "insurance_premium", columnDefinition = JpaUtils.NUMERIC_DEF)
    private BigDecimal insurancePremium;

    @Getter
    @Setter
    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Getter
    @Setter
    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Getter
    @Setter
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @OneToMany(fetch = FetchType.EAGER,mappedBy = "orders",cascade = CascadeType.PERSIST)
    private List<OrderItemEntity> orderItem = new ArrayList<>();

    @Getter
    @Setter
    @Column(name = "payment_transaction_id")
    private Integer paymentTransactionId;

    @Getter
    @Setter
    @Column(name = "payment_token")
    private String paymentToken;

    @Getter
    @Setter
    @Column(name = "product_type")
    private String productType;

    @Getter
    @Setter
    @Transient
    private String version;

    @Getter
    @Setter
    @Transient
    private String packetDurationDescription;

    @Getter
    @Setter
    @Transient
    private JsonNode chosenWarranties;

    @Getter
    @Setter
    @Transient
    private JsonNode resPg;

    @Getter
    @Setter
    @Column(name = "payment_type")
    private String paymentType;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(name = "field_to_recover")
    private JsonNode fieldToRecover;
    
    @Getter
    @Setter
    @Column(name = "language")
    private String language;

    @Getter
    @Setter
    @Column(name = "agenzia_di_riferimento")
    private String agenziaDiRiferimento;

    @Getter
    @Setter
    @Column(name = "utm_source")
    private String utmSource;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(name = "anonymous_user_data")
    private JsonNode anonymousUserData;


    @Getter
    @Setter
    @Column(name = "parent_order")
    private String parentOrder;

    @Getter
    @Setter
    @Column(name = "intermediary_order")
    private Boolean intermediaryOrder;

    @Getter
    @Setter
    @Transient
    private String productCode;

    @Getter
    @Setter
    @Transient
    private List<String> stepState;

    @Getter
    @Setter
    @Transient
    private String paymentFrequency;

    @Getter
    @Setter
    @Transient
    private AnswerResponse survey;

    @Getter
    @Setter
    private String session_id;
}
