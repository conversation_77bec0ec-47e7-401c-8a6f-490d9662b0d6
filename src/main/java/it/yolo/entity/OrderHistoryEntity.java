package it.yolo.entity;

import it.yolo.constants.StepStateEnum;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "order_history", schema = "\"order\"")
public class OrderHistoryEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "created_by")
    private String createdBy;

    @Getter
    @Setter
    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @Column(name = "order_code")
    private String orderCode;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    @Column(name = "step_state")
    private StepStateEnum stepState;

    @Getter
    @Setter
    @Column(name = "order_state")
    private String orderState;
}
