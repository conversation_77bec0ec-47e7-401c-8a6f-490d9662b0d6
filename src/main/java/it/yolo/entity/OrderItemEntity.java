package it.yolo.entity;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkiverse.hibernate.types.json.JsonTypes;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "order_item", schema = "\"order\"")
public class OrderItemEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "price", nullable = false)
    private String price;

    @Getter
    @Setter
    @Column(name = "annual_price")
    private String annualPrice;

    @Getter
    @Setter
    @Column(name = "product_id", nullable = false)
    private int product_id;

    @Getter
    @Setter
    @Column(name = "policy_number", nullable = false)
    private String policy_number;


    @Getter
    @Setter
    @Column(name = "master_policy_number", nullable = false)
    private String master_policy_number;

    @Getter
    @Setter
    @Column(name = "external_id", nullable = false)
    private String external_id;

    @Getter
    @Setter
    @Column(name = "state", nullable = false)
    private String state;


    @Getter
    @Setter
    @Column(name = "start_date", nullable = false)
    private LocalDateTime start_date;

    @Getter
    @Setter
    @Column(name = "expiration_date", nullable = false)
    private LocalDateTime expiration_date;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(columnDefinition = "jsonb not null")
    private JsonNode insured_item;

    @Getter
    @Setter
    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Getter
    @Setter
    @Transient
    private Integer days;

    @Getter
    @Setter
    @ManyToOne()
    @JoinColumn(name = "order_id")
    private OrderEntity orders;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(columnDefinition = "jsonb")
    private JsonNode quotation;

    @Getter
    @Setter
    @Column(name = "packet_id", nullable = false)
    private Integer packetId;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(name = "emission", nullable = false)
    private JsonNode emission;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(name = "promotion")
    private JsonNode promotion;


    @Getter
    @Setter
    @Transient
    private Boolean instant;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column(name = "instance", nullable = false)
    private JsonNode instance;

    @Getter
    @Setter
    @Transient
    private JsonNode ceilings;
}
