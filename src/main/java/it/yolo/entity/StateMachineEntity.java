package it.yolo.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.*;

@Entity
@ToString
@Table(name = "orders", schema = "`state_machine_workflow`")
public class StateMachineEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "order_code", nullable = false)
    private String order_code;

    @Getter
    @Setter
    @Column(name = "product_code")
    private String product_code;

    @Getter
    @Setter
    @Column(name = "current_state")
    private String current_state;

    @Getter
    @Setter
    @Column(name = "next_state")
    private String next_state;

}
