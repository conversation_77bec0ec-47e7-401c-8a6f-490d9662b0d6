package it.yolo.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "workflow", schema = "\"order\"")
public class WorkflowEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "product_id")
    private Integer productId;

    @Getter
    @Setter
    @Column(name = "workflow")
    private String workflow;

    @Getter
    @Setter
    @Column(name = "enable")
    private boolean enable;
}
