package it.yolo.exception;

public class EntityNotFoundException extends YoloException {

	public EntityNotFoundException(String title, String description) {
		super(title, description);
	}
	
	/**
	 * constructor that uses "Entity not found exception" as title
	 * and description provided
	 * @param description
	 */
	public EntityNotFoundException(String description) {
		super("Entity not found exception", description);
	}

	private static final long serialVersionUID = 700275747604686267L;

}
