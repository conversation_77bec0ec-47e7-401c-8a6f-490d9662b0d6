package it.yolo.exception;

import it.yolo.model.ErrorResponse;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.resteasy.reactive.server.ServerExceptionMapper;

public class ExceptionMappers {

    private static final Logger LOGGER = Logger.getLogger(ExceptionMappers.class);

//    @ServerExceptionMapper(priority = 4001)
    @ServerExceptionMapper(YoloException.class)
    public Response mapException(
            YoloException ex) {
        LOGGER.error(ex.getMessage(), ex);
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(new ErrorResponse(ex.getTitle(), ex.getDescription())).build();
    }

    @ServerExceptionMapper(Exception.class)
    public Response mapException(Exception ex) {
        LOGGER.error(ex.getMessage(), ex);
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(new ErrorResponse(ex.getMessage(), ex.getStackTrace().toString())).build();
    }

    @ServerExceptionMapper(WarrantyRulesClientException.class)
    public Response mapException(WarrantyRulesClientException ex) {
        LOGGER.error(ex.getMessage(), ex);
        return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse(ex.getMessage(), ex.getStackTrace().toString())).build();
    }

    @ServerExceptionMapper(InsuredAgeNotMatchedException.class)
    public Response mapException(InsuredAgeNotMatchedException ex) {
        LOGGER.error(ex.getMessage(), ex);
        return Response.status(Response.Status.BAD_REQUEST)
                .entity(new ErrorResponse(ex.getErrorCode(), ex.getMessage()))
                .type(MediaType.APPLICATION_JSON)
                .build();
    }


}
