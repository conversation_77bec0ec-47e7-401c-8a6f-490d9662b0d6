package it.yolo.exception;

import com.fasterxml.jackson.databind.JsonNode;

public class WarrantyRulesClientException extends YoloException{

	private JsonNode body;

	public WarrantyRulesClientException(String title, String descritpion, JsonNode body) {
		super(title, descritpion);
		this.body=body;
	}

	public JsonNode getBody() {
		return body;
	}

	public void setBody(JsonNode body) {
		this.body = body;
	}

	private static final long serialVersionUID = -8288274071302200385L;
}
