package it.yolo.machine.step.concrete;

import it.yolo.entity.StateMachineEntity;
import it.yolo.machine.step.Step;
import it.yolo.machine.step.contex.Order;
import it.yolo.repository.StateMachineRepository;

import javax.inject.Inject;

public class Addres extends Step {
    @Inject
    StateMachineRepository stateMachineRepository;
    @Override
    public void next(Order order) {
        StateMachineEntity result= stateMachineRepository.find("select distinct o from Order o where o.current_states='InsuranceInfo' and o.order_code= :order_code").firstResult();
        if(result!=null){
            // set prossimo stato
        }else{
            prev(order);
        }

    }

    @Override
    public void prev(Order order) {
        order.setStep(new InsuranceInfo());
    }

    @Override
    public void actual(Order order) {

    }
}
