package it.yolo.machine.step.concrete;

import it.yolo.entity.StateMachineEntity;
import it.yolo.machine.step.Step;
import it.yolo.machine.step.contex.Order;
import it.yolo.repository.StateMachineRepository;

import javax.inject.Inject;

public class InsuranceInfo extends Step {

    @Inject
    StateMachineRepository stateMachineRepository;

    @Override
    public void next(Order order) {
        StateMachineEntity stateMachineEntity = new StateMachineEntity();
        stateMachineEntity.setOrder_code(order.getOrderCode());
        stateMachineEntity.setCurrent_state("InsuranceInfo");// QUI POTREBBERO ESSERE CONFIGURATI A DB
        stateMachineEntity.setNext_state("Addres");// QUI POTREBBERO ESSERE CONFIGURATI A DB
        stateMachineRepository.persist(stateMachineEntity);
    }

    @Override
    public void prev(Order order) {
        System.out.println("The package is in its root state.");
    }

    @Override
    public void actual(Order order) {
        System.out.println("The package is in its root state.");
    }
}
