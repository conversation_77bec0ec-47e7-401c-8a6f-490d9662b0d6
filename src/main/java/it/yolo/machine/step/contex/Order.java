package it.yolo.machine.step.contex;

import it.yolo.machine.step.Step;
import it.yolo.machine.step.concrete.InsuranceInfo;
public class Order {
    private Step stepState = new InsuranceInfo();
    private int customerId;
    private String orderCode;
    private String policyCode;
    private Long anagStatesId;
    private int productId;

    /***
     *
     *  GETTER / SETTERS
     */

            public int getCustomerId() {
                return customerId;
            }

            public void setCustomerId(int customerId) {
                this.customerId = customerId;
            }

            public String getOrderCode() {
                return orderCode;
            }

            public void setOrderCode(String orderCode) {
                this.orderCode = orderCode;
            }

            public String getPolicyCode() {
                return policyCode;
            }

            public void setPolicyCode(String policyCode) {
                this.policyCode = policyCode;
            }

            public Long getAnagStatesId() {
                return anagStatesId;
            }

            public void setAnagStatesId(Long anagStatesId) {
                this.anagStatesId = anagStatesId;
            }

            public int getProductId() {
                return productId;
            }

            public void setProductId(int productId) {
                this.productId = productId;
            }

    /***
     * METHOD FOR STATE_MACHINE
     */
            public void previousState() {
                stepState.prev(this);
            }

            public void nextState() {
                stepState.next(this);
            }

            public void actualStatus() {
                stepState.actual(this);
            }


    /***
     * GETTERS/SETTERS STATE MACHINE STATE
     */
            public Step getStep() {
                return stepState;
            }

            public void setStep(Step step) {
                this.stepState = step;
            }
}
