package it.yolo.machine.step.state;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import it.yolo.constants.StepStateEnum;

/**
 * trasform a step state in its next
 */
public class StepStateTransformator {
	
	private StepStateTransformator() {
		super();
	}
	
	/**
	 * transofmr a state in its next
	 * @param state
	 * @return Strinc
	 * @deprecated 11/01/2023 
	 */
	@Deprecated
	public static StepStateEnum transformState(StepStateEnum state) {
		StepStateEnum next = StepStateEnum.INSURANCE_INFO;
		
		if(state != null) {
			int nextVal = state.getWeight()+1;
			next = StepStateEnum.getByValue(nextVal);
		}
		
		return next;
	}

	/**
	 * 
	 * @param workflow
	 * @return
	 */
	public static StepStateEnum getNextState(String actualStepState, String[] workflow) {
		if(workflow == null || workflow.length == 0 || StringUtils.isBlank(actualStepState)) return StepStateEnum.INSURANCE_INFO;
		
		List<String> asList = Arrays.asList(workflow);
		int actualIndex = asList.indexOf(actualStepState);
		
		int nextIndex = actualIndex+1;
		if(nextIndex >= asList.size()) nextIndex = asList.size()-1;
		
		return StepStateEnum.getByString(asList.get(nextIndex));
	}
}
