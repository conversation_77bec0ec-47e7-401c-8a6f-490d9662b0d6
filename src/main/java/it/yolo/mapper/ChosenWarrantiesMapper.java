package it.yolo.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.model.ChosenWarranties;
import it.yolo.model.ChosenWarrantiesData;
import it.yolo.model.ChosenWarrantiesWrapper;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * Mapper MapStruct per la conversione di ChosenWarranties.
 * Gestisce la conversione da JsonNode ai DTO e viceversa.
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface ChosenWarrantiesMapper {

    ChosenWarrantiesMapper INSTANCE = Mappers.getMapper(ChosenWarrantiesMapper.class);

    /**
     * Mappa da ChosenWarrantiesWrapper a ChosenWarranties.
     */
    @Mapping(target = "packetId", source = "data.packetId")
    @Mapping(target = "warranties", source = "data.warranties")
    @Mapping(target = "skipRules", source = "skipRules")
    @Mapping(target = "justRemovedId", source = "justRemovedId")
    ChosenWarranties wrapperToChosenWarranties(ChosenWarrantiesWrapper wrapper);

    /**
     * Mappa da ChosenWarranties a ChosenWarrantiesWrapper.
     */
    @Mapping(target = "data.packetId", source = "packetId")
    @Mapping(target = "data.warranties", source = "warranties")
    @Mapping(target = "skipRules", source = "skipRules")
    @Mapping(target = "justRemovedId", source = "justRemovedId")
    ChosenWarrantiesWrapper chosenWarrantiesToWrapper(ChosenWarranties chosenWarranties);

    /**
     * Mappa da ChosenWarrantiesData a ChosenWarranties.
     */
    @Mapping(target = "packetId", source = "packetId")
    @Mapping(target = "warranties", source = "warranties")
    @Mapping(target = "skipRules", ignore = true)
    @Mapping(target = "justRemovedId", ignore = true)
    ChosenWarranties dataToChosenWarranties(ChosenWarrantiesData data);



    /**
     * Metodo principale per mappare da JsonNode a ChosenWarranties.
     * Utilizza ObjectMapper configurato per gestire le date e poi MapStruct per il mapping.
     */
    default ChosenWarranties mapFromJsonNode(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }

        ObjectMapper objectMapper = createConfiguredObjectMapper();

        try {
            // Prova prima con la struttura wrapper
            if (jsonNode.has("data")) {
                ChosenWarrantiesWrapper wrapper = objectMapper.treeToValue(jsonNode, ChosenWarrantiesWrapper.class);
                return wrapperToChosenWarranties(wrapper);
            } else {
                // Prova con la struttura diretta
                ChosenWarrantiesData data = objectMapper.treeToValue(jsonNode, ChosenWarrantiesData.class);
                return dataToChosenWarranties(data);
            }
        } catch (Exception e) {
            System.err.println("Errore nel mapping da JsonNode: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Crea un ObjectMapper configurato per gestire le date Java 8.
     */
    default ObjectMapper createConfiguredObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // Configurazione per gestire le date senza moduli aggiuntivi
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false);
        return mapper;
    }

    /**
     * Metodo per convertire ChosenWarranties in JsonNode per il nodo instance.
     * Costruisce manualmente il JsonNode includendo SOLO i campi da persistere:
     * - packetId
     * - warranties
     *
     * I campi skipRules e justRemovedId sono esclusi perché sono solo per la logica di business.
     */
    default JsonNode mapToJsonNode(ChosenWarranties chosenWarranties) {
        if (chosenWarranties == null) {
            return null;
        }

        ObjectMapper objectMapper = createConfiguredObjectMapper();

        try {
            // Costruisce manualmente il JsonNode con solo i campi da persistere
            ObjectNode rootNode = objectMapper.createObjectNode();

            // Crea il nodo "data" con i campi essenziali per la persistenza
            ObjectNode dataNode = objectMapper.createObjectNode();

            if (chosenWarranties.getPacketId() != null) {
                dataNode.put("packetId", chosenWarranties.getPacketId());
            }

            if (chosenWarranties.getWarranties() != null) {
                JsonNode warrantiesNode = objectMapper.valueToTree(chosenWarranties.getWarranties());
                dataNode.set("warranties", warrantiesNode);
            }

            rootNode.set("data", dataNode);

            // NON aggiungiamo skipRules e justRemovedId perché sono solo per la logica di business

            return rootNode;
        } catch (Exception e) {
            System.err.println("Errore nella conversione ChosenWarranties -> JsonNode: " + e.getMessage());
            return null;
        }
    }

    /**
     * Verifica se un JsonNode rappresenta una struttura valida di chosenWarranties.
     * Migliorato: controlla anche che il nodo warranties sia un array non vuoto.
     */
    default boolean isValidChosenWarrantiesStructure(JsonNode node) {
        if (node == null || node.isNull()) {
            return false;
        }
        // Verifica struttura diretta
        if (node.has("warranties") || node.has("packetId") || node.has("skipRules")) {
            if (node.has("warranties")) {
                JsonNode warrantiesNode = node.get("warranties");
                if (!warrantiesNode.isArray() || warrantiesNode.isEmpty()) {
                    return false;
                }
            }
            return true;
        }
        // Verifica struttura con wrapper "data"
        if (node.has("data")) {
            JsonNode dataNode = node.get("data");
            if (dataNode.has("warranties")) {
                JsonNode warrantiesNode = dataNode.get("warranties");
                if (!warrantiesNode.isArray() || warrantiesNode.isEmpty()) {
                    return false;
                }
            }
            return dataNode.has("warranties") || dataNode.has("packetId");
        }
        return false;
    }

    /**
     * Verifica se un JsonNode rappresenta una struttura valida di chosenWarranties nullable.
     */
    default boolean isChosenWarrantiesNullable(JsonNode node) {
        if (node == null || node.isNull()) {
            return false;
        }
        // Verifica struttura con wrapper "data"
        if (node.has("data")) {
            JsonNode dataNode = node.get("data");
            if (dataNode.has("warranties") && dataNode.has("packetId")) {
               return true;
            }
        }
        return false;
    }
}
