package it.yolo.mapper;

import it.yolo.entity.OrderItemEntity;
import it.yolo.model.OrderItemBoundaryRequest;
import it.yolo.model.OrderItemBoundaryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderItemMapper extends GenericBoundaryMapper<OrderItemBoundaryRequest, OrderItemBoundaryResponse, OrderItemEntity> {

    OrderItemMapper INSTANCE = Mappers.getMapper(OrderItemMapper.class);

    @Override
    List<OrderItemBoundaryResponse> entitiesToResponses(List<OrderItemEntity> entity);
}
