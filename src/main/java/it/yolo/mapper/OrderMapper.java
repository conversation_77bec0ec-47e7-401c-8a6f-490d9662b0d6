package it.yolo.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import it.yolo.entity.OrderEntity;
import it.yolo.model.OrderBoundaryRequest;
import it.yolo.model.OrderBoundaryResponse;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderMapper extends
                GenericBoundaryMapper<OrderBoundaryRequest, OrderBoundaryResponse, OrderEntity> {
        OrderMapper INSTANCE = Mappers.getMapper(OrderMapper.class);

        @Mapping(target = "anagStates.id", source = "anagStatesId")
        @Mapping(target = "anagStates.state", source = "anagState")
        @Override
        OrderEntity requestToEntity(OrderBoundaryRequest request);

}
