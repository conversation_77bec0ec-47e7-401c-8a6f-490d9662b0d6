package it.yolo.mapper.custom;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.mapper.ChosenWarrantiesMapper;
import it.yolo.model.ChosenWarranties;

/**
 * Wrapper per il mapper MapStruct ChosenWarrantiesMapper.
 * Mantiene la compatibilità con il codice esistente.
 */
public class MapperChosenWarranties {

    /**
     * Mappa un JsonNode in ChosenWarranties utilizzando MapStruct.
     */
    public static ChosenWarranties mapChosenWarranties(JsonNode chosenWarranties) {
        return ChosenWarrantiesMapper.INSTANCE.mapFromJsonNode(chosenWarranties);
    }

    /**
     * Converte un DTO ChosenWarranties di nuovo in JsonNode.
     * Utilizza MapStruct per la conversione.
     */
    public static JsonNode mapToJsonNode(ChosenWarranties chosenWarranties) {
        return ChosenWarrantiesMapper.INSTANCE.mapToJsonNode(chosenWarranties);
    }

    /**
     * Verifica se un JsonNode rappresenta una struttura valida di chosenWarranties.
     * Utilizza MapStruct per la validazione.
     */
    public static boolean isValidChosenWarrantiesStructure(JsonNode node) {
        return ChosenWarrantiesMapper.INSTANCE.isValidChosenWarrantiesStructure(node);
    }

    /**
     * Verifica se un JsonNode rappresenta una struttura valida di chosenWarranties nullable.
     * Utilizza MapStruct per la validazione.
     */
    public static boolean isChosenWarrantiesNullable(JsonNode node) {
        return ChosenWarrantiesMapper.INSTANCE.isChosenWarrantiesNullable(node);
    }

    /**
     * Metodo di utilità per il debugging - stampa informazioni sulla struttura mappata.
     * Utilizza i metodi di utilità del DTO ChosenWarranties.
     */
    public static void logMappingResult(ChosenWarranties chosenWarranties) {
        if (chosenWarranties == null) {
            System.out.println("ChosenWarranties: null");
            return;
        }

        System.out.println("ChosenWarranties mappate con MapStruct:");
        System.out.println("  - PacketId: " + chosenWarranties.getPacketId());
        System.out.println("  - SkipRules: " + chosenWarranties.getSkipRules());
        System.out.println("  - Numero warranties: " + chosenWarranties.getWarrantiesCount());

        if (chosenWarranties.hasWarranties()) {
            System.out.println("  - Warranties obbligatorie: " + chosenWarranties.getMandatoryWarranties().size());
            System.out.println("  - Warranties opzionali: " + chosenWarranties.getOptionalWarranties().size());

            for (int i = 0; i < chosenWarranties.getWarranties().size(); i++) {
                it.yolo.model.RelatedWarranty warranty = chosenWarranties.getWarranties().get(i);
                System.out.println("    [" + i + "] ID: " + warranty.getId() +
                                 ", Name: " + warranty.getName() +
                                 ", Mandatory: " + warranty.getMandatory());
            }
        }
    }
}
