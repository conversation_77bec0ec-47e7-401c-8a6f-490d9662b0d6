package it.yolo.mapper.custom;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.document.dto.*;
import it.yolo.client.response.client.packet.Product;
import it.yolo.client.response.client.packet.Split;
import it.yolo.client.response.client.product.Data;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.common.Utility;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.OrderEx;
import it.yolo.mapper.OrderMapper;
import it.yolo.model.OrderBoundaryResponse;
import it.yolo.records.Packet;
import it.yolo.service.OrderItemUpdateService;
import it.yolo.service.v3.OrderService;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *  implementazione doc senza l'utilizzo di mapStruct
 */
@ApplicationScoped
public class MapperOrder {

    @Inject
    OrderItemUpdateService orderItemUpdateService;

    public static OrderItemEntity packetToOrderEntity(Packet packet){
        OrderItemEntity orderEntity = new OrderItemEntity();
        orderEntity.setPrice(String.valueOf(packet.packetResponse().getData().getPrice()));
        return orderEntity;
    };

    public static List<OrderItemEntity> packetsToOrderItems(List<Packet> list) {
        List<OrderItemEntity> listItem = new ArrayList<>();
        if (list.isEmpty()) {
            return null;
        }
        for (Packet packet : list) {
            OrderItemEntity orderItemEntity = new OrderItemEntity();
            if (!packet.packetResponse().getData().getPrice().toString().isBlank())
                orderItemEntity.setPrice(String.valueOf(packet.packetResponse().getData().getPrice()));
            listItem.add(orderItemEntity);
        }
        return listItem;
    }

    public static List<OrderItemEntity> OrderToOrderItemList(
            String price,
            String state,
            OrderEntity entity,
            Integer duration,
            String durationType,
            String productCode) {

        // logica per gestire i prezzi della quote sui prodotti
        Double priceFinal = Double.parseDouble(price);
        if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getPrice() != null) {
            priceFinal = Double.parseDouble(entity.getOrderItem().get(0).getPrice());
        }
        if(entity.getOrderItem().get(0).getQuantity()!=null) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getQuantity();
        }
        if(entity.getOrderItem().get(0).getDays()!=null) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getDays();
        }
        List<OrderItemEntity> listItem = new ArrayList<>();
        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setState(state);
        orderItemEntity.setOrders(entity);
        orderItemEntity.setPrice(String.valueOf(priceFinal));

        if (!entity.getOrderItem().isEmpty()) {
            if (entity.getOrderItem().get(0).getStart_date() == null) {
                entity.getOrderItem().get(0).setStart_date(Utility.setStartDate(productCode));
                orderItemEntity.setStart_date(Utility.setStartDate(productCode));
            } else {
                orderItemEntity.setStart_date(entity.getOrderItem().get(0).getStart_date());
            }
            if (StringUtils.isNotBlank(durationType)) {
                orderItemEntity.setExpiration_date(
                        Utility.getOrderDuration(
                                entity.getOrderItem().get(0).getStart_date(),
                                duration,
                                durationType));
            }
        }

        if (entity.getOrderItem().size() > 0) {
            orderItemEntity.setQuantity(entity.getOrderItem().get(0).getQuantity());
        }
        orderItemEntity.setProduct_id(entity.getProductId());
        listItem.add(orderItemEntity);
        return listItem;
    }

    public static List<OrderItemEntity> OrderToOrderItemListV2(Packet packet, String state, OrderEntity entity) {
        // logica per gestire i prezzi della quote sui prodotti
        Double priceFinal = packet.packetResponse().getData().getPacketPremium();

        if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getPrice() != null) {
            priceFinal = Double.parseDouble(entity.getOrderItem().get(0).getPrice());
        }
        if (entity.getOrderItem().get(0).getQuantity() != null && !packet.packetResponse().getData().getProduct().getCode().startsWith("winter-sport")) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getQuantity();
        }
        if (entity.getOrderItem().get(0).getDays() != null) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getDays();
        }
        List<OrderItemEntity> listItem = new ArrayList<>();
        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setState(state);
        orderItemEntity.setOrders(entity);
        orderItemEntity.setPrice(String.valueOf(priceFinal));
        orderItemEntity.setPacketId(entity.getPacketId());

        /*
        if(entity.getOrderItem().get(0).getInsured_item()!=null){
            orderItemEntity.setInsured_item(entity.getOrderItem().get(0).getInsured_item());
        }
        */

        // aggiunto or siccome il seasonal non gestisce i days
        if (!packet.packetResponse().getData().getSku().startsWith("tim-for-ski")
        || packet.packetResponse().getData().getName().equalsIgnoreCase("Seasonal")){
            if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getStart_date() == null) {
                if (packet.packetResponse().getData().getFixedStartDate() != null) {
                    orderItemEntity.setStart_date(packet.packetResponse().getData().getFixedStartDate());
                } else {
                    entity.getOrderItem().get(0).setStart_date(Utility.setStartDate(packet.packetResponse().getData().getProduct().getCode()));
                    orderItemEntity.setStart_date(Utility.setStartDate(packet.packetResponse().getData().getProduct().getCode()));
                }
            } else {
                orderItemEntity.setStart_date(entity.getOrderItem().get(0).getStart_date());
            }
            if (packet.packetResponse().getData().getFixedEndDate() != null) {
                orderItemEntity.setExpiration_date(packet.packetResponse().getData().getFixedEndDate());

            } else {
//            if(entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getStart_date() != null){
//
//            }
                if (packet.packetResponse().getData().getDuration() != null
                        && packet.packetResponse().getData().getDurationType() != null) {
                    String productCode = packet.packetResponse().getData().getProduct().getCode();
                    orderItemEntity.setExpiration_date(
                            Utility.getOrderDuration(
                                    Utility.setStartDate(productCode),
                                    entity.getOrderItem().get(0).getDays() != null ? entity.getOrderItem().get(0).getDays() :
                                            Integer.parseInt(packet.packetResponse().getData().getDuration()),
                                    packet.packetResponse().getData().getDurationType()));
                } else if (packet.packetResponse().getData().getPacketDuration() != null
                        && packet.packetResponse().getData().getPacketDuration().get("packet_duration") != null) {
                    String productCode = packet.packetResponse().getData().getProduct().getCode();
                    orderItemEntity.setExpiration_date(
                            Utility.getOrderDuration(
                                    Utility.setStartDate(productCode),
                                    packet.packetResponse().getData().getPacketDuration().get("packet_duration").get(0).get("duration").asInt(),
                                    packet.packetResponse().getData().getPacketDuration().get("packet_duration").get(0).get("durationType").asText()));
                }
            }
        }
        else{
            entity.getOrderItem().get(0).setStart_date(checkStartDateTimSki(packet.packetResponse().getData().getSku(), entity.getOrderItem().get(0).getStart_date()));
            orderItemEntity.setStart_date(checkStartDateTimSki(packet.packetResponse().getData().getSku(), entity.getOrderItem().get(0).getStart_date()));
            entity.getOrderItem().get(0).setExpiration_date(checkExpirationDateTimSki(packet.packetResponse().getData().getSku(),
                    entity.getOrderItem().get(0).getDays()-1 , entity.getOrderItem().get(0).getStart_date(),
                    entity.getOrderItem().get(0).getExpiration_date()));
            orderItemEntity.setExpiration_date(checkExpirationDateTimSki(packet.packetResponse().getData().getSku(),
                    entity.getOrderItem().get(0).getDays()-1, entity.getOrderItem().get(0).getStart_date(),
                    entity.getOrderItem().get(0).getExpiration_date()));
        }
        if(entity.getOrderItem().size() > 0) {
            orderItemEntity.setQuantity(entity.getOrderItem().get(0).getQuantity());
        }
        orderItemEntity.setProduct_id(entity.getProductId());
        listItem.add(orderItemEntity);
        return listItem;
    }

    //
    public ProductResponse productToProductResponse(Product product) {

        ProductResponse productResponse = new ProductResponse();
        productResponse.setData(new Data());

        productResponse.getData().setId(product.getId());
        productResponse.getData().setCode(product.getCode());
        productResponse.getData().setDescription(product.getDescription());
        productResponse.getData().setStartDate(product.getStartDate());
        productResponse.getData().setRecurring(product.getRecurring());
        productResponse.getData().setInsuranceCompany(product.getInsuranceCompany());

        List<Object> splitObjects = new ArrayList<>();
        List<Split> splits = product.getSplits();
        for (Split split : splits) {
            Object splitObject = split;
            splitObjects.add(splitObject);
        }
        productResponse.getData().setSplits(splitObjects);

        productResponse.getData().setConfiguration(product.getConfiguration());
        productResponse.getData().setProductType(product.getProductType());

        return productResponse;
    }

    //TODO logica da modificare creata per gestire le date per rilascio in produzione
    private static LocalDateTime checkStartDateTimSki(String sku, LocalDateTime startDate){
        if(startDate==null) {
            switch (sku) {
                case "tim-for-ski-silver-day":
                case "tim-for-ski-gold-day":
                case "tim-for-ski-platinum-day":
                    return LocalDateTime.now();
            }
        }
        return startDate;
    }

    //TODO logica da modificare creata per gestire le date per rilascio in produzione
    private static LocalDateTime checkExpirationDateTimSki(String sku, Integer daysNumber, LocalDateTime startDate, LocalDateTime expirationDate) {
        if (daysNumber != null) {
            switch (sku) {
                case "tim-for-ski-silver-day":
                case "tim-for-ski-gold-day":
                case "tim-for-ski-platinum-day":
                    return startDate.toLocalDate().plusDays(daysNumber +1).atStartOfDay().minus(1,ChronoUnit.MILLIS);
            }
        }
        return expirationDate;
    }

    public static List<OrderItemEntity> OrderToOrderItemListV3(Packet packet, String state, OrderEntity entity) throws OrderEx {
        // logica per gestire i prezzi della quote sui prodotti
        Double priceFinal = packet.packetResponse().getData().getPacketPremium();

        if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getPrice() != null) {
            priceFinal = Double.parseDouble(entity.getOrderItem().get(0).getPrice());
        }
        if (entity.getOrderItem().get(0).getQuantity() != null) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getQuantity();
        }
        if (entity.getOrderItem().get(0).getDays() != null) {
            priceFinal = priceFinal * entity.getOrderItem().get(0).getDays();
        }
        List<OrderItemEntity> listItem = new ArrayList<>();
        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setState(state);
        orderItemEntity.setOrders(entity);
        orderItemEntity.setPrice(String.valueOf(priceFinal));
        orderItemEntity.setPacketId(entity.getPacketId());

        /*
        if(entity.getOrderItem().get(0).getInsured_item()!=null){
            orderItemEntity.setInsured_item(entity.getOrderItem().get(0).getInsured_item());
        }
        */

        // aggiunto or siccome il seasonal non gestisce i days
        Product product=packet.packetResponse().getData().getProduct();
        JsonNode properties=product.getConfiguration().getProperties();
        if (!packet.packetResponse().getData().getSku().startsWith("tim-for-ski")
                || packet.packetResponse().getData().getName().startsWith("Seasonal")){
            if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getStart_date() == null) {
                if (packet.packetResponse().getData().getFixedStartDate() != null) {
                    orderItemEntity.setStart_date(packet.packetResponse().getData().getFixedStartDate());
                } else{
                    entity.getOrderItem().get(0).setStart_date(Utility.setStartDate(product.getCode()));
                    orderItemEntity.setStart_date(Utility.setStartDate(product.getCode()));
                }
            } else {
                orderItemEntity.setStart_date(entity.getOrderItem().get(0).getStart_date());
            }
            if (packet.packetResponse().getData().getFixedEndDate() != null) {
                orderItemEntity.setExpiration_date(packet.packetResponse().getData().getFixedEndDate());

            } else {
//            if(entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getStart_date() != null){
//
//            }
                if(entity.getOrderItem().get(0).getDays()!=null &&
                        properties.hasNonNull("durationFromDays") &&
                        properties.get("durationFromDays").asBoolean()){
                    orderItemEntity.setStart_date(entity.getOrderItem().get(0).getStart_date().toLocalDate().atStartOfDay());
                    orderItemEntity.setExpiration_date(orderItemEntity.getStart_date().toLocalDate().
                            plusDays(entity.getOrderItem().get(0).getDays()).atStartOfDay().minus(1, ChronoUnit.MILLIS));
                } else if (packet.packetResponse().getData().getDuration() != null
                        && packet.packetResponse().getData().getDurationType() != null) {
                    String productCode = product.getCode();
                    orderItemEntity.setExpiration_date(
                            Utility.getOrderDuration(
                                    Utility.setStartDate(productCode),
                                    entity.getOrderItem().get(0).getDays() != null ? entity.getOrderItem().get(0).getDays() :
                                            Integer.parseInt(packet.packetResponse().getData().getDuration()),
                                    packet.packetResponse().getData().getDurationType()));
                } else if (packet.packetResponse().getData().getPacketDuration() != null
                        && packet.packetResponse().getData().getPacketDuration().get("packet_duration") != null 
                        && packet.packetResponse().getData().getPacketDuration().get("packet_duration").has("duration") 
                        && packet.packetResponse().getData().getPacketDuration().get("packet_duration").has("durationType")) {
                    String productCode = product.getCode();
                    orderItemEntity.setExpiration_date(
                            Utility.getOrderDuration(
                                    Utility.setStartDate(productCode),
                                    packet.packetResponse().getData().getPacketDuration().get("packet_duration").get(0).get("duration").asInt(),
                                    packet.packetResponse().getData().getPacketDuration().get("packet_duration").get(0).get("durationType").asText()));
                }
            }
        }
        else{
            entity.getOrderItem().get(0).setStart_date(checkStartDateTimSki(packet.packetResponse().getData().getSku(), entity.getOrderItem().get(0).getStart_date()));
            orderItemEntity.setStart_date(checkStartDateTimSki(packet.packetResponse().getData().getSku(), entity.getOrderItem().get(0).getStart_date()));
            entity.getOrderItem().get(0).setExpiration_date(checkExpirationDateTimSki(packet.packetResponse().getData().getSku(),
                    entity.getOrderItem().get(0).getDays()-1, entity.getOrderItem().get(0).getStart_date(),
                    entity.getOrderItem().get(0).getExpiration_date()));
            orderItemEntity.setExpiration_date(checkExpirationDateTimSki(packet.packetResponse().getData().getSku(),
                    entity.getOrderItem().get(0).getDays()-1, entity.getOrderItem().get(0).getStart_date(),
                    entity.getOrderItem().get(0).getExpiration_date()));
        }
        if(properties.hasNonNull("startAfterToday") && properties.get("startAfterToday").asBoolean() &&
                entity.getOrderItem().get(0).getStart_date().isBefore(LocalDate.now().plusDays(1).atStartOfDay().minusNanos(1))){
            throw new OrderEx("Order Exception", "Error updating order");
        }
        if(entity.getOrderItem().size() > 0) {
            orderItemEntity.setQuantity(entity.getOrderItem().get(0).getQuantity());
        }
        orderItemEntity.setInsured_item(entity.getOrderItem().get(0).getInsured_item());
        orderItemEntity.setProduct_id(entity.getProductId());
        if(orderItemEntity.getStart_date().toLocalDate().isAfter(LocalDate.now())){
            orderItemEntity.setStart_date(orderItemEntity.getStart_date().toLocalDate().atStartOfDay());
        }
        listItem.add(orderItemEntity);
        return listItem;
    }
    public List<OrderItemEntity> mapOrderToOrderItemListSecure(Packet packet, String state, OrderEntity entity) throws OrderEx {
        // Early exit: controlli null e lista vuota
        if (packet == null || packet.packetResponse() == null || packet.packetResponse().getData() == null) {
            throw new OrderEx("Order Exception","Packet non valido o dati mancanti");
        }
        if (entity == null || entity.getOrderItem() == null || entity.getOrderItem().isEmpty()) {
            throw new OrderEx("Order Exception", "OrderEntity o OrderItem mancante");
        }

        OrderItemEntity firstItem = entity.getOrderItem().get(0);
        Product product = packet.packetResponse().getData().getProduct();
        JsonNode properties = Optional.ofNullable(product)
                .map(Product::getConfiguration)
                .map(cfg -> cfg.getProperties())
                .orElse(null);

        // Costruzione OrderItemEntity
        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setState(state);
        orderItemEntity.setOrders(entity);
        orderItemEntity.setPacketId(entity.getPacketId());
        orderItemEntity.setInsured_item(firstItem.getInsured_item());
        orderItemEntity.setProduct_id(entity.getProductId());

        // Calcolo prezzo sicuro utilizzando il servizio
        double securePrice = orderItemUpdateService.calculateSecurePrice(packet, firstItem);
        orderItemEntity.setPrice(String.valueOf(securePrice));
        orderItemEntity.setQuantity(Optional.ofNullable(firstItem.getQuantity()).orElse(1));
        // Gestione date utilizzando il servizio
        orderItemUpdateService.handleTimSkiDates(orderItemEntity, packet, entity);
        orderItemUpdateService.handleStandardDates(orderItemEntity, packet, entity);

        // Validazione startAfterToday
        if (properties != null && properties.hasNonNull("startAfterToday") && properties.get("startAfterToday").asBoolean()) {
            LocalDateTime startDate = orderItemEntity.getStart_date();
            if (startDate != null && startDate.isBefore(LocalDate.now().plusDays(1).atStartOfDay().minusNanos(1))) {
                throw new OrderEx("Order Exception", "Start date non valida: deve essere successiva a oggi");
            }
        }

        // Normalizzazione start_date utilizzando il servizio
        orderItemUpdateService.normalizeStartDate(orderItemEntity);

        List<OrderItemEntity> listItem = new ArrayList<>();
        listItem.add(orderItemEntity);
        return listItem;
    }

    public CertificateRequestDto OrderToCertificateReq(OrderEntity resultDb, ProductResponse product, OrderEntity entity, String tenant) {
        CertificateRequestDto certificateReq = new CertificateRequestDto();
        OrderBoundaryResponse orderToResponse = OrderMapper.INSTANCE.entityToResponse(resultDb);
        OrderResponseDto orderResponse = new OrderResponseDto();
        orderResponse.setResponse(orderToResponse);
        certificateReq.setOrder(orderResponse);
        CustomerResponseDto customer = new CustomerResponseDto();
        DataCustomerResponseDto dataCustomer = new DataCustomerResponseDto();
        customer.setData(dataCustomer);
        if(entity!=null && entity.getAnonymousUserData()!=null && entity.getAnonymousUserData().has("primary_mail")) {
            customer.getData().setPrimaryMail(entity.getAnonymousUserData().get("primary_mail").asText());
            customer.getData().setPrimaryPhone(entity.getAnonymousUserData().get("primary_phone").asText());
        } else if(resultDb.getAnonymousUserData()!=null){
            customer.getData().setPrimaryMail(resultDb.getAnonymousUserData().get("primary_mail").asText());
            customer.getData().setPrimaryPhone(resultDb.getAnonymousUserData().get("primary_phone").asText());
        }
        certificateReq.setCustomer(customer);
        certificateReq.setProduct(product);
        certificateReq.setTenant(tenant);
        PolicyResponseDto policy = new PolicyResponseDto();
        DataPolicyResponse dataPolicy = new DataPolicyResponse();
        policy.setData(dataPolicy);
        policy.getData().setPolicyCode("");
        certificateReq.setPolicy(policy);
        return certificateReq;
    }
    public static OrderEntity updateEntityFromResult(OrderEntity resultDb,OrderEntity request){

        // duplicazione ordine con generazione nuovo numero di ordine.
        request.setProductId(resultDb.getProductId());
        request.setPacketId(resultDb.getPacketId());
        request.setCustomerId(resultDb.getCustomerId());
        request.setBrokerId(resultDb.getBrokerId());
        request.setInsurancePremium(resultDb.getInsurancePremium());
        request.setCreatedBy(resultDb.getCreatedBy());
        request.setUpdatedBy(resultDb.getUpdatedBy());
        request.setParentOrder(resultDb.getOrderCode());

        //set stato insurance
        request.getOrderItem().forEach(item -> {
            item.setState(OrderService.INSURANCE);
        });

        //verifica price
        request.getOrderItem().forEach(item -> {
            resultDb.getOrderItem().forEach(itemResult->{
                if(item.getProduct_id()==itemResult.getProduct_id()){
                    if(item.getPrice()==null || item.getPrice().isEmpty()){
                        item.setPrice(itemResult.getPrice());
                    }
                }
            });
        });

        request.getOrderItem().forEach(item -> {
            resultDb.getOrderItem().forEach(itemResult->{
                if(item.getProduct_id()==itemResult.getProduct_id()){
                    item.setPolicy_number(itemResult.getPolicy_number());
                    item.setMaster_policy_number(itemResult.getMaster_policy_number());
                    item.setExternal_id(itemResult.getExternal_id());
                    item.setStart_date(itemResult.getStart_date());
                    item.setExpiration_date(itemResult.getExpiration_date());
                    item.setInsured_item(itemResult.getInsured_item());
                    item.setQuantity(itemResult.getQuantity());
                    item.setQuotation(itemResult.getQuotation());
                    item.setEmission(itemResult.getEmission());
                    item.setOrders(request);
                }
            });
        });


        return request;
    }

}
