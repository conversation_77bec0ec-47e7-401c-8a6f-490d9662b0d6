package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDateTime;

@Schema(description = "Anag States entity")
public class AnagStatesBoundaryResponse {

    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "State")
    private String state;

    @Getter
    @Setter
    @Schema(description = "Created By")
    private String createdBy;

    @Getter
    @Setter
    @Schema(description = "Updated by")
    private String updatedBy;

    @Getter
    @Setter
    @Schema(description = "Created at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;
}
