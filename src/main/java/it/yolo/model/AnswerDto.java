package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Schema(description = "Answer DTO")
public class AnswerDto {

    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Product")
    private Long productId;

    @Getter
    @Setter
    @Schema(description = "Question")
    private Long questionId;

    @Getter
    @Setter
    @Schema(description = "Answer")
    private Long answerId;

    @Getter
    @Setter
    @Schema(description = "Customer")
    private Long customerId;

    @Getter
    @Setter
    @Schema(description = "orderCode")
    private String orderCode;

    @Getter
    @Setter
    @Schema(description = "Order Item")
    private Long orderItemId;
}
