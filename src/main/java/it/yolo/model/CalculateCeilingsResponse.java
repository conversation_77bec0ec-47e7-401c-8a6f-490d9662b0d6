package it.yolo.model;

import com.fasterxml.jackson.databind.node.ArrayNode;

public class CalculateCeilingsResponse {

    private boolean updated;

    private ArrayNode warranties;

    public boolean isUpdated() {
        return updated;
    }

    public void setUpdated(boolean updated) {
        this.updated = updated;
    }

    public ArrayNode getWarranties() {
        return warranties;
    }

    public void setWarranties(ArrayNode warranties) {
        this.warranties = warranties;
    }

    public CalculateCeilingsResponse() {
    }

    public CalculateCeilingsResponse(boolean updated, ArrayNode warranties) {
        this.updated = updated;
        this.warranties = warranties;
    }
}
