package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Setter;
import java.time.LocalDateTime;
import java.util.List;

@Setter
public class CheckExistingReq {

    @JsonProperty("customerId")
    private List<Long> customerId;

    @JsonProperty("productId")
    private Long productId;

    @JsonProperty("packetId")
    private Long packetId;

    @JsonProperty("destination")
    private String destination;

    @JsonProperty("start")
    private LocalDateTime start;

    @JsonProperty("end")
    private LocalDateTime end;
}
