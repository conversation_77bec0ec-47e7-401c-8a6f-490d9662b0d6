package it.yolo.model;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

public class ChosenWarranties {

    @Schema(description = "Packet")
    private Long packetId;

    @Schema(description = "Optional Id of the just removed relatedWarranty")
    private Long justRemovedId;

    @Schema(description = "Optional boolean for skipping validation rules")
    private Boolean skipRules;

    @Schema(description = "RelatedWarranty list")
    private List<RelatedWarranty> warranties;

    public Long getPacketId() {
        return packetId;
    }

    public void setPacketId(Long packetId) {
        this.packetId = packetId;
    }

    public Long getJustRemovedId() {
        return justRemovedId;
    }

    public void setJustRemovedId(Long justRemovedId) {
        this.justRemovedId = justRemovedId;
    }

    public Boolean getSkipRules() {
        return skipRules;
    }

    public void setSkipRules(Boolean skipRules) {
        this.skipRules = skipRules;
    }

    public List<RelatedWarranty> getWarranties() {
        return warranties;
    }

    public void setWarranties(List<RelatedWarranty> warranties) {
        this.warranties = warranties;
    }

    /**
     * Verifica se le regole di validazione devono essere saltate.
     */
    public boolean shouldSkipRules() {
        return skipRules != null && skipRules;
    }

    /**
     * Verifica se ci sono warranties presenti.
     */
    public boolean hasWarranties() {
        return warranties != null && !warranties.isEmpty();
    }

    /**
     * Ottiene il numero di warranties.
     */
    public int getWarrantiesCount() {
        return warranties != null ? warranties.size() : 0;
    }

    /**
     * Filtra le warranties per mandatory.
     */
    public List<RelatedWarranty> getMandatoryWarranties() {
        if (warranties == null) {
            return new java.util.ArrayList<>();
        }
        return warranties.stream()
                .filter(w -> w.getMandatory() != null && w.getMandatory())
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Filtra le warranties per optional (non mandatory).
     */
    public List<RelatedWarranty> getOptionalWarranties() {
        if (warranties == null) {
            return new java.util.ArrayList<>();
        }
        return warranties.stream()
                .filter(w -> w.getMandatory() == null || !w.getMandatory())
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public String toString() {
        return "ChosenWarranties{" +
                "packetId=" + packetId +
                ", justRemovedId=" + justRemovedId +
                ", skipRules=" + skipRules +
                ", warranties=" + (warranties != null ? warranties.size() + " warranties" : "null") +
                '}';
    }
}
