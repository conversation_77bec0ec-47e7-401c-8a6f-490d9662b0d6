package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * DTO che rappresenta la sezione "data" della struttura chosenWarranties.
 */
public class ChosenWarrantiesData {

    @JsonProperty("packetId")
    private Long packetId;

    @JsonProperty("warranties")
    private List<RelatedWarranty> warranties;

    @JsonProperty("informativa")
    private String informativa;

    @JsonProperty("packetCombination")
    private String packetCombination;

    public Long getPacketId() {
        return packetId;
    }

    public void setPacketId(Long packetId) {
        this.packetId = packetId;
    }

    public List<RelatedWarranty> getWarranties() {
        return warranties;
    }

    public void setWarranties(List<RelatedWarranty> warranties) {
        this.warranties = warranties;
    }

    public String getInformativa() {
        return informativa;
    }

    public void setInformativa(String informativa) {
        this.informativa = informativa;
    }

    public String getPacketCombination() {
        return packetCombination;
    }

    public void setPacketCombination(String packetCombination) {
        this.packetCombination = packetCombination;
    }

    /**
     * Verifica se ci sono warranties presenti.
     */
    public boolean hasWarranties() {
        return warranties != null && !warranties.isEmpty();
    }

    /**
     * Ottiene il numero di warranties.
     */
    public int getWarrantiesCount() {
        return warranties != null ? warranties.size() : 0;
    }

    /**
     * Filtra le warranties per mandatory.
     */
    public List<RelatedWarranty> getMandatoryWarranties() {
        if (warranties == null) {
            return new java.util.ArrayList<>();
        }
        return warranties.stream()
                .filter(RelatedWarranty::isMandatory)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Filtra le warranties per optional (non mandatory).
     */
    public List<RelatedWarranty> getOptionalWarranties() {
        if (warranties == null) {
            return new java.util.ArrayList<>();
        }
        return warranties.stream()
                .filter(RelatedWarranty::isOptional)
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public String toString() {
        return "ChosenWarrantiesData{" +
                "packetId=" + packetId +
                ", warranties=" + (warranties != null ? warranties.size() + " warranties" : "null") +
                ", informativa='" + informativa + '\'' +
                ", packetCombination='" + packetCombination + '\'' +
                '}';
    }
}
