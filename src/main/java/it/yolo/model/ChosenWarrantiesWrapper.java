package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO che rappresenta la struttura completa di chosenWarranties 
 * come viene inviata dal frontend, incluso il wrapper "data".
 */
public class ChosenWarrantiesWrapper {

    @JsonProperty("data")
    private ChosenWarrantiesData data;

    @JsonProperty("skipRules")
    private Boolean skipRules;

    @JsonProperty("justRemovedId")
    private Long justRemovedId;

    public ChosenWarrantiesData getData() {
        return data;
    }

    public void setData(ChosenWarrantiesData data) {
        this.data = data;
    }

    public Boolean getSkipRules() {
        return skipRules;
    }

    public void setSkipRules(Boolean skipRules) {
        this.skipRules = skipRules;
    }

    public Long getJustRemovedId() {
        return justRemovedId;
    }

    public void setJustRemovedId(Long justRemovedId) {
        this.justRemovedId = justRemovedId;
    }

    /**
     * Verifica se le regole di validazione devono essere saltate.
     */
    public boolean shouldSkipRules() {
        return skipRules != null && skipRules;
    }

    /**
     * Verifica se ci sono dati presenti.
     */
    public boolean hasData() {
        return data != null;
    }

    @Override
    public String toString() {
        return "ChosenWarrantiesWrapper{" +
                "data=" + data +
                ", skipRules=" + skipRules +
                ", justRemovedId=" + justRemovedId +
                '}';
    }
}
