package it.yolo.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

public class EmissionBoundaryRequest {

    @Getter
    @Setter
    @Schema(description = "Order item id")
    private Long Id;

    @Getter
    @Setter
    @Schema(description = "Order code")
    private String orderCode;

    @Getter
    @Setter
    @Schema(description = "Emission")
    private JsonNode emission;
}
