package it.yolo.model;

import io.quarkus.runtime.annotations.RegisterForReflection;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@RegisterForReflection
@Schema(description = "Error response")
public class ErrorResponse {
	
	public ErrorResponse(String error, String errorInfo) {
		super();
		this.error = error;
		this.errorInfo = errorInfo;
	}
	

	@Schema(description = "Error")
    private String error;

    @Schema(description = "Information")
    private String errorInfo;
	

    public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}
}
