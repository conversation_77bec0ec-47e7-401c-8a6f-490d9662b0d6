package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "Order data")
public class OrderBoundaryRequest {

    @Getter
    @Setter
    @Schema(description = "Customer ID")
    private Integer customerId;

    @Getter
    @Setter
    @Schema(description = "Order code")
    private String orderCode;

    @Getter
    @Setter
    @Schema(description = "Policy code")
    private String policyCode;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Anag States ID")
    private Long anagStatesId;

    @Getter
    @Setter
    @Schema(description = "Packet ID")
    private Integer packetId;

    @Getter
    @Setter
    @JsonAlias({"product_id"})
    @Schema(description = "Product ID")
    private Integer productId;

    @Getter
    @Setter
    @Schema(description = "asset of goods")
    private JsonNode asset;

    @Getter
    @Setter
    @Schema(description = "Broker ID")
    private int brokerId;

    @Getter
    @Setter
    @Schema(description = "Company ID")
    private int companyId;

    @Getter
    @Setter
    @Schema(description = "Insured item", example = "{}")
    private JsonNode insuredItem;


    @Getter
    @Setter
    @Schema(description = "Insurance premium")
    private double insurancePremium;

    @Getter
    @Setter
    @Schema(description = "Created by")
    private String createdBy;

    @Getter
    @Setter
    @Schema(description = "Updated by")
    private String updatedBy;

    @Getter
    @Setter
    @Schema(description = "Created at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    private Packet packet;


    @Getter
    @Setter
    private JsonNode fieldToRecover;

    @Getter
    @Setter
    private List<OrderItemEntity> orderItem = new ArrayList<>();

    @Getter
    @Setter
    @Schema(description = "Payment transaction id")
    private Integer paymentTransactionId;

    @Getter
    @Setter
    @Schema(description = "Payment type")
    private String paymentType;

    @Getter
    @Setter
    @Schema(description = "Payment token")
    private String paymentToken;

    @Getter
    @Setter
    @Schema(description = "Product Type")
    private String productType;

    @Getter
    @Setter
    @Schema(description = "Discount")
    private String discount;

    @Getter
    @Setter
    @Schema(description = "State")
    private String anagState;

    @Getter
    @Setter
    @Schema(description = "Payment Frequency")
    private String paymentFrequency;

    @Getter
    @Setter
    @Schema(description = "Packet Duration Description")
    private String packetDurationDescription;

    @Getter
    @Setter
    @Schema(description = "Chosen warranties")
    private JsonNode chosenWarranties;

    @Getter
    @Setter
    @Schema(description = "Utm source")
    private String utmSource;

    @Getter
    @Setter
    @Schema(description = "Agenzia di riferimento")
    private String agenziaDiRiferimento;

    @Getter
    @Setter
    @Schema(description = "Response PG")
    private JsonNode resPg;

    @Getter
    @Setter
    private JsonNode anonymousUserData;
}
