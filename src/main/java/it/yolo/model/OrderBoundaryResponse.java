package it.yolo.model;

import java.time.LocalDateTime;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.constants.StepStateEnum;
import it.yolo.entity.OrderHistoryEntity;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "Order entity")
public class OrderBoundaryResponse {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Order code")
    private String orderCode;

    @Getter
    @Setter
    @Schema(description = "Policy code")
    private String policyCode;

    @Getter
    @Setter
    @Schema(description = "Anag States Id")
    private AnagStatesBoundaryResponse anagStates;

    @Getter
    @Setter
    @Schema(description = "Packet ID")
    private int packetId;

    @Getter
    @Setter
    @Schema(description = "Product ID")
    private int productId;

    @Getter
    @Setter
    @Schema(description = "product for order")
    private String productCode;
    @Getter
    @Setter
    @Schema(description = "Broker ID")
    private int brokerId;

    @Getter
    @Setter
    @Schema(description = "Company ID")
    private int companyId;

    @Getter
    @Setter
    @Schema(description = "Customer ID")
    private int customerId;

    @Getter
    @Setter
    @Schema(description = "Insurance premium")
    private double insurancePremium;

    @Getter
    @Setter
    @Schema(description = "Created by")
    private String createdBy;

    @Getter
    @Setter
    @Schema(description = "Updated by")
    private String updatedBy;

    @Getter
    @Setter
    @Schema(description = "Created at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @Schema(description = "packet for order")
    private PacketResponse packet;

    @Getter
    @Setter
    @Schema(description = "Order history")
    private OrderHistoryEntity orderHistory;

    @Getter
    @Setter
    @Schema(description = "Step state")
    private List<String> stepState;

    @Getter
    @Setter
    @Schema(description = "Order item")
    private List<OrderItemReferenceBoundaryResponse> orderItem;

    @Getter
    @Setter
    @Schema(description = "product for order")
    private ProductResponse product;

    @Getter
    @Setter
    @Schema(description = "Payment Transaction Id")
    private Integer paymentTransactionId;

    @Getter
    @Setter
    @Schema(description = "Payment token")
    private String paymentToken;

    @Getter
    @Setter
    @Schema(description = "Product Type")
    private String productType;

    @Getter
    @Setter
    @Schema(description = "Payment type")
    private String paymentType;

    @Getter
    @Setter
    @Schema(description = "Version")
    private String version;

    @Getter
    @Setter
    private JsonNode fieldToRecover;

    @Getter
    @Setter
    @Schema(description = "Utm source")
    private String utmSource;

    @Getter
    @Setter
    @Schema(description = "Agenzia di riferimento")
    private String agenziaDiRiferimento;

    @Getter
    @Setter
    @Schema(description = "x-tenant-language")
    private String language;

    @Getter
    @Setter
    @Schema(description = "parentOrder")
    private String parentOrder;

    @Getter
    @Setter
    @Schema(description = "intermediaryOrder")
    private Boolean intermediaryOrder;
    @Getter
    @Setter
    private JsonNode anonymousUserData;
    @Getter
    @Setter
    private AnswerResponse survey;
}
