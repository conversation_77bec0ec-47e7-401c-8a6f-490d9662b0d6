package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDateTime;

@Schema(description = "Order History data")
public class OrderHistoryBoundaryRequest {
    @Getter
    @Setter
    @Schema(description = "Created By")
    private String createdBy;

    @Getter
    @Setter
    @Schema(description = "Updated By")
    private String updatedBy;

    @Getter
    @Setter
    @Schema(description = "Created at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @Schema(name = "Order Code")
    private String orderCode;

    @Getter
    @Setter
    @Schema(name = "Step State")
    private String stepState;

    @Getter
    @Setter
    @Schema(name = "Order State")
    private String orderState;

}
