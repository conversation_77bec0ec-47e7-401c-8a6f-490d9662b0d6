package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDateTime;

public class OrderHistoryBoundaryResponse {
    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Order Code")
    private String orderCode;

    @Getter
    @Setter
    @Schema(description = "Step State")
    private String stepState;

    @Getter
    @Setter
    @Schema(description = "Order State")
    private String orderState;


    @Getter
    @Setter
    @Schema(description = "Created By")
    private String createdBy;

    @Getter
    @Setter
    @Schema(description = "Updated by")
    private String updatedBy;

    @Getter
    @Setter
    @Schema(description = "Created at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @Schema(description = "Updated at")
    private LocalDateTime updatedAt;
}
