package it.yolo.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.persistence.*;
import java.time.LocalDateTime;

@Schema(description = "Order item entity")
public class OrderItemBoundaryResponse {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Price")
    private String price;

    @Getter
    @Setter
    @Schema(description = "annualPrice")
    private String annualPrice;

    @Getter
    @Setter
    @Schema(description = "Product id")
    private int product_id;

    @Getter
    @Setter
    @Schema(description = "Policy number")
    private String policy_number;


    @Getter
    @Setter
    @Schema(description = "Master policy number")
    private String master_policy_number;

    @Getter
    @Setter
    @Schema(description = "External id")
    private String external_id;

    @Getter
    @Setter
    @Schema(description = "State")
    private String state;

    @Getter
    @Setter
    @Schema(description = "Start date")
    private LocalDateTime start_date;

    @Getter
    @Setter
    @Schema(description = "Expiration date")
    private LocalDateTime expiration_date;

    @Getter
    @Setter
    @Schema(description = "Insured Item")
    private JsonNode insured_item;

    @Getter
    @Setter
    @Schema(description = "Quantity")
    private Integer quantity;
/*
    @Getter
    @Setter
    @Schema(description = "Order")
    private OrderEntity order;
    */

    @Getter
    @Setter
    @Schema(description = "Emission")
    private JsonNode emission;
}
