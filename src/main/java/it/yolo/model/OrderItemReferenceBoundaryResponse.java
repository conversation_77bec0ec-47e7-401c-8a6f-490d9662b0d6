package it.yolo.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import java.time.LocalDateTime;

public class OrderItemReferenceBoundaryResponse {

    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description="Price")
    private String price;

    @Getter
    @Setter
    @Schema(description="Annual Price")
    private String annualPrice;

    @Getter
    @Setter
    @Schema(description="Product id")
    private int product_id;

    @Getter
    @Setter
    @Schema(description="Policy number")
    private String policy_number;

    @Getter
    @Setter
    @Schema(description="Master policy number")
    private String master_policy_number;

    @Getter
    @Setter
    @Schema(description="External id")
    private String external_id;

    @Getter
    @Setter
    @Schema(description="State")
    private String state;

    @Getter
    @Setter
    @Schema(description="Start date")
    private LocalDateTime start_date;

    @Getter
    @Setter
    @Schema(description="Expiration date")
    private LocalDateTime expiration_date;

    @Getter
    @Setter
    @Schema(description="Insured item")
    private JsonNode insured_item;

    @Getter
    @Setter
    @Schema(description = "Quantity")
    private int quantity;

    @Getter
    @Setter
    @Schema(description="quotation")
    private JsonNode quotation;

    @Getter
    @Setter
    @Schema(description="packet id")
    private Integer packetId;

    @Getter
    @Setter
    @Schema(description = "Emission")
    private JsonNode emission;

    @Getter
    @Setter
    @Schema(description = "Instance")
    private JsonNode instance;

    @Getter
    @Setter
    private JsonNode promotion;

    @Getter
    @Setter
    private JsonNode ceilings;
}
