package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RelatedWarranty {
    private Map<String, Object> anagWarranty;
    private Object branch;
    private Integer categoryId;
    private String categoryName;
    private Map<String, Object> ceilings;
    private Object coinsurance;
    private String description;
    private String endDate;
    private String externalCode;
    private Integer id;
    private Object images;
    private Object insurancePremium;
    private Boolean mandatory;
    private String name;
    private Object packetId;
    private Object parentId;
    private Integer position;
    private Boolean preselected;
    private Boolean recurring;
    private Map<String, Object> rule;
    private String startDate;
    private Map<String, Object> taxons;
    private String translationCode;
    private Object warrantiesId;
    private Boolean openSelection;
    private Boolean noMaximals;
    private Integer maxDuration;

    private Map<String, Object> additionalProperties;

    public RelatedWarranty() {}

    public Map<String, Object> getAnagWarranty() { return anagWarranty; }
    public void setAnagWarranty(Map<String, Object> anagWarranty) { this.anagWarranty = anagWarranty; }

    public Object getBranch() { return branch; }
    public void setBranch(Object branch) { this.branch = branch; }

    public Integer getCategoryId() { return categoryId; }
    public void setCategoryId(Integer categoryId) { this.categoryId = categoryId; }

    public String getCategoryName() { return categoryName; }
    public void setCategoryName(String categoryName) { this.categoryName = categoryName; }

    public Map<String, Object> getCeilings() { return ceilings; }
    public void setCeilings(Map<String, Object> ceilings) { this.ceilings = ceilings; }

    public Object getCoinsurance() { return coinsurance; }
    public void setCoinsurance(Object coinsurance) { this.coinsurance = coinsurance; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getEndDate() { return endDate; }
    public void setEndDate(String endDate) { this.endDate = endDate; }

    public String getExternalCode() { return externalCode; }
    public void setExternalCode(String externalCode) { this.externalCode = externalCode; }

    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public Object getImages() { return images; }
    public void setImages(Object images) { this.images = images; }

    public Object getInsurancePremium() { return insurancePremium; }
    public void setInsurancePremium(Object insurancePremium) { this.insurancePremium = insurancePremium; }

    public Boolean getMandatory() { return mandatory; }
    public void setMandatory(Boolean mandatory) { this.mandatory = mandatory; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public Object getPacketId() { return packetId; }
    public void setPacketId(Object packetId) { this.packetId = packetId; }

    public Object getParentId() { return parentId; }
    public void setParentId(Object parentId) { this.parentId = parentId; }

    public Integer getPosition() { return position; }
    public void setPosition(Integer position) { this.position = position; }

    public Boolean getPreselected() { return preselected; }
    public void setPreselected(Boolean preselected) { this.preselected = preselected; }

    public Boolean getRecurring() { return recurring; }
    public void setRecurring(Boolean recurring) { this.recurring = recurring; }

    public Map<String, Object> getRule() { return rule; }
    public void setRule(Map<String, Object> rule) { this.rule = rule; }

    public String getStartDate() { return startDate; }
    public void setStartDate(String startDate) { this.startDate = startDate; }

    public Map<String, Object> getTaxons() { return taxons; }
    public void setTaxons(Map<String, Object> taxons) { this.taxons = taxons; }

    public String getTranslationCode() { return translationCode; }
    public void setTranslationCode(String translationCode) { this.translationCode = translationCode; }

    public Object getWarrantiesId() { return warrantiesId; }
    public void setWarrantiesId(Object warrantiesId) { this.warrantiesId = warrantiesId; }

    public Boolean getOpenSelection() { return openSelection; }
    public void setOpenSelection(Boolean openSelection) { this.openSelection = openSelection; }

    public Boolean getNoMaximals() { return noMaximals; }
    public void setNoMaximals(Boolean noMaximals) { this.noMaximals = noMaximals; }

    public Integer getMaxDuration() { return maxDuration; }
    public void setMaxDuration(Integer maxDuration) { this.maxDuration = maxDuration; }

    public Map<String, Object> getAdditionalProperties() { return additionalProperties; }
    public void setAdditionalProperties(Map<String, Object> additionalProperties) { this.additionalProperties = additionalProperties; }

    /**
     * Verifica se la warranty è obbligatoria.
     */
    public boolean isMandatory() {
        return mandatory != null && mandatory;
    }

    /**
     * Verifica se la warranty è opzionale.
     */
    public boolean isOptional() {
        return mandatory == null || !mandatory;
    }

    /**
     * Verifica se la warranty ha un nome valido.
     */
    public boolean hasValidName() {
        return name != null && !name.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "RelatedWarranty{" +
                "anagWarranty=" + anagWarranty +
                ", branch=" + branch +
                ", categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", ceilings=" + ceilings +
                ", coinsurance=" + coinsurance +
                ", description='" + description + '\'' +
                ", endDate=" + endDate +
                ", externalCode='" + externalCode + '\'' +
                ", id=" + id +
                ", images=" + images +
                ", insurancePremium=" + insurancePremium +
                ", mandatory=" + mandatory +
                ", name='" + name + '\'' +
                ", packetId=" + packetId +
                ", parentId=" + parentId +
                ", position=" + position +
                ", preselected=" + preselected +
                ", recurring=" + recurring +
                ", rule=" + rule +
                ", startDate=" + startDate +
                ", taxons=" + taxons +
                ", translationCode='" + translationCode + '\'' +
                ", warrantiesId=" + warrantiesId +
                ", openSelection=" + openSelection +
                ", noMaximals=" + noMaximals +
                ", maxDuration=" + maxDuration +
                ", additionalProperties=" + additionalProperties +
                '}';
    }
}
