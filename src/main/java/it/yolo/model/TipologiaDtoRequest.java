package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TipologiaDtoRequest {

    @JsonProperty("tipologiaTitoloAbitazione")
    private String tipologiaTitoloAbitazione;

    @JsonProperty("tipologiaUsoAbitazione")
    private String tipologiaUsoAbitazione;

    @JsonProperty("tipologiaCostruttivaAbitazione")
    private String tipologiaCostruttivaAbitazione;

    @JsonProperty("tipologiaTitoloAbitazione")
    public String getTipologiaTitoloAbitazione() {
        return tipologiaTitoloAbitazione;
    }

    @JsonProperty("tipologiaTitoloAbitazione")
    public void setTipologiaTitoloAbitazione(String tipologiaTitoloAbitazione) {
        this.tipologiaTitoloAbitazione = tipologiaTitoloAbitazione;
    }

    @JsonProperty("tipologiaUsoAbitazione")
    public String getTipologiaUsoAbitazione() {
        return tipologiaUsoAbitazione;
    }

    @JsonProperty("tipologiaUsoAbitazione")
    public void setTipologiaUsoAbitazione(String tipologiaUsoAbitazione) {
        this.tipologiaUsoAbitazione = tipologiaUsoAbitazione;
    }


    @JsonProperty("tipologiaCostruttivaAbitazione")
    public String getTipologiaCostruttivaAbitazione() {
        return tipologiaCostruttivaAbitazione;
    }


    @JsonProperty("tipologiaCostruttivaAbitazione")
    public void setTipologiaCostruttivaAbitazione(String tipologiaCostruttivaAbitazione) {
        this.tipologiaCostruttivaAbitazione = tipologiaCostruttivaAbitazione;
    }

    public TipologiaDtoRequest(String tipologiaTitoloAbitazione, String tipologiaUsoAbitazione, String tipologiaCostruttivaAbitazione) {
        this.tipologiaTitoloAbitazione = tipologiaTitoloAbitazione;
        this.tipologiaUsoAbitazione = tipologiaUsoAbitazione;
        this.tipologiaCostruttivaAbitazione = tipologiaCostruttivaAbitazione;
    }

    public TipologiaDtoRequest() {
    }
}
