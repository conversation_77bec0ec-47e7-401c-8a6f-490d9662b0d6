package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UpdateCustomerBoundaryRequest {

    @JsonProperty("email")
    private String email;

    @JsonProperty("customerId")
    private Integer customerId;

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("customerId")
    public Integer getCustomerId() {
        return customerId;
    }

    @JsonProperty("customerId")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }
}
