package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Schema(description = "Workflow data")
public class WorkflowBoundaryRequest {
    @Getter
    @Setter
    @Schema(description = "Product Id")
    private Integer productId;

    @Getter
    @Setter
    @Schema(description = "Workflow")
    private String[] workflow;

    @Getter
    @Setter
    @Schema(description = "enable")
    private boolean enable;
}
