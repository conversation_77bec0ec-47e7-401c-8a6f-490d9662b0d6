package it.yolo.model;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

public class WorkflowBoundaryResponse {
    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Product Id")
    private Integer productId;

    @Getter
    @Setter
    @Schema(description = "Workflow")
    private String[] workflow;

    @Getter
    @Setter
    @Schema(description = "Enable")
    private boolean enable;
}
