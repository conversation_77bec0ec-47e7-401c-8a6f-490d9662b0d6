package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.AnagStatesEntity;

import javax.enterprise.context.ApplicationScoped;

import org.apache.commons.lang3.StringUtils;

@ApplicationScoped
public class AnagStatesRepository implements PanacheRepository<AnagStatesEntity> {
	
	public AnagStatesEntity getByFrontendState(String frontendState) {
		if(StringUtils.isBlank(frontendState)) return null;
		String ourState = translateState(frontendState);
		if(ourState == null) return null;
		
		return getByState(ourState);
	}
	
	private AnagStatesEntity getByState(String state) {
		return find("from AnagStatesEntity where state = ?1", state).firstResult();
	}
	
	private String translateState(String frontendState) {
		if(StringUtils.isBlank(frontendState)) return null;
		switch (frontendState) {
		case "INSURANCE_INFO", "WARRANTIES", "ADDRESS", "ADDITIONAL_INSURANCE_INFO", "SURVEY":
			return "Draft";
		case "PAYMENT", "CONFIRM":
			return "Elaboration";
		case "COMPLETE":
			return "Confirmed";
		default:
			throw new IllegalArgumentException("Unexpected value: " + frontendState);
		}
	}
}
