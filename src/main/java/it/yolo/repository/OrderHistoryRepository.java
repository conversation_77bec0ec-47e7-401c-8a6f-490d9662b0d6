package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.OrderHistoryEntity;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class OrderHistoryRepository implements PanacheRepository<OrderHistoryEntity> {

    public OrderHistoryEntity findByOrderCode(String order_code) {
        return find("from OrderHistoryEntity oh where oh.createdAt = (select MAX(oh2.createdAt) from OrderHistoryEntity oh2 where oh2.orderCode = ?1)" +
                " and oh.orderCode = ?1", order_code).firstResult();
    }

//    public OrderHistoryEntity findByDate(String order_code) {
//        return find("select MAX(oh2.createdAt) from OrderHistoryEntity oh2 where oh2.orderCode = :order_code)").firstResult();
//    }
}
