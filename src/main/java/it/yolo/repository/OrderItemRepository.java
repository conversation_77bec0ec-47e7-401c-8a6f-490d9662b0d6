package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class OrderItemRepository implements PanacheRepository<OrderItemEntity> {

    public List<OrderItemEntity> findByOrderId(OrderEntity order) {
        List<OrderItemEntity> result = find("from OrderItemEntity item where item.order_id = ?1", order.getId()).list();
        return result;
    }
}
