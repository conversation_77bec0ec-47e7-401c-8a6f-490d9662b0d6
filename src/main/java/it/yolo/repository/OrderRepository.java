package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.OrderEntity;
import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class OrderRepository implements PanacheRepository<OrderEntity> {
    public OrderEntity findByOrderCodeAndCustomerId(String orderCode, int customerId) {
        OrderEntity result = find("from OrderEntity order where order.orderCode = ?1 and order.customerId = ?2", orderCode, customerId).firstResult();
        return result;
    }
    public List<OrderEntity> findByIdIn(List<Long> ids) {
        return find("id IN "+ ids.toString().replace("[", "(").replace("]",")")).list();
    }
}
