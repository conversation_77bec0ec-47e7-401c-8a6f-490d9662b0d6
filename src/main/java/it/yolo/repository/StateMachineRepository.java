package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.StateMachineEntity;

import javax.enterprise.context.ApplicationScoped;
@ApplicationScoped
public class StateMachineRepository implements PanacheRepository<StateMachineEntity>{
    /*public Optional<StateMachineEntity> findByOrderCode(String order_code) {
        return Optional.of(find("order_code", order_code).firstResult());
    }
     */

    public StateMachineEntity findByOrderCode(String order_code) {
        return find("select distinct o from Order o where o.current_states='InsuranceInfo' and o.order_code= :order_code").firstResult();
    }
}
