package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.WorkflowEntity;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class WorkflowRepository implements PanacheRepository<WorkflowEntity> {

    public WorkflowEntity findByProductId(Integer product_id){
        return find("from WorkflowEntity where productId = ?1", product_id).firstResult();
    }
}
