package it.yolo.resources;

import io.quarkus.security.Authenticated;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.AnagStatesMapper;
import it.yolo.model.*;
import it.yolo.service.AnagStatesService;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.RestPath;

import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;

@Path("/v1/anagStates")
public class AnagStatesResources {

        @Inject
        AnagStatesService service;

        @GET
        @Path("{id}")
        @Operation(summary = "Reads an anagStates entity")
        @Authenticated
        @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
        @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
        public BaseResponseBoundary<AnagStatesBoundaryResponse> read(
                @Parameter(description = "Order ID") @RestPath("id") Long id, @HeaderParam("Authorization") String token) {
                AnagStatesEntity entity;
                AnagStatesBoundaryResponse resp;

                entity = service.readAnagState(id).orElseThrow(() -> new EntityNotFoundException(String.format(
                        "Entity anagState by id=%d not found", id)));
                resp = AnagStatesMapper.INSTANCE.entityToResponse(entity);
                return new BaseResponseBoundary<>(resp);
        }
}
