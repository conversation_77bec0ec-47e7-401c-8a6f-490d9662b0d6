package it.yolo.resources;

import io.quarkus.security.Authenticated;
import it.yolo.http.ResponseCode;
import it.yolo.model.BaseRequestBoundary;
import it.yolo.model.BaseResponseBoundary;
import it.yolo.model.ErrorResponse;
import it.yolo.model.UpdateCustomerBoundaryRequest;
import it.yolo.service.ReconciliationService;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import javax.inject.Inject;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/v1/reconciliation")
public class OrderReconciliationResources {

        @Inject
        ReconciliationService service;

        @POST
        @Operation(summary = "Updates a order returning the resulting entity")
        @Authenticated
        @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
        @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
        public BaseResponseBoundary<Object> updateCustomer(
                BaseRequestBoundary<UpdateCustomerBoundaryRequest> req, @HeaderParam("Authorization") String token) {
                List<Long> ids= service.reconciliateOrder(req.getData().getEmail(), req.getData().getCustomerId());
                return new BaseResponseBoundary<>(ids);
        }
}
