package it.yolo.resources;

import io.quarkus.security.Authenticated;
import it.yolo.constants.OrderStateEnum;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.entity.OrderEntity;
import it.yolo.exception.*;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.OrderMapper;
import it.yolo.mapper.custom.MapperOrder;
import it.yolo.model.*;
import it.yolo.repository.OrderHistoryRepository;
import it.yolo.service.AnagStatesService;
import it.yolo.service.OrderService;
import it.yolo.service.client.ServiceClientPackets;
import it.yolo.service.client.ServiceClientProduct;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;
import com.fasterxml.jackson.databind.JsonNode;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Path("/v1/order")
public class OrderResource {

    @Inject
    OrderService service;

    @Inject
    AnagStatesService anagStatesService;

    @Inject
    ServiceClientProduct invokeProduct;

    @Inject
    ServiceClientPackets invokePacket;

    @Inject
    OrderHistoryRepository historyRepo;

    @Inject
    MapperOrder mapperOrder;

    @POST
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Creates a new order returning the created entity")
    @APIResponse(responseCode = ResponseCode.CREATED, description = "Entity created")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> create(@HeaderParam("Authorization") String token,
            BaseRequestBoundary<OrderBoundaryRequest> req) {

            OrderEntity entity;
            OrderBoundaryResponse resp;
            entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
            entity = service.createOrder(entity, token);
            resp = OrderMapper.INSTANCE.entityToResponse(entity);
            resp.setOrderHistory(historyRepo.findByOrderCode(entity.getOrderCode()));
            resp.setProduct(invokeProduct.getProductResponse());
            return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("{id}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> read(
            @Parameter(description = "Order ID") @RestPath("id") Long id, @HeaderParam("Authorization") String token) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readOrder(id);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/code/{orderCode}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> read(
            @Parameter(description = "Order Code") @RestPath("orderCode") String orderCode) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readOrderByOrderCode(orderCode);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    /*
    @PUT
    @Path("confirmed")
    @Operation(summary = "Updates the anagStatus of a order to confirmed. Returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> confirmOrder(
            @Parameter(description = "Order ID") @QueryParam("order_id") long order_id, @HeaderParam("Authorization") String token)
            throws Exception {

        OrderBoundaryResponse resp;

        Optional<OrderEntity> orderEntity = service.readOrder(order_id);
        if (orderEntity.isEmpty()) {
            throw new Exception("Invalid input received");
        } else {

            Optional<AnagStatesEntity> anagEntity = anagStatesService.readAnagState(OrderStateEnum.CONFIRMED.getValue().longValue());
            orderEntity.get().setAnagStates(anagEntity.get());
            service.updateOrder(String.valueOf(order_id), orderEntity.get(), token, null);
            service.updatePolicyStatus(Long.valueOf(order_id), token);
            resp = OrderMapper.INSTANCE.entityToResponse(orderEntity.get());
        }

        return new BaseResponseBoundary<>(resp);
    }
     */


    @PUT
    @Path("failed")
    @Operation(summary = "Updates the anagStatus of a order to failed. Returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> failed(
            @Parameter(description = "Order ID") @QueryParam("orderCode") String orderCode, @HeaderParam("Authorization") String token)
            throws Exception {

        OrderBoundaryResponse resp;
        OrderEntity orderEntity = service.readOrderByOrderCode(orderCode);
        if (orderEntity==null) {
            throw new Exception("Invalid input received");
        } else {
            Optional<AnagStatesEntity> anagEntity = anagStatesService.readByName(OrderStateEnum.FAILED.getKey());
            orderEntity = service.updatefailedStatus(orderEntity,anagEntity.get());
            resp = OrderMapper.INSTANCE.entityToResponse(orderEntity);
        }

        return new BaseResponseBoundary<>(resp);
    }


    @PUT
    @Path("{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> update(
            @Parameter(description = "Order ID") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req, @HeaderParam("Authorization") String token) {
        OrderEntity entity;
        OrderBoundaryResponse resp;
        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.updateOrder(order_code, entity, token, req.getData().getDiscount());
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.setProduct(invokeProduct.getProductResponse());
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/emission")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateEmission(
            BaseRequestBoundary<EmissionBoundaryRequest> req, @HeaderParam("Authorization") String token) {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.updateEmission(req.getData().getOrderCode(), req.getData().getId(), token, req.getData().getEmission());
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<OrderBoundaryResponse>> list(@HeaderParam("Authorization") String token) {
        List<OrderEntity> list;
        List<OrderBoundaryResponse> resp;

        list = service.listOrders();

        resp = OrderMapper.INSTANCE.entitiesToResponses(list);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/{customerId}/{orderCode}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> readByCustomerAndOrderCode(
            @HeaderParam("Authorization") String token,
            @Parameter(description = "Customer Id") @RestPath("customerId") int customerId,
            @Parameter(description = "Order Code") @RestPath("orderCode") String orderCode) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readOrderByOrderCodeAndCustomerId(orderCode, customerId, token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        if(resp.getPacketId() != 0) {
            resp.setPacket(invokePacket.getPacketResponse());
        } else {
            resp.setProduct(invokeProduct.getProductResponse());
        }
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/details/order/{orderCode}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> readByCustomerAndOrderCode(
            @HeaderParam("Authorization") String token,
            @Parameter(description = "Order Code") @RestPath("orderCode") String orderCode) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readDetailsOrder(orderCode,token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        if(resp.getPacketId() != 0) {
            resp.setPacket(invokePacket.getPacketResponse());
        } else {
            resp.setProduct(invokeProduct.getProductResponse());
        }
        return new BaseResponseBoundary<>(resp);
    }



    @GET
    @Path("/details/{orderItemId}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> detailsByOrderItemId(
            @HeaderParam("Authorization") String token,
            @RestPath("orderItemId") Long orderItemId) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readOrderDetailsByOrdeItemId(orderItemId, token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.getOrderItem().forEach(orderItem-> {
            if(orderItem.getPacketId()!=null){
                resp.setProduct(mapperOrder.productToProductResponse(invokePacket.getPacketResponse().getData().getProduct()));
                resp.getProduct().getData().setPacket(invokePacket.getPacketResponse());
            }else{
                resp.setProduct(invokeProduct.getProductResponse());
            }
        });
        return new BaseResponseBoundary<>(resp);
    }

    @POST
    @Path("/id-to-codes")
    @Operation(summary = "get order codes from ids")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<Map<Long, OrderBoundaryResponse>> idToCodes(
            @HeaderParam("Authorization") String token, IdsRequest request) {
        return new BaseResponseBoundary<>(service.getCodesFromIds(request.getData().getIds()));
    }

    @PUT
    @Path("/orderItem/price")
    @Operation(summary = "Updates a order item price")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<OrderBoundaryResponse>> updateOrderItemPrice(
            BaseRequestBoundary<List<OrderItemBoundaryRequest>> req, @HeaderParam("Authorization") String token) {

        List<OrderEntity> entities;
        List<OrderBoundaryResponse> responses;

        entities = service.updateOrderItemsPrices(req.getData());
        responses = OrderMapper.INSTANCE.entitiesToResponses(entities);
        return new BaseResponseBoundary<>(responses);
    }

    
    @PUT
    @Path("/updateOrder/estimate")
    @Operation(summary = "Updates a order item price")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateOrderWithEstimate(
            BaseRequestBoundary<JsonNode> req) {

        OrderEntity entities;
        OrderBoundaryResponse responses;

        entities = service.updateOrderWithEstimate(req.getData().get("orderCode").asText(),req.getData().get("estimateId").asText());
        responses = OrderMapper.INSTANCE.entityToResponse(entities);
        return new BaseResponseBoundary<>(responses);
    }

    static class ResponseSchema extends
            BaseResponseBoundary<OrderBoundaryResponse> {

        public ResponseSchema(OrderBoundaryResponse data) {
            super(data);
        }
    }
}
