package it.yolo.resources.legacy;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

import io.smallrye.common.annotation.Blocking;
import it.yolo.service.legacy.ServiceYinOrder;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestPath;

import com.fasterxml.jackson.databind.JsonNode;

import it.yolo.entity.OrderItemEntity;
import it.yolo.http.ResponseCode;
import it.yolo.model.BaseRequestBoundary;
import it.yolo.model.BaseResponseBoundary;
import it.yolo.model.ErrorResponse;
import it.yolo.model.OrderItemBoundaryResponse;
import it.yolo.service.OrderService;

@Path("/yin/v1")
public class YinOrderResource {

    @Inject
    Logger log;

    @Inject
    ServiceYinOrder service;

    @PUT
    @Path("/orders/{order_number}/callback_order_updated")
    @APIResponse(responseCode = ResponseCode.OK, description = "Callback order updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<?> callBackOrderUpdated(
            @Parameter(description = "Order number")
            @RestPath("order_number") String orderNumber,
            JsonNode req) throws Exception {
        JsonNode res;
        log.info("{resources: YinOrderResource start , body : " + req.toString() + "orderNumber : " + orderNumber + "}");
        if (req == null) {
            throw new Exception("ERRORE PARAMETRO OBBLIGATORIO  : callback_on_order_updated");
        }
        String url = req.get("callback_on_order_updated").asText();
        JsonNode resp = service.callBackOnOrderUpdate(url, orderNumber);
        log.info("{resources: YinOrderResource end , body : " + resp.toString());
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/associate_user_to_order")
    @APIResponse(responseCode = ResponseCode.OK, description = "Callback order updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public JsonNode associateUserToOrder(
            JsonNode req) throws Exception {
        JsonNode res;
        log.info("{resources: YinOrderResource start , body : " + req.toString() + "orderNumber : " + req.get("order_number").asText() + "}");
        if (req == null) {
            throw new Exception("ERRORE PARAMETRO OBBLIGATORIO  :");
        }

        String orderNumber = req.get("order_number")==null?null:req.get("order_number").asText();
        Long userId= req.get("user_id")==null?null:req.get("user_id").asLong();
        String ndg = req.get("ndg")==null?null:req.get("ndg").asText();
        service.associateUserToOrderNoAuth(userId, orderNumber,ndg);
        log.info("{resources: YinOrderResource end , body : " + req.toString());
        return req;
    }


    @PUT
    @Path("/associate_user_to_order/grpc")
    @APIResponse(responseCode = ResponseCode.OK, description = "Callback order updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public JsonNode associateUserToOrderGrpc(
            JsonNode req) throws Exception {
        JsonNode res;
        log.info("{resources: YinOrderResource start , body : " + req.toString() + "orderNumber : " + req.get("order_number").asText() + "}");
        if (req == null) {
            throw new Exception("ERRORE PARAMETRO OBBLIGATORIO  :");
        }

        String orderNumber = req.get("order_number")==null?null:req.get("order_number").asText();
        Long userId= req.get("user_id")==null?null:req.get("user_id").asLong();
        String ndg = req.get("ndg")==null?null:req.get("ndg").asText();
        service.associateUserToOrder(userId, orderNumber,ndg);
        log.info("{resources: YinOrderResource end , body : " + req.toString());
        return req;
    }




    @PUT
    @Path("/update-status/items")
    @APIResponse(responseCode = ResponseCode.OK, description = "Update order status")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public JsonNode updateStatusOrder(
            JsonNode req) throws Exception {
        OrderItemEntity res;
        log.info("{resources: YinOrderResource start updateStatusOrder , body : " + req.toString() + "orderNumber : " + req.get("orderNumber").asText() + "updateStatus : " + req.get("updateStatus").asText()+ "}");
        if (req == null) {
            throw new Exception("ERRORE PARAMETRO OBBLIGATORIO");
        }
        String updateStatus = req.get("updateStatus").asText();
        String orderNumber= req.get("orderNumber").asText();

        service.updateStatusOrderById(orderNumber, updateStatus);
        log.info("{resources: YinOrderResource updateStatusOrder end , body : " + req.toString());
        return req;
    }
}
