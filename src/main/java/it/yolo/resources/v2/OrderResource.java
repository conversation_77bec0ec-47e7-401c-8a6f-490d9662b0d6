package it.yolo.resources.v2;

import io.quarkus.security.Authenticated;
import it.yolo.entity.OrderEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.OrderMapper;
import it.yolo.model.*;
import it.yolo.repository.OrderHistoryRepository;
import it.yolo.service.client.ServiceClientPackets;
import it.yolo.service.v2.OrderService;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/*
 * LA RESOURCE V2 DEVE PUNTARE AL SERVICE NEL PACKAGE V2
 */
@Path("/v2")
public class OrderResource {

    @Inject
    OrderService service;


    @Inject
    ServiceClientPackets invokePacket;

    @Inject
    OrderHistoryRepository historyRepo;

    @POST
    @Path("/order")
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Creates a new order returning the created entity")
    @APIResponse(responseCode = ResponseCode.CREATED, description = "Entity created")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> create(@HeaderParam("Authorization") String token,
            BaseRequestBoundary<OrderBoundaryRequest> req) {

            OrderEntity entity;
            OrderBoundaryResponse resp;
            entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
            entity = service.createOrder(token, entity);
            resp = OrderMapper.INSTANCE.entityToResponse(entity);
            resp.setOrderHistory(historyRepo.findByOrderCode(entity.getOrderCode()));
            resp.setPacket(invokePacket.getPacketResponse());
            resp.getOrderItem().forEach(item->item.setPacketId(resp.getPacketId()));
            return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/{id}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> read(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order ID") @RestPath("id") Long id) {
        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.readOrder(id);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/order/{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> update(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.updateOrder(order_code, entity, token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.setPacket(invokePacket.getPacketResponse());
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<OrderBoundaryResponse>> list() {
        List<OrderEntity> list;
        List<OrderBoundaryResponse> resp;

        list = service.listOrders();

        resp = OrderMapper.INSTANCE.entitiesToResponses(list);
        return new BaseResponseBoundary<>(resp);
    }

    static class ResponseSchema extends
            BaseResponseBoundary<OrderBoundaryResponse> {

        public ResponseSchema(OrderBoundaryResponse data) {
            super(data);
        }
    }
}
