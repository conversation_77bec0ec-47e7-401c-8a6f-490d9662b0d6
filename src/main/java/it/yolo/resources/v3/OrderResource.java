package it.yolo.resources.v3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import io.quarkus.security.Authenticated;
import it.yolo.client.communicationManager.dto.CertificateResponseDto;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.client.response.client.product.Question;
import it.yolo.common.Utility;
import it.yolo.entity.OrderEntity;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.OrderMapper;
import it.yolo.model.*;
import it.yolo.records.Packet;
import it.yolo.repository.OrderHistoryRepository;
import it.yolo.service.client.ServiceClientPacketsV3;
import it.yolo.service.client.ServiceClientProductV3;
import it.yolo.service.question.QuestionProcessorService;
import it.yolo.service.rule.PacketFilterService;
import it.yolo.service.v3.OrderService;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.util.List;
import java.util.stream.Collectors;

/*
 * LA RESOURCE V3 DEVE PUNTARE AL SERVICE NEL PACKAGE V3
 */
@Path("/v3")
public class OrderResource {

    @Inject
    OrderService service;

    @Inject
    ServiceClientProductV3 invokeProduct;

    @Inject
    ServiceClientPacketsV3 invokePacket;

    @Inject
    OrderHistoryRepository historyRepo;

    @Inject
    PacketFilterService packetFilterService;

    @Inject
    QuestionProcessorService questionProcessorService;

    @Inject
    Logger logger;

    @Inject
    JsonWebToken jsonWebToken;

    @POST
    @Path("/order")
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Creates a new order returning the created entity")
    @APIResponse(responseCode = ResponseCode.CREATED, description = "Entity created")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> create(@HeaderParam("Authorization") String token,
            @HeaderParam("x-tenant-language") String tenantLanguage,
            @HeaderParam("Session-id") String sessionId,
            BaseRequestBoundary<OrderBoundaryRequest> req) throws Exception {

        logger.info("TOKEN AUTH ONED CONTROLLER: " + token); // REMOVE
        OrderEntity entity;
        OrderBoundaryResponse resp;
        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity.setLanguage(tenantLanguage);
        entity = service.createOrder(token, entity, sessionId);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.setOrderHistory(historyRepo.findByOrderCode(entity.getOrderCode()));
        resp.setPacket(invokePacket.findById(Long.valueOf(entity.getPacketId()), token).packetResponse());
        resp.getOrderItem().get(0).setPacketId(resp.getPacketId());
        return new BaseResponseBoundary<>(resp);
    }

    // Versione sincrona per il confronto
    private void enrichOrderWithProductAndPacketSync(OrderBoundaryResponse resp, OrderEntity entity, String token,
            String language) {
        // Versione sincrona semplice
        ProductResponse product = language != null
                ? invokeProduct.getById(Long.valueOf(entity.getProductId()), token, language)
                : invokeProduct.getById(Long.valueOf(entity.getProductId()), token);

        Packet packet = language != null
                ? invokePacket.findById(Long.valueOf(entity.getPacketId()), token, language)
                : invokePacket.findById(Long.valueOf(entity.getPacketId()), token);

        // Stessa logica di applicazione
        applyProductAndPacketData(resp, entity, product, packet);
    }

    private void applyProductAndPacketData(OrderBoundaryResponse resp, OrderEntity entity, ProductResponse product,
            Packet packet) {
        resp.setProduct(product);

        // Early exit
        if (product == null || product.getData() == null) {
            resp.setPacket(Utility.setPacketWithCieling(packet, entity.getOrderItem().get(0)));
            return;
        }

        packetFilterService.filterProductPackets(entity, resp.getProduct());

        List<Question> productQuestions = product.getData().getQuestions();
        if (productQuestions == null || productQuestions.isEmpty()) {
            resp.setPacket(Utility.setPacketWithCieling(packet, entity.getOrderItem().get(0)));
            return;
        }

        // Process questions
        questionProcessorService.processQuestionsInPlace(productQuestions, entity);

        resp.setPacket(Utility.setPacketWithCieling(packet, entity.getOrderItem().get(0)));

        // Merge ottimizzato
        if (resp.getPacket() != null &&
                resp.getPacket().getData() != null &&
                resp.getPacket().getData().getQuestions() != null &&
                !productQuestions.isEmpty()) {

            // Usa addAll diretto invece di stream se non serve filtraggio
            boolean needsFiltering = productQuestions.stream().anyMatch(q -> q.getPacketId() != null);

            if (!needsFiltering) {
                // Caso più veloce: no filtering needed
                resp.getPacket().getData().getQuestions().addAll(productQuestions);
            } else {
                // Caso con filtering
                List<Question> questionsToAdd = productQuestions.stream()
                        .filter(question -> question.getPacketId() == null)
                        .collect(Collectors.toList());

                if (!questionsToAdd.isEmpty()) {
                    resp.getPacket().getData().getQuestions().addAll(questionsToAdd);
                }
            }

            if (!resp.getPacket().getData().getQuestions().isEmpty()) {
                service.computeAnswerRules(resp);
                resp.setPacket(service.computeAnswerRules(resp.getPacket(), resp));
            }
        }
    }

    @GET
    @Path("/order/{id}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> read(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order ID") @RestPath("id") Long id,
            @HeaderParam("Session-id") String sessionId) throws Exception {
        OrderEntity entity = service.readOrder(id, sessionId);
        OrderBoundaryResponse resp = OrderMapper.INSTANCE.entityToResponse(entity);
        enrichOrderWithProductAndPacketSync(resp, entity, token, null);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/unchecked/{id}")
    @Operation(summary = "Reads an order entity")
    @RolesAllowed({ "technical-users", "yolo-users", "intermediary-users", "sso-users" })
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> uncheckedRead(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order ID") @RestPath("id") Long id) throws Exception {
        OrderEntity entity = service.uncheckedReadOrder(id);
        OrderBoundaryResponse resp = OrderMapper.INSTANCE.entityToResponse(entity);
        enrichOrderWithProductAndPacketSync(resp, entity, token, null);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/code/{orderCode}")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> read(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order Code") @RestPath("orderCode") String orderCode,
            @HeaderParam("x-tenant-language") String language,
            @HeaderParam("Session-id") String sessionId) throws Exception {
        OrderEntity entity = service.readOrderByOrderCode(orderCode, sessionId);
        OrderBoundaryResponse resp = OrderMapper.INSTANCE.entityToResponse(entity);
        enrichOrderWithProductAndPacketSync(resp, entity, token, language);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/unchecked/code/{orderCode}")
    @Operation(summary = "Reads an order entity")
    @RolesAllowed({ "technical-users", "yolo-users", "intermediary-users", "sso-users" })
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> uncheckedReadCode(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order Code") @RestPath("orderCode") String orderCode) throws Exception {
        OrderEntity entity = service.readOrderByOrderCodeUnchecked(orderCode);
        OrderBoundaryResponse resp = OrderMapper.INSTANCE.entityToResponse(entity);
        enrichOrderWithProductAndPacketSync(resp, entity, token, null);
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/order/{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> update(@HeaderParam("Authorization") String token,
            @HeaderParam("x-tenant-language") String tenantLanguage,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req,
            @HeaderParam("Session-id") String sessionId) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.updateOrder(order_code, tenantLanguage, entity, false, null, token, sessionId);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.setProduct(invokeProduct.getById(Long.valueOf(entity.getProductId()), token, tenantLanguage));
        // entity.setXTenantLanguage(tenantLanguage);
        resp.setPacket(Utility.setPacketWithCieling(invokePacket
                .findById(Long.valueOf(entity.getPacketId()), token, tenantLanguage),
                entity.getOrderItem().get(0)));
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/order/yin/{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateYin(@HeaderParam("Authorization") String token,
            @HeaderParam("x-tenant-language") String tenantLanguage,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.putFromYin(order_code, tenantLanguage, entity, token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        // entity.setXTenantLanguage(tenantLanguage);
        resp.setPacket(Utility.setPacketWithCieling(invokePacket
                .findById(Long.valueOf(entity.getPacketId()), token),
                entity.getOrderItem().get(0)));
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/order/unchecked/{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @RolesAllowed({ "technical-users", "yolo-users", "intermediary-users", "sso-users" })
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> uncheckedUpdate(@HeaderParam("Authorization") String token,
            @HeaderParam("x-tenant-language") String tenantLanguage,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req,
            @HeaderParam("Session-id") String sessionId) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.updateOrder(order_code, tenantLanguage, entity, true, null, token, sessionId);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        // entity.setXTenantLanguage(tenantLanguage);
        resp.setPacket(Utility.setPacketWithCieling(invokePacket
                .findById(Long.valueOf(entity.getPacketId()), token),
                entity.getOrderItem().get(0)));
        return new BaseResponseBoundary<>(resp);
    }

    @PUT
    @Path("/order/quotation/{order_code}")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateQuotation(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            JsonNode req) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;
        entity = service.updateOrderQuotation(order_code, req);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<OrderBoundaryResponse>> list() throws Exception {
        List<OrderEntity> list;
        List<OrderBoundaryResponse> resp;

        list = service.listOrders();

        resp = OrderMapper.INSTANCE.entitiesToResponses(list);
        return new BaseResponseBoundary<>(resp);
    }

    @POST
    @Path("/order/legacyRequest/{order_code}")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> setLegacyRequest(JsonNode req,
            @RestPath("order_code") String order_code) throws Exception {
        return new BaseResponseBoundary<>(
                OrderMapper.INSTANCE.entityToResponse(service.setLegacyRequest(req, order_code)));
    }

    @POST
    @Path("order/emission")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateEmission(
            BaseRequestBoundary<EmissionBoundaryRequest> req, @HeaderParam("Authorization") String token) {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = service.updateEmission(req.getData().getOrderCode(), req.getData().getId(), token,
                req.getData().getEmission());
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @POST
    @Path("order/{order_code}/positive_adjustment")
    @Operation(summary = "Updates a order returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public JsonNode priceAdjustment(@PathParam("order_code") String orderCode, JsonNode req) {
        service.priceAdjustment(req.get("price_to_adjust").asDouble(), orderCode);
        return JsonNodeFactory.instance.objectNode();
    }

    @GET
    @Path("/estimates")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<OrderBoundaryResponse>> readByCustomerAndOrderCode(
            @HeaderParam("Authorization") String token) throws Exception {
        List<OrderEntity> orders;
        List<OrderBoundaryResponse> resp;

        orders = service.listEstimates(token);
        resp = OrderMapper.INSTANCE.entitiesToResponses(orders);
        for (int i = 0; i < resp.size(); i++) {
            resp.get(i).setPacket(Utility.setPacketWithCieling(invokePacket
                    .findById(Long.valueOf(orders.get(i).getPacketId()), token),
                    orders.get(i).getOrderItem().get(0)));
        }
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/{customerId}/customer")
    @Operation(summary = "Reads an order entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<String>> readByCustomerId(@PathParam("customerId") Long customerId)
            throws Exception {
        return new BaseResponseBoundary<>(service.readOrdersByCustomer(customerId));
    }

    @PUT
    @Path("/estimates/{order_code}")
    @Operation(summary = "Updates the state of the order to Estimate returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> saveEstimate(@HeaderParam("Authorization") String token,
            @HeaderParam("x-tenant-language") String tenantLanguage,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code,
            BaseRequestBoundary<OrderBoundaryRequest> req) throws Exception {

        OrderEntity entity;
        OrderBoundaryResponse resp;

        entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.checkEstimateKind(order_code, tenantLanguage, entity, token);
        resp = OrderMapper.INSTANCE.entityToResponse(entity);
        resp.setPacket(Utility.setPacketWithCieling(invokePacket
                .findById(Long.valueOf(entity.getPacketId()), token),
                entity.getOrderItem().get(0)));
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/estimates/{order_code}")
    @Operation(summary = "Updates the state of the order to Estimate returning the resulting entity")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<CertificateResponseDto> getEstimate(@HeaderParam("Authorization") String token,
            @Parameter(description = "Order code") @RestPath("order_code") String order_code) throws Exception {
        CertificateResponseDto certificate = service.getEstimate(order_code, token);
        return new BaseResponseBoundary<>(certificate);
    }

    @POST
    @Path("order/duplicated-order")
    @Operation(summary = "create a new  order entity By orderCode ")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
        public BaseResponseBoundary<OrderBoundaryResponse> duplicatedOrder(
            @HeaderParam("Authorization") String token,
            @HeaderParam("sessionId") String sessionId,
            BaseRequestBoundary<OrderBoundaryRequest> req) throws Exception {
        logger.info("OrderResource.duplicated-order() start with req + " + req);
        OrderEntity entity = OrderMapper.INSTANCE.requestToEntity(req.getData());
        entity = service.duplicatedOrder(entity, token, sessionId);
        OrderBoundaryResponse resp = OrderMapper.INSTANCE.entityToResponse(entity);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/retry/{orderCode}/{step}")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateRetryStep(String orderCode, Integer step)
            throws Exception {
        OrderBoundaryResponse resp;
        OrderEntity order = service.updateRetryStep(orderCode, step);
        resp = OrderMapper.INSTANCE.entityToResponse(order);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("/order/checkExistingPolicy/{orderCode}")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<String> checkExistingPolicy(String orderCode,
            @HeaderParam("Authorization") String token) throws Exception {
        return new BaseResponseBoundary<>(service.checkExistingPolicy(orderCode, token));
    }

    @POST
    @Path("/order/by-packets")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<Long>> getByPackets(@HeaderParam("Authorization") String token, IdsDto req)
            throws Exception {
        return new BaseResponseBoundary<>(service.getByPackets(req, token));
    }

    @POST
    @Path("/order/by-products")
    @Operation(summary = "Lists order entities")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<Long>> getByOrdersDestinations(@HeaderParam("Authorization") String token,
            IdsDto req) throws Exception {
        return new BaseResponseBoundary<>(service.getByProductDestination(req, token));
    }

    @GET
    @Path("order/start/{orderCode}")
    @Operation(summary = "update start date order Stripe")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<OrderBoundaryResponse> updateStartDate(String orderCode) throws Exception {
        OrderBoundaryResponse resp;
        OrderEntity order = service.updateStartDate(orderCode);
        resp = OrderMapper.INSTANCE.entityToResponse(order);
        return new BaseResponseBoundary<>(resp);
    }

    @GET
    @Path("order/precontractual/{orderCode}")
    @Operation(summary = "update start date order Stripe")
    @Authenticated
    @APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public Response preContractMail(@HeaderParam("Authorization") String token,
            @PathParam("orderCode") String orderCode) throws Exception {
        return service.preContractualEmail(orderCode, token);
    }

    static class ResponseSchema extends
            BaseResponseBoundary<OrderBoundaryResponse> {

        public ResponseSchema(OrderBoundaryResponse data) {
            super(data);
        }
    }
}
