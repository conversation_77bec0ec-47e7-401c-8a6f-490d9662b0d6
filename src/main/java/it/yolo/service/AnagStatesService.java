package it.yolo.service;

import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.repository.AnagStatesRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Optional;

@ApplicationScoped
public class AnagStatesService {
    @Inject
    AnagStatesRepository repo;

    @WithSpan("AnagStateService.readAnagState")
    public Optional<AnagStatesEntity> readAnagState(
            @SpanAttribute("arg.id") Long id) {
        return repo.findByIdOptional(id);
    }

    @WithSpan("AnagStateService.readAnagState")
    public Optional<AnagStatesEntity> readByName(
            @SpanAttribute("arg.name") String name) {
        return Optional.of(repo.find("state",name).firstResult());
    }
}
