package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.entity.*;
import it.yolo.machine.step.state.StepStateTransformator;
import it.yolo.repository.AnagStatesRepository;
import it.yolo.repository.OrderHistoryRepository;
import it.yolo.repository.WorkflowRepository;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;

import static it.yolo.constants.StepStateEnum.*;

@RequestScoped
public class OrderHistoryService {

    @Inject
    OrderHistoryRepository historyRepo;

    @Inject
    WorkflowRepository workflowRepo;

    @Inject
    AnagStatesRepository stateRepo;

    private static String STATE_ORDER_CREATE = "Draft";

    @Transactional
    public void createHistoryForOrderInsert(OrderEntity order) {
        OrderHistoryEntity orderHistory = new OrderHistoryEntity();
        orderHistory.setCreatedAt(order.getCreatedAt());
        orderHistory.setUpdatedAt(order.getUpdatedAt());
        orderHistory.setCreatedBy(order.getCreatedBy());
        orderHistory.setUpdatedBy(order.getUpdatedBy());
        orderHistory.setOrderCode(order.getOrderCode());
        orderHistory.setOrderState(STATE_ORDER_CREATE);
        orderHistory.setStepState(INSURANCE_INFO);

        historyRepo.persist(orderHistory);
    }

    @Transactional
    public void createHistoryForOrderPut(OrderEntity order, JsonNode warranties) {
        OrderHistoryEntity orderHistory = new OrderHistoryEntity();
        orderHistory.setCreatedAt(order.getCreatedAt());
        orderHistory.setCreatedBy(order.getCreatedBy());
        orderHistory.setUpdatedBy(order.getUpdatedBy());
        orderHistory.setOrderCode(order.getOrderCode());

        OrderHistoryEntity orderHistoryPrevious = historyRepo.findByOrderCode(order.getOrderCode());
        if(!orderHistoryPrevious.getStepState().name().equalsIgnoreCase(ADDRESS.name()) || warranties==null) {
            WorkflowEntity workflow = workflowRepo.findByProductId(order.getProductId());
            orderHistory.setStepState(StepStateTransformator.getNextState(orderHistoryPrevious.getStepState().name(),
                    workflow.getWorkflow().split(";")));
            AnagStatesEntity orderState = stateRepo.getByFrontendState(orderHistory.getStepState().name());
            if (orderState != null)
                orderHistory.setOrderState(orderState.getState());
            historyRepo.persist(orderHistory);

            for (OrderItemEntity item : order.getOrderItem()) {
                item.setState(orderHistory.getStepState().name().toLowerCase());
            }
        }
    }
}
