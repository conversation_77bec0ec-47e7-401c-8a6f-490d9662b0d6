package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.packet.Product;
import it.yolo.common.Utility;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * Servizio per la gestione sicura degli aggiornamenti degli OrderItem.
 * Centralizza la logica comune tra updateOrderItem e mapOrderToOrderItemListSecure.
 */
@ApplicationScoped
public class OrderItemUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(OrderItemUpdateService.class);

    /**
     * <PERSON>cola il prezzo finale in modo sicuro basandosi solo sui dati del backend.
     * Il prezzo non può essere passato dal client per motivi di sicurezza.
     */
    public double calculateSecurePrice(Packet packetResponse, OrderItemEntity orderItem) {
        double basePremium = Optional.ofNullable(packetResponse.packetResponse().getData().getPacketPremium()).orElse(0.0);
        int quantity = Optional.ofNullable(orderItem.getQuantity()).orElse(1);
        int days = Optional.ofNullable(orderItem.getDays()).orElse(1);
        
        double priceFinal = basePremium * quantity * days;
        
        logger.debug("Calculated secure price: basePremium={}, quantity={}, days={}, final={}", 
                    basePremium, quantity, days, priceFinal);
        
        return priceFinal;
    }

    /**
     * Aggiorna il PacketId se diverso dall'entity principale.
     */
    public void updatePacketId(List<OrderItemEntity> orderItems, OrderEntity updatedEntity, Packet packetResponse) {
        orderItems.forEach(orderItem -> {
            if (orderItem.getPacketId() != updatedEntity.getPacketId()) {
                logger.info("Updating packetId for orderItem {} from {} to {}", 
                           orderItem.getId(), orderItem.getPacketId(), updatedEntity.getPacketId());
                orderItem.setPacketId(updatedEntity.getPacketId());
                
                // Aggiorna anche il prezzo quando cambia il packet
                double securePrice = calculateSecurePrice(packetResponse, orderItem);
                orderItem.setPrice(String.valueOf(securePrice));
                logger.info("Updated price for orderItem {} to {} due to packet change", 
                           orderItem.getId(), securePrice);
            }
        });
    }

    /**
     * Aggiorna il ProductId se diverso dall'entity principale.
     */
    public void updateProductId(List<OrderItemEntity> orderItems, OrderEntity updatedEntity) {
        orderItems.forEach(orderItem -> {
            if (orderItem.getProduct_id() != updatedEntity.getProductId()) {
                logger.info("Updating productId for orderItem {} from {} to {}", 
                           orderItem.getId(), orderItem.getProduct_id(), updatedEntity.getProductId());
                orderItem.setProduct_id(updatedEntity.getProductId());
            }
        });
    }

    /**
     * Aggiorna la quantità dagli OrderItem della richiesta.
     */
    public void updateQuantity(List<OrderItemEntity> orderItems, OrderEntity reqEntity) {
        if (reqEntity.getOrderItem() == null || reqEntity.getOrderItem().isEmpty()) {
            return;
        }

        // Aggiorna quantità da singoli item della richiesta
        orderItems.forEach(orderItem -> {
            reqEntity.getOrderItem().forEach(reqItem -> {
                if (orderItem.getQuantity() == null && reqItem.getQuantity() != null) {
                    logger.info("Setting quantity {} for orderItem {}", reqItem.getQuantity(), orderItem.getId());
                    orderItem.setQuantity(reqItem.getQuantity());
                }
            });
        });

        // Aggiorna quantità dal primo item se presente
        if (reqEntity.getOrderItem().get(0).getQuantity() != null) {
            orderItems.forEach(orderItem -> {
                logger.info("Setting quantity {} for orderItem {} from first request item", 
                           reqEntity.getOrderItem().get(0).getQuantity(), orderItem.getId());
                orderItem.setQuantity(reqEntity.getOrderItem().get(0).getQuantity());
            });
        }
    }

    /**
     * Aggiunge paymentFrequency all'instance degli OrderItem.
     */
    public void updatePaymentFrequency(List<OrderItemEntity> orderItems, OrderEntity reqEntity) {
        if (reqEntity.getPaymentFrequency() == null) {
            return;
        }

        orderItems.forEach(orderItem -> {
            if (orderItem.getInstance() == null) {
                orderItem.setInstance(JsonNodeFactory.instance.objectNode());
            }
            ((ObjectNode) orderItem.getInstance()).put("paymentFrequency", reqEntity.getPaymentFrequency());
            logger.debug("Set paymentFrequency {} for orderItem {}", reqEntity.getPaymentFrequency(), orderItem.getId());
        });
    }

    /**
     * Inizializza l'instance se null per permettere l'emissione.
     */
    public void initializeInstanceIfNull(List<OrderItemEntity> orderItems) {
        orderItems.forEach(orderItem -> {
            if (orderItem.getInstance() == null) {
                logger.info("Initializing empty instance for orderItem {}", orderItem.getId());
                orderItem.setInstance(JsonNodeFactory.instance.objectNode());
            }
        });
    }

    /**
     * Applica lo sconto se presente (logica temporanea).
     */
    public void applyDiscount(List<OrderItemEntity> orderItems, String discount) {
        if (discount == null || !discount.equalsIgnoreCase("sconto1@@")) {
            return;
        }

        Integer discountPercentage = 100;
        orderItems.forEach(orderItem -> {
            try {
                Float price = Float.parseFloat(orderItem.getPrice());
                Float discountedPrice = price - ((price / 100) * discountPercentage);
                orderItem.setPrice(String.valueOf(discountedPrice));
                logger.info("Applied discount to orderItem {}: {} -> {}", 
                           orderItem.getId(), price, discountedPrice);
            } catch (NumberFormatException e) {
                logger.warn("Could not apply discount to orderItem {} due to invalid price: {}", 
                           orderItem.getId(), orderItem.getPrice());
            }
        });
    }

    /**
     * Aggiorna la quotation dagli OrderItem della richiesta.
     */
    public void updateQuotation(List<OrderItemEntity> orderItems, OrderEntity reqEntity) {
        if (reqEntity.getOrderItem() == null || reqEntity.getOrderItem().isEmpty()) {
            return;
        }

        List<OrderItemEntity> itemsWithQuotation = reqEntity.getOrderItem().stream()
                .filter(item -> item.getQuotation() != null)
                .toList();

        if (!itemsWithQuotation.isEmpty()) {
            for (OrderItemEntity itemWithQuotation : itemsWithQuotation) {
                for (OrderItemEntity orderItem : orderItems) {
                    orderItem.setQuotation(itemWithQuotation.getQuotation());
                    logger.debug("Set quotation for orderItem {}", orderItem.getId());
                }
            }
        }
    }

    /**
     * Gestisce il caso speciale dove se chosenWarranties.warranties è vuoto, il prezzo deve essere 0.
     */
    public void handleEmptyWarrantiesPrice(List<OrderItemEntity> orderItems, OrderEntity reqEntity) {
        JsonNode warrantiesNode = reqEntity.getChosenWarranties() == null
                ? null
                : reqEntity.getChosenWarranties().findValue("warranties");
        
        if (warrantiesNode != null && warrantiesNode.isEmpty()) {
            logger.info("Setting orderItems' price to 0 because chosenWarranties -> data -> warranties is empty");
            orderItems.forEach(orderItem -> orderItem.setPrice("0"));
        }
    }

    /**
     * Gestisce la normalizzazione della start_date.
     */
    public void normalizeStartDate(OrderItemEntity orderItem) {
        Optional.ofNullable(orderItem.getStart_date())
                .filter(date -> date.toLocalDate().isAfter(LocalDate.now()))
                .ifPresent(date -> {
                    LocalDateTime normalizedDate = date.toLocalDate().atStartOfDay();
                    orderItem.setStart_date(normalizedDate);
                    logger.debug("Normalized start_date for orderItem {} to {}", orderItem.getId(), normalizedDate);
                });
    }

    /**
     * Verifica che il prezzo non sia vuoto e lo imposta al packet premium se necessario.
     */
    public void ensurePriceNotEmpty(List<OrderItemEntity> orderItems, Packet packetResponse) {
        orderItems.forEach(orderItem -> {
            if (orderItem.getPrice() == null || orderItem.getPrice().isEmpty()) {
                double price = this.calculateSecurePrice(packetResponse, orderItem);
                orderItem.setPrice(String.valueOf(price));
            }
        });
    }

    /**
     * Gestisce le date per prodotti tim-for-ski.
     */
    public void handleTimSkiDates(OrderItemEntity orderItem, Packet packetResponse, OrderEntity reqEntity) {
        String sku = packetResponse.packetResponse().getData().getSku();
        if (sku == null || !sku.startsWith("tim-for-ski")) {
            return;
        }

        String name = packetResponse.packetResponse().getData().getName();
        boolean isSeasonal = name != null && name.startsWith("Seasonal");

        if (!isSeasonal) {
            // Logica specifica per tim-for-ski non seasonal
            OrderItemEntity firstReqItem = reqEntity.getOrderItem().get(0);
            LocalDateTime startDate = checkStartDateTimSki(sku, firstReqItem.getStart_date());
            orderItem.setStart_date(startDate);

            int days = Optional.ofNullable(firstReqItem.getDays()).orElse(1);
            LocalDateTime expirationDate = checkExpirationDateTimSki(sku, days - 1, startDate, firstReqItem.getExpiration_date());
            orderItem.setExpiration_date(expirationDate);

            logger.debug("Set tim-for-ski dates for orderItem {}: start={}, expiration={}",
                        orderItem.getId(), startDate, expirationDate);
        }
    }

    /**
     * Gestisce le date standard per prodotti non tim-for-ski.
     */
    public void handleStandardDates(OrderItemEntity orderItem, Packet packetResponse, OrderEntity reqEntity) {
        String sku = packetResponse.packetResponse().getData().getSku();
        String name = packetResponse.packetResponse().getData().getName();

        boolean isTimForSki = sku != null && sku.startsWith("tim-for-ski");
        boolean isSeasonal = name != null && name.startsWith("Seasonal");

        if (isTimForSki && !isSeasonal) {
            return; // Gestito da handleTimSkiDates
        }

        Product product = packetResponse.packetResponse().getData().getProduct();
        JsonNode properties = Optional.ofNullable(product)
                .map(Product::getConfiguration)
                .map(cfg -> cfg.getProperties())
                .orElse(null);

        OrderItemEntity firstReqItem = reqEntity.getOrderItem().get(0);
        int days = Optional.ofNullable(firstReqItem.getDays()).orElse(1);

        // Calcolo start_date
        LocalDateTime startDate = Optional.ofNullable(firstReqItem.getStart_date())
                .orElseGet(() -> Optional.ofNullable(packetResponse.packetResponse().getData().getFixedStartDate())
                        .orElseGet(() -> Utility.setStartDate(Optional.ofNullable(product).map(Product::getCode).orElse(""))));
        orderItem.setStart_date(startDate);

        // Calcolo expiration_date
        LocalDateTime expirationDate = Optional.ofNullable(packetResponse.packetResponse().getData().getFixedEndDate())
                .orElseGet(() -> {
                    if (properties != null && days > 0 && properties.hasNonNull("durationFromDays") && properties.get("durationFromDays").asBoolean()) {
                        return startDate.toLocalDate().plusDays(days).atStartOfDay().minus(1, ChronoUnit.MILLIS);
                    } else if (packetResponse.packetResponse().getData().getDuration() != null && packetResponse.packetResponse().getData().getDurationType() != null) {
                        String productCode = Optional.ofNullable(product).map(Product::getCode).orElse("");
                        int durationValue = days > 0 ? days : Integer.parseInt(packetResponse.packetResponse().getData().getDuration());
                        return Utility.getOrderDuration(Utility.setStartDate(productCode), durationValue, packetResponse.packetResponse().getData().getDurationType());
                    } else if (packetResponse.packetResponse().getData().getPacketDuration() != null &&
                            packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration") != null) {
                        String productCode = Optional.ofNullable(product).map(Product::getCode).orElse("");
                        JsonNode packetDuration = packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration").get(0);
                        return Utility.getOrderDuration(
                                Utility.setStartDate(productCode),
                                packetDuration.get("duration").asInt(),
                                packetDuration.get("durationType").asText());
                    }
                    return null;
                });

        if(product.getConfiguration().getProperties().has("minus_day")){
            int minusDay = product.getConfiguration().getProperties().get("minus_day").asInt();
           expirationDate=expirationDate.minusDays(minusDay);
        }
        orderItem.setExpiration_date(expirationDate);

        logger.debug("Set standard dates for orderItem {}: start={}, expiration={}",
                    orderItem.getId(), startDate, expirationDate);
    }

    // Metodi helper per tim-for-ski (copiati da MapperOrder)
    private LocalDateTime checkStartDateTimSki(String sku, LocalDateTime startDate) {
        if (startDate == null) {
            switch (sku) {
                case "tim-for-ski-silver-day":
                case "tim-for-ski-gold-day":
                case "tim-for-ski-platinum-day":
                    return LocalDateTime.now();
            }
        }
        return startDate;
    }

    private LocalDateTime checkExpirationDateTimSki(String sku, Integer daysNumber, LocalDateTime startDate, LocalDateTime expirationDate) {
        if (daysNumber != null) {
            switch (sku) {
                case "tim-for-ski-silver-day":
                case "tim-for-ski-gold-day":
                case "tim-for-ski-platinum-day":
                    return startDate.toLocalDate().plusDays(daysNumber + 1).atStartOfDay().minus(1, ChronoUnit.MILLIS);
            }
        }
        return expirationDate;
    }
}
