package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.common.CommonUtils;
import it.yolo.common.Utility;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.*;
import it.yolo.mapper.OrderMapper;
import it.yolo.model.OrderBoundaryResponse;
import it.yolo.model.OrderItemBoundaryRequest;
import it.yolo.records.Catalog;
import it.yolo.records.Packet;
import it.yolo.records.Product;
import it.yolo.repository.AnagStatesRepository;
import it.yolo.repository.OrderItemRepository;
import it.yolo.repository.OrderRepository;
import it.yolo.service.client.ServiceClientCatalog;
import it.yolo.service.client.ServiceClientPackets;
import it.yolo.service.client.ServiceClientProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@RequestScoped
@Slf4j
public class OrderService {

    @Inject
    OrderRepository repo;

    @Inject
    AnagStatesRepository stateRepo;

    @Inject
    OrderItemRepository itemRepo;

    @Inject
    OrderHistoryService orderHistoryService;

    @Inject
    ServiceClientCatalog serviceClientCatalog;

    @Inject
    ServiceClientProduct serviceClientProduct;

    @Inject
    ServiceClientPackets serviceClientPacket;

    @Inject
    CommonUtils commonUtils;

    @Inject
    Logger logger;

    @Inject
    JsonWebToken jsonWebToken;

    @ConfigProperty(name = "intermediary.users")
    String intermediaryUser;


    private static String STATE_ORDER_CREATE = "Draft";
    private static String INSURANCE = "insurance_info";

    //variabile uguale a 0 in quanto a oggi la corrispondenza tra order e product è univoca
    private static int ORDER_ITEM = 0;

    //in questa versione viene settato a zero siccome si lavora con il prodotto
    private static int DEFAULT_PACKET = 0;

    @Transactional
    @WithSpan("OrderService.createOrder")
    public OrderEntity createOrder(
            @SpanAttribute("arg.entity") OrderEntity entity,
            String token) {
        Long productId = Long.valueOf(entity.getProductId());
        String orderCode = Utility.generateOrderCode();
        //ORDINE INSERITO SEMPRE CON IL PRIMO STATO DRAFT
        AnagStatesEntity stateOrderCreate = stateRepo.find("state", STATE_ORDER_CREATE).firstResult();
        //INVOCAZIONE PRODUCT PER RECUPERO DETTAGLIO PRODOTTI
        Product productResponse = invokeProductByProductId(productId, token);
        //SET ORDER-CODE
        entity.setOrderCode(orderCode);
        //DA INSERIRE ID PACCHETTO BASE
        if (entity.getPacketId() == null) {
            entity.setPacketId(DEFAULT_PACKET);
        }
        //SET PRODUCT TYPE
        entity.setProductType(productResponse.productType());
        entity.setId(null);
        //SET ANAG-STATE
        entity.setAnagStates(stateOrderCreate);
        entity.setIntermediaryOrder(checkTokenIntermediary());
        //SET ORDER && SET PRODUCT ID
        entity.getOrderItem().forEach(orderItem -> {
            orderItem.setOrders(entity);
            orderItem.setProduct_id(entity.getProductId());
        });
        //SET QUANTITY
        entity.getOrderItem().forEach(orderItem -> {
            if (orderItem.getQuantity() == null) {
                Integer quantity = entity.getOrderItem().size() > 0 ? entity.getOrderItem().size() : 1;
                orderItem.setQuantity(quantity);
            }
        });
        //LOGICA SU PRICE
        entity.getOrderItem().forEach(orderItem -> {
            if (orderItem.getPrice() == null) {
                orderItem.setPrice(String.valueOf(productResponse.price()));
            }
            if (orderItem.getQuantity() != null) {
                double price = Double.valueOf(orderItem.getPrice()) * orderItem.getQuantity();
                orderItem.setPrice(String.valueOf(price));
            }
            if (orderItem.getDays() != null) {
                double price = Double.valueOf(orderItem.getPrice()) * orderItem.getDays();
                orderItem.setPrice(String.valueOf(price));
            }
        });
        // SET START-DATE
        entity.getOrderItem().forEach(orderItem -> {
            if (orderItem.getStart_date() == null) {
                orderItem.setStart_date(Utility.setStartDate(productResponse.code()));
            }
        });

        // SET EXPIRATION-DATE
        entity.getOrderItem().forEach(orderItem -> {
            if (orderItem.getExpiration_date() == null) {
                orderItem.setExpiration_date(
                        Utility.getOrderDuration(
                                orderItem.getStart_date(),
                                productResponse.duration(),
                                productResponse.durationType())
                );
            }
        });
        repo.persist(entity);
        orderHistoryService.createHistoryForOrderInsert(entity);
        entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        return entity;
    }
    @WithSpan("OrderService.readOrder")
    public OrderEntity readOrder(
            @SpanAttribute("arg.id") Long id) {
        OrderEntity entity=repo.findById(id);
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by id=%d not found", id));
        }
        return entity;
    }

    @WithSpan("OrderService.readOrderByOrderCode")
    public OrderEntity readOrderByOrderCode(
            @SpanAttribute("arg.id") String orderCode) {
        OrderEntity entity=repo.find("order_code", orderCode).firstResult();
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by code not found: "+orderCode));
        }
        return entity;
    }

    @Transactional
    @WithSpan("OrderService.updatefailed")
    public OrderEntity updatefailedStatus(
            @SpanAttribute("arg.OrderEntity") OrderEntity entity,
            @SpanAttribute("arg.anag.state.failed.entity") AnagStatesEntity stateFailed) {

        OrderEntity resultDb = repo.find("order_code", entity.getOrderCode()).firstResult();
        resultDb.setAnagStates(stateFailed);
        OrderEntity orderEntityFailed= repo.getEntityManager().merge(resultDb);
        return orderEntityFailed;
    }



    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity updateOrder(
            @SpanAttribute("arg.id") String order_code,
            @SpanAttribute("arg.entity") OrderEntity entity, String token, String discount) throws ProductClientException, CatalogClientException, ValidationStructureProductException, PacketException {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if (resultDb == null) {
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        Product productResponse = invokeProductByProductId(Long.valueOf(resultDb.getProductId()), token);
        entity.setProductId(productResponse.id());
        if(!resultDb.getIntermediaryOrder()){
            resultDb.setIntermediaryOrder(checkTokenIntermediary());
        }
        AnagStatesEntity stateUpdating = stateRepo.find("state", entity.getAnagStates().getState()).firstResult();
        if(entity.getFieldToRecover()!=null){
            resultDb.setFieldToRecover(entity.getFieldToRecover());
        }
        if(entity.getPaymentToken()!=null && !entity.getPaymentToken().isEmpty()){
            resultDb.setPaymentToken(entity.getPaymentToken());
        }
        if(entity.getPaymentTransactionId()!=null) {
            resultDb.setPaymentTransactionId(entity.getPaymentTransactionId());
        }
        if(entity.getPaymentType() !=null && !entity.getPaymentType().isEmpty()){
            resultDb.setPaymentType(entity.getPaymentType());
        }
        if(entity.getCustomerId() != null){
            resultDb.setCustomerId(entity.getCustomerId());
        }
        resultDb.setAnagStates(stateUpdating);
        OrderEntity orderAfterUpdate  = repo.getEntityManager().merge(resultDb);
        List<OrderItemEntity> resultOrderItem=updateOrderItem(orderAfterUpdate, entity, productResponse,discount);
        if(resultOrderItem!=null){
            orderAfterUpdate.setOrderItem(resultOrderItem);
        }
        orderHistoryService.createHistoryForOrderPut(orderAfterUpdate, null);
        orderAfterUpdate.setStepState(commonUtils.getStepState(entity.getProductId()));
        return orderAfterUpdate;
    }

    @WithSpan("OrderService.listOrders")
    public List<OrderEntity> listOrders() {
        return repo.listAll();
    }

    @Transactional
    private List<OrderItemEntity> updateOrderItem(OrderEntity updatedEntity, OrderEntity reqEntity, Product productResponse,String discount) {
        List<OrderItemEntity> items = itemRepo.find("order_id", updatedEntity.getId()).list();
        JsonNode insuredItem = checkInsuredItem(items, reqEntity, productResponse);
        items = checkStartDate(items, productResponse, reqEntity);


        //paracadute discount
        items.forEach( item ->{
            if(discount !=null && discount.equalsIgnoreCase("sconto1@@")){
                Integer discountPercentage=100;
                Float price=Float.parseFloat(item.getPrice());
                item.setPrice(String.valueOf(price-((price/100)*discountPercentage)));
            }
        });

        //verifica se un orderItem contiene un prezzo
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(r ->r.getPrice()!=null).count()>0){
            List<OrderItemEntity> itemByPrice=reqEntity.getOrderItem().stream().filter(req ->req.getPrice()!=null).collect(Collectors.toList());
            items.forEach(price -> {
                itemByPrice.forEach(iByPrice->{
                    price.setPrice(iByPrice.getPrice());
                });
            });
        }

        //salvataggio informazioni relavite alla quotation
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).count()>0){
            List<OrderItemEntity> itemByQuotation=reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).collect(Collectors.toList());
            for (OrderItemEntity orderItemByQuotation: itemByQuotation) {
                for (OrderItemEntity orderItemEntity:items) {
                    orderItemEntity.setQuotation(orderItemByQuotation.getQuotation());
                }
            };
        }

        items.forEach(item ->{
            item.setInsured_item(insuredItem);
            itemRepo.getEntityManager().merge(item);
        });

        return items;
    }

    @WithSpan("OrderService.readOrderByOrderCode")
    public OrderEntity readOrderByOrderCodeAndCustomerId(
            @SpanAttribute("arg.id") String orderCode, int customerId, String token) {
        OrderEntity order = repo.findByOrderCodeAndCustomerId(orderCode, customerId);
        if(order.getPacketId() != 0) {
            Packet packetResponse= invokePacketByPacketId(Long.valueOf(order.getPacketId()), token);
            order.setVersion("v2");
        } else {
            Product productResponse = invokeProductByProductId(Long.valueOf(order.getProductId()), token);
            order.setVersion("v1");
        }
        order.setStepState(commonUtils.getStepState(order.getProductId()));
        return order;
    }
    @WithSpan("OrderService.readDetailsOrder")
    public OrderEntity readDetailsOrder(
            @SpanAttribute("arg.id") String orderCode,
            String token) {
        OrderEntity entity = repo.find("order_code",orderCode).firstResult();
        if(entity.getPacketId() != 0) {
            Packet packetResponse= invokePacketByPacketId(Long.valueOf(entity.getPacketId()), token);
        } else {
            Product productResponse = invokeProductByProductId(Long.valueOf(entity.getProductId()), token);
        }
        return entity;
    }




    public OrderEntity readOrderDetailsByOrdeItemId(Long orderItemId, String token) {
        OrderItemEntity orderItemEntity = itemRepo.find("id", orderItemId).firstResult();
        if(orderItemEntity.getOrders().getPacketId() != 0) {
            Long packetId=Long.valueOf(orderItemEntity.getOrders().getPacketId());
            invokePacketByPacketId(packetId,token);
            return orderItemEntity.getOrders();
        }
            Long productId=Long.valueOf(orderItemEntity.getOrders().getProductId());
            invokeProductByProductId(productId, token);

        return orderItemEntity.getOrders();
    }

    public Map<Long, OrderBoundaryResponse> getCodesFromIds(List<Long> ids) {
        List<OrderEntity> orders=repo.findByIdIn(ids);
        Map<Long, OrderBoundaryResponse> map=new HashMap<>();
        orders.stream().forEach(order -> {
            map.put(order.getId(), OrderMapper.INSTANCE.entityToResponse(order));
        });
        return map;
    }

    private Catalog invokeCatalogByProductCode(String product_code, JsonNode asset) throws CatalogClientException, ValidationStructureProductException {
        Catalog catalogResponse = serviceClientCatalog.findByProductCode(product_code);

        if(catalogResponse.enabled()){
            Utility.validationProductStructure(catalogResponse.goodsStructure(), asset);
        }
        return catalogResponse;
    }

    /*
     * CHIAMATA A V1 PRODUCT
     */
    private Product invokeProductByProductId(Long id, String token) throws ProductClientException {
        Product productResponse = serviceClientProduct.findById(id, token);
        return productResponse;
    }

    /*
     * CHIAMATA A V2 PRODUCT
     */
    private Packet invokePacketByPacketId(Long id, String token) throws PacketException {
        Packet packetResponse = serviceClientPacket.findById(id, token);
        return packetResponse;
    }

    private JsonNode checkInsuredItem(List<OrderItemEntity> orderItemEntities, OrderEntity entity, Product productResponse){
        JsonNode insuredItem = null;
        for (OrderItemEntity orderItem : orderItemEntities) {
            if (orderItem.getInsured_item() != null) {
                insuredItem = orderItem.getInsured_item();
            } else if (entity.getOrderItem() != null && !entity.getOrderItem().isEmpty()) {
                insuredItem = entity.getOrderItem().get(ORDER_ITEM).getInsured_item();
                Catalog catalogResponse = invokeCatalogByProductCode(productResponse.code(), insuredItem);
            }
        }
        return insuredItem;
    }

    private List<OrderItemEntity> checkStartDate(List<OrderItemEntity> orderItems, Product productResponse, OrderEntity entity){
        for(OrderItemEntity orderItem : orderItems){
            if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(ORDER_ITEM).getStart_date() != null){
                orderItem.setStart_date(entity.getOrderItem().get(ORDER_ITEM).getStart_date());
                if (StringUtils.isNotBlank(productResponse.durationType())) {
                    entity.getOrderItem().get(ORDER_ITEM).setExpiration_date(
                            Utility.getOrderDuration(
                                    entity.getOrderItem().get(ORDER_ITEM).getStart_date(),
                                    productResponse.duration(),
                                    productResponse.durationType()));
                }
            }
        }
        return orderItems;
    }

    @Transactional
    @WithSpan("OrderService.updateEmission")
    public OrderEntity updateEmission(
            @SpanAttribute("arg.id") String order_code, Long orderItemId, String token, JsonNode emission) throws ProductClientException, CatalogClientException, ValidationStructureProductException, PacketException {

        //recupero l'ordine a db
        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if (resultDb == null) {
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        //recupero l'order item
        OrderItemEntity orderItemDb = itemRepo.find("order_id", resultDb.getId()).firstResult();
        //setto la emission
        orderItemDb.setEmission(emission);
        itemRepo.getEntityManager().merge(orderItemDb);

        OrderEntity orderAfterUpdate  = repo.getEntityManager().merge(resultDb);
        if(orderItemDb!=null){
            orderAfterUpdate.getOrderItem().set(0, orderItemDb);
        }
        return orderAfterUpdate;
    }

    @Transactional
    public List<OrderEntity> updateOrderItemsPrices(List<OrderItemBoundaryRequest> orderItemRequests) {

        List<OrderEntity> orders = new ArrayList<>();

        orderItemRequests.forEach(item->{
            Optional<OrderItemEntity> resultDb = itemRepo.findByIdOptional(item.getId());
            logger.info("updateOrderItemsPrices start updating orderItems");
            if(resultDb.isPresent()) {
                if(item.getPrice()!=null){
                    logger.info("updateOrderItemsPrices price updated :"+item.getPrice());
                    resultDb.get().setPrice(item.getPrice());
                }
                if(item.getPromotion()!=null){
                logger.info("updateOrderItemsPrices set promotion :"+item.getPromotion());
                    resultDb.get().setPromotion(item.getPromotion());
                }
                itemRepo.getEntityManager().merge(resultDb.get());
                orders.add(resultDb.get().getOrders());
            }
        });

        return orders;
    }

    @Transactional
    public OrderEntity updateOrderWithEstimate (String orderCode, String estimateId){

        OrderEntity order = repo.find("order_code", orderCode).firstResult();

        if(order.getOrderItem().get(0).getInstance()==null){
            order.getOrderItem().get(0).setInstance(JsonNodeFactory.instance.objectNode());
        }
        ((ObjectNode)order.getOrderItem().get(0).getInstance()).put("estimateId",estimateId);

        return repo.getEntityManager().merge(order);
    }

    public Boolean checkTokenIntermediary(){
        //check token appartenente a technical-users o intermediary-users
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        if(groupsJson!=null) {
            for (JsonString group : groupsJson) {
                groups.add(group.getString());
            }
            return groups.contains(intermediaryUser);
        }
        return false;
    }
}
