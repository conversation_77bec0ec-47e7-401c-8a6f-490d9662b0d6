package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import it.yolo.client.request.PgwData;
import it.yolo.client.request.PgwRequest;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.entity.*;
import it.yolo.mapper.OrderMapper;
import it.yolo.model.OrderBoundaryResponse;

import it.yolo.service.client.ServiceClientCustomer;
import it.yolo.service.client.ServiceClientProductV3;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import it.yolo.service.v3.JwtUtilsService;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;


@RequestScoped
public class PgwService {

    @Inject
    ServiceClientCustomer serviceClientCustomer;

    @Inject
    ServiceClientProductV3 serviceClientProduct;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    Logger logger;

    @Inject
    JwtUtilsService jwtUtils;


    public PgwRequest createPgwRequest(OrderEntity orderEntity, String token) throws Exception {
        boolean isTechnical = jwtUtils.checkTokenTI();

        // customer
        String ndg;
        Integer id;
        JsonNode customer;
        if(!isTechnical) {
            ndg = jsonWebToken.getClaim("username");
            logger.info("Find customer by ndg: " + ndg);
            customer = serviceClientCustomer.findByNdg(ndg);
        } else {
            id = orderEntity.getCustomerId();
            logger.info("Find customer by id: " + id);
            customer = serviceClientCustomer.findById(id, token);
        }

        // product
        logger.info("Get product by id: " + orderEntity.getProductId().toString());
        OrderItemEntity orderItemEntity = orderEntity.getOrderItem().get(0);
        LocalDateTime startDate = orderItemEntity.getStart_date();

//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
//        String formattedDate = startDate.format(formatter);
//
//        LocalDateTime startDateWithMilliseconds = LocalDateTime.parse(formattedDate, formatter);

        orderItemEntity.setStart_date(startDate.plus(1, ChronoUnit.MILLIS));
        JsonNode product = serviceClientProduct.getByIdJSON(Long.valueOf(orderEntity.getProductId()), token);

        // order
        OrderBoundaryResponse resultDbResponse = OrderMapper.INSTANCE.entityToResponse(orderEntity);
        logger.info("Get productResponse by id: " + orderEntity.getProductId().toString());
        ProductResponse productResponse = serviceClientProduct.getById(Long.valueOf(orderEntity.getProductId()), token);
        resultDbResponse.setProduct(productResponse);
        JsonNode order = new ObjectMapper().getNodeFactory().pojoNode(resultDbResponse);
        ObjectNode orderData = new ObjectMapper().createObjectNode();
        orderData.set("data", order);

        // pgwRequest
        PgwData pgData = new PgwData();
        pgData.setOrder(orderData);
        pgData.setProduct(product);
        pgData.setCustomer(customer);

        PgwRequest pgRequest = new PgwRequest();
        pgRequest.setData(pgData);

        return pgRequest;
    }

    
}
