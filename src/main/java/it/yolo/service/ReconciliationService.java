package it.yolo.service;

import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.entity.OrderEntity;
import it.yolo.repository.OrderItemRepository;
import it.yolo.repository.OrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.List;

@RequestScoped
@Slf4j
public class ReconciliationService {

    @Inject
    OrderRepository orderRepo;

    @Inject
    OrderItemRepository orderItemRepo;

    @Inject
    Logger logger;

    private static int ORDER_ITEM = 0;


    @Transactional
    @WithSpan("OrderService.createOrder")
    public List<Long> reconciliateOrder(@SpanAttribute("arg.email") String email, @SpanAttribute("arg.customerId") Integer customerId) {
        List<Long> ids= orderItemRepo.getEntityManager().createNativeQuery("SELECT o.order_id FROM \"order\".order_item o where" +
                        " o.insured_item ->> 'representative_email' = :email ;")
                .setParameter("email", email).getResultList();
        if(ids.size()>0){
            List<OrderEntity> orders=orderRepo.findByIdIn(ids);
            if(orders!=null && !orders.isEmpty()) {
                orders.forEach(order -> {
                    order.setCustomerId(customerId);
                    orderRepo.getEntityManager().merge(order);
                });
            }
        }
        return ids;
    }



}
