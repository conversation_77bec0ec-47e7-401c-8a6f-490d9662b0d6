package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.IamClient;
import it.yolo.model.iam.LoginRequestDto;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServiceIam {

    @Inject
    @RestClient
    IamClient iamClient;

    @ConfigProperty(name = "tch.usr")
    String username ;

    @ConfigProperty(name = "quarkus.auth.password")
    String password;

    public String generateToken() {
        try {
            LoginRequestDto loginRequest = new LoginRequestDto();
            loginRequest.setUsername(username);
            loginRequest.setPassword(password);
            Response response = iamClient.getToken(loginRequest);
            JsonNode token = response.readEntity(JsonNode.class);
            return token.get("token").asText();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
