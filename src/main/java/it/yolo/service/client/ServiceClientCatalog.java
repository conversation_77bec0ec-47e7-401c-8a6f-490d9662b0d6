package it.yolo.service.client;

import it.yolo.client.CatalogClient;
import it.yolo.client.response.client.catalog.CatalogResponse;
import it.yolo.exception.CatalogClientException;
import it.yolo.records.Catalog;
import it.yolo.records.ErrorMessage;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
@Slf4j
public class ServiceClientCatalog {
    @Inject
    @RestClient
    CatalogClient catalogClient;

    @Inject
    JsonWebToken jsonWebToken;

    public Catalog findByProductCode(String product_code) throws CatalogClientException {
        try{
            CatalogResponse responseCatalogClient= catalogClient.findByProductCode(product_code);
            Catalog catalogGoods= new Catalog(responseCatalogClient.getData().getAsset(),responseCatalogClient.getData().getEnabled());
            return catalogGoods;
        }catch(Exception e){
            log.error("Catalog error", e);
            throw new CatalogClientException("Catalog Error", ErrorMessage.MSG_CATALOG_ERROR + " "+ e.getStackTrace().toString());
        }
    }


}
