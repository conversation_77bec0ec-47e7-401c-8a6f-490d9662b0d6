package it.yolo.service.client;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import be.digitech.iadtoken.grpc.Empty;
import io.quarkus.logging.Log;
import it.yolo.client.grpc.IadTokenGrpcClient;
import it.yolo.service.v3.JwtUtilsService;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.CustomerClient;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.Response;
import java.util.List;

@RequestScoped
public class ServiceClientCustomer {

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    IadTokenGrpcClient tokenGrpcClient;

    @Inject
    JwtUtilsService jwtUtilsService;

    public JsonNode findByNdg(String ndg) throws Exception {
        Log.infov("Find by ndg: {0}", ndg);
        if(jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
            Empty empty = null;
            String token = tokenGrpcClient.technicalLogin(empty).getToken();
            ndg = jwtUtilsService.extractUsernameFromAnotherToken(token);
        }
        if(jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
            Empty empty = null;
            String token = tokenGrpcClient.intermediaryTechnicalLogin(empty).getToken();
            ndg = jwtUtilsService.extractUsernameFromAnotherToken(token);
        }
        Response responseCustomer;
        try {
            responseCustomer= customerClient.findByNdg(ndg);
            Log.infov("Response customer: {0}", responseCustomer.getStatus());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
        JsonNode customerResponse = responseCustomer.readEntity(JsonNode.class);
        return customerResponse;
    }

    public List<Long> findByTaxCodeAndEmail(Long id, String token) throws Exception {
        Response responseCustomer;
        try {
            responseCustomer= customerClient.findByIdToTaxCodeAndEmailIds(id, token);
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }

        return responseCustomer.readEntity(new GenericType<List<Long>>() {});
    }

    public List<Long> findByTaxcode(Long id, String token) throws Exception {
        Response responseCustomer;
        try {
            responseCustomer= customerClient.findByIdToTaxCode(id, token);
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }

        return responseCustomer.readEntity(new GenericType<List<Long>>() {});
    }

    public JsonNode findById(Integer ndg, String jwt) throws Exception {
        Response responseCustomer;
        try {
            responseCustomer= customerClient.findById(ndg, jwt);
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
        JsonNode customerResponse = responseCustomer.readEntity(JsonNode.class);
        return customerResponse;
    }
    
}
