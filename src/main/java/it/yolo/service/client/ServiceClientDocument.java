package it.yolo.service.client;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

import it.yolo.client.communicationManager.dto.CertificateResponseDto;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.document.dto.CertificateRequestDto;
import it.yolo.client.document.dto.UploadCertificateRequestDto;
import it.yolo.exception.DocumentException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import com.fasterxml.jackson.databind.JsonNode;

@RequestScoped
public class ServiceClientDocument {
    @Inject
    @RestClient
    DocumentClient documentClient;

    public CertificateResponseDto generateCertificate(CertificateRequestDto certificateRequestDto){
        Response response;
        try {
            response=documentClient.generateCertficate(certificateRequestDto);
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        CertificateResponseDto certificateResponseDto=response.readEntity(CertificateResponseDto.class);
        //String certificate=certificateResponseDto.getFile();
        //certificateRequestDto.setCertificate(certificate);
        return certificateResponseDto;
    }


    public JsonNode uploadCertificate(UploadCertificateRequestDto uploadCertificateRequestDto){
        JsonNode response;
        try {
            response=documentClient.uploadCertificate(uploadCertificateRequestDto).readEntity(JsonNode.class);;
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }
        
        return response;
    }
}

