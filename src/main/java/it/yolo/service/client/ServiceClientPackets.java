package it.yolo.service.client;

import it.yolo.client.PacketClient;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.exception.PacketException;
import it.yolo.records.ErrorMessage;
import it.yolo.records.Packet;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ServiceClientPackets {
    @Inject
    @RestClient
    PacketClient packetClient;

    PacketResponse packetResponse;

    public Packet findById(Long id, String token) throws PacketException {
        try{
            PacketResponse packetResponse = packetClient.findById(id, token);
            Packet packet = new Packet(packetResponse);
            setPacketResponse(packetResponse);
            return packet;
        }catch(Exception e){
            throw new PacketException("Packet Exception", ErrorMessage.MSG_PACKET_ERROR+""+e.getStackTrace());
        }
    }

    public PacketResponse getPacketResponse() {
        return packetResponse;
    }

    public void setPacketResponse(PacketResponse packetResponse) {
        this.packetResponse = packetResponse;
    }

}
