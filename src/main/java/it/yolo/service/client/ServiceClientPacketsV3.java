package it.yolo.service.client;

import it.yolo.client.PacketClientV3;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.packet.PacketsResponse;
import it.yolo.exception.PacketException;
import it.yolo.model.TipologiaRequest;
import it.yolo.records.ErrorMessage;
import it.yolo.records.Packet;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import java.util.ArrayList;
import java.util.List;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ServiceClientPacketsV3 {
    @Inject
    @RestClient
    PacketClientV3 packetClient;

    PacketResponse packetResponse;

    public Packet findById(Long id, String token) throws PacketException {
        try{
            if(id==null || id.intValue()==0){
                return new Packet(null);
            }
            PacketResponse packetResponse = packetClient.findById(id, token);
            Packet packet = new Packet(packetResponse);
            setPacketResponse(packetResponse);
            return packet;
        }catch(Exception e){
            throw new PacketException("Packet Exception", ErrorMessage.MSG_PACKET_ERROR+""+e.getStackTrace());
        }
    }

    public Packet findById(Long id, String token, String language) throws PacketException {
        try{
            if(id==null || id.intValue()==0){
                return new Packet(null);
            }
            PacketResponse packetResponse = packetClient.findById(id, token, language);
            Packet packet = new Packet(packetResponse);
            setPacketResponse(packetResponse);
            return packet;
        }catch(Exception e){
            throw new PacketException("Packet Exception", ErrorMessage.MSG_PACKET_ERROR+""+e.getStackTrace());
        }
    }

    public Packet findByTipologia(TipologiaRequest request, String token) throws PacketException {
        try{
            PacketResponse packetResponse = packetClient.findByTipologia(request, token);
            Packet packet = new Packet(packetResponse);
            setPacketResponse(packetResponse);
            return packet;
        }catch(Exception e){
            throw new PacketException("Packet Exception", ErrorMessage.MSG_PACKET_ERROR+""+e.getStackTrace());
        }
    }

    public PacketResponse getPacketResponse() {
        return packetResponse;
    }

    public void setPacketResponse(PacketResponse packetResponse) {
        this.packetResponse = packetResponse;
    }

    public List<Packet> findByProductId(Long productId, String token) throws PacketException {
        try {
            if (productId == null || productId.intValue() == 0) {
                return new ArrayList<>();
            }
            PacketsResponse packetsResponse = packetClient.findByProductId(productId, token);
            List<Packet> packets = new ArrayList<>();
            if (packetsResponse != null && packetsResponse.getData() != null) {
                for (it.yolo.client.response.client.packet.Data data : packetsResponse.getData()) {
                    PacketResponse packetResponse = new PacketResponse();
                    packetResponse.setData(data);
                    packets.add(new Packet(packetResponse));
                }
            }
            return packets;
        } catch (Exception e) {
            throw new PacketException("Packet Exception", ErrorMessage.MSG_PACKET_ERROR + "" + e.getStackTrace());
        }
    }

}
