package it.yolo.service.client;

import it.yolo.client.ProductClient;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.exception.ProductClientException;
import it.yolo.records.ErrorMessage;
import it.yolo.records.Product;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.logging.Logger;

@RequestScoped
public class ServiceClientProduct {

    @Inject
    @RestClient
    ProductClient productClient;

    ProductResponse productResponse;

    public Product findById(Long id, String token) throws ProductClientException {
        try{
            ProductResponse productResponse= productClient.findById(id, token);
            Product product = new Product(
                    productResponse.getData().getPrice(),
                    productResponse.getData().getCode(),
                    productResponse.getData().getId(),
                    productResponse.getData().getDuration(),
                    productResponse.getData().getProductType(),
                    productResponse.getData().getDurationType(),
                    productResponse.getData().getLegacy());
            setProductResponse(productResponse);
            return product;
        }catch(Exception e){
            throw new ProductClientException("Product Client Exception",ErrorMessage.MSG_PRODUCT_ERROR + " "+ e.getStackTrace().toString());
        }
    }

    public ProductResponse getProductResponse() {
        return productResponse;
    }

    public void setProductResponse(ProductResponse productResponse) {
        this.productResponse = productResponse;
    }
}

