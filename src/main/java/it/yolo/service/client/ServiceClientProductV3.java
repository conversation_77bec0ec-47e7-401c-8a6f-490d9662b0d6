package it.yolo.service.client;

import it.yolo.client.ProductClientV3;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.exception.ProductClientException;
import it.yolo.records.ErrorMessage;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import com.fasterxml.jackson.databind.JsonNode;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
@Slf4j
public class ServiceClientProductV3 {

    @Inject
    @RestClient
    ProductClientV3 productClient;

    public ProductResponse getById(Long id, String token) throws ProductClientException {
        try{
            ProductResponse productResponse= productClient.findById(id, token);
            return productResponse;
        }catch(Exception e){
            log.error("Product Client Exception", e);
            throw new ProductClientException("Product Client Exception",ErrorMessage.MSG_PRODUCT_ERROR + " "+ e.getStackTrace().toString());
        }
    }

    public ProductResponse getById(Long id, String token, String language) throws ProductClientException {
        try{
            ProductResponse productResponse= productClient.findById(id, token, language);
            return productResponse;
        }catch(Exception e){
            log.error("Product Client Exception", e);
            throw new ProductClientException("Product Client Exception",ErrorMessage.MSG_PRODUCT_ERROR + " "+ e.getStackTrace().toString());
        }
    }

    public JsonNode getByIdJSON(Long id, String token) throws ProductClientException {
        try{
            JsonNode productResponse= productClient.findByIdJSON(id, token).readEntity(JsonNode.class);
            return productResponse;
        }catch(Exception e){
            log.error("Product Client Exception", e);
            throw new ProductClientException("Product Client Exception",ErrorMessage.MSG_PRODUCT_ERROR + " "+ e.getStackTrace().toString());
        }
    }
}
