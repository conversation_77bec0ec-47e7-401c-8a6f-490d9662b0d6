package it.yolo.service.legacy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.StringValue;
import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.Response;
import io.grpc.StatusException;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.grpc.GrpcClient;
import io.smallrye.common.annotation.Blocking;
import it.yolo.client.PacketClientV3;
import it.yolo.client.ProductClientV3;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.packet.Product;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.YoloException;
import it.yolo.grpc.CustomerRequest;
import it.yolo.grpc.CustomerResponse;
import it.yolo.grpc.ServiceCustomerGrpc;
import it.yolo.repository.OrderItemRepository;
import it.yolo.repository.OrderRepository;
import it.yolo.service.ServiceIam;
import it.yolo.service.client.ServiceClientCustomer;

import it.yolo.service.v3.OrderItemCheckService;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RequestScoped
public class ServiceYinOrder {

    @Inject
    Logger log;

    @Inject
    ServiceIam iamService;

    @Inject
    OrderRepository orderRepository;

    @RestClient
    @Inject
    PacketClientV3 packetClient;

    @Inject
    OrderItemCheckService orderItemCheckService;

    @Inject
    OrderItemRepository orderItemRepository;

    @Inject
    ServiceClientCustomer serviceClientCustomer;
    @Inject


    //blockingStub=chiamata sincrona fra stub
    @GrpcClient("customer-service-grpc")
    ServiceCustomerGrpc.ServiceCustomerBlockingStub blockingStub;
    /*
     * @Inject
     *
     * @RestClient
     * YinClient yinClient;
     */

    /*
     * OKHTTPCLIENT SICCOME A RUNTIME VENIVA PASSATA LA URL DA RICHIAMARE
     * ATTEZIONE ALLE LIBRERIE UTILLIZZATE
     * CHIAMATA ESCLUSIVA PER YIN
     */
    @WithSpan("OrderService.callBackOnOrderUpdate")
    public JsonNode callBackOnOrderUpdate(String url,
                                          String orderNumber) {
        log.info("{service : OrderService start , url : " + url + "orderNumber : " + orderNumber);

        try {
            String resRead;
            JsonNode resReturn;
            OkHttpClient client = new OkHttpClient();
            client.setReadTimeout(20, TimeUnit.MINUTES);
            client.setConnectTimeout(20, TimeUnit.MINUTES);
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();
            log.info("{service : call yinClient start , url : " + url + "orderNumber : " + orderNumber);
            Response response = client.newCall(request).execute();
            ObjectMapper mapper = new ObjectMapper();
            resRead = response.body().string();
            log.info("{service OrderService : call yinClient end ,response : " + resRead);
            resReturn = mapper.readValue(resRead, JsonNode.class);
            return resReturn;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    @WithSpan("OrderService.callBackOnOrderUpdate")
    @Blocking
    public void associateUserToOrder(Long userId,
                                     String orderNumber, String ndg) throws Exception {
        log.info("{service : OrderService start , userId : " + userId + "orderNumber : " + orderNumber);
        Long customerId;
        OrderEntity orderEntity = orderRepository.find("order_code", orderNumber).firstResult();
        customerId = orderEntity.getCustomerId().longValue();
        if (!ndg.isEmpty()) {
            try {
                log.info("{service : OrderService start  callgrpc getId");
                CustomerRequest reqGrpc = CustomerRequest.newBuilder()
                        .setNdg(StringValue.of(ndg)).build();
                CustomerResponse res = blockingStub.getId(reqGrpc);
                customerId = res.getId().getValue();
                log.info("{service : OrderService callgrpc getId  " + customerId);
            } catch (Exception ex) {
                throw new Exception(ex.getMessage());
            }

        }
        orderEntity.setCustomerId(customerId.intValue());
        OrderEntity orderAfterUpdate = orderRepository.getEntityManager().merge(orderEntity);
    }




    @Transactional
    @WithSpan("OrderService.associateUserToOrderNoAuth")
    @Blocking
    public void associateUserToOrderNoAuth(Long userId,
                                     String orderNumber, String ndg) throws Exception {
        log.info("{service : OrderService start , userId : " + userId + "orderNumber : " + orderNumber+ "ndg : " + ndg);
        Long customerId;
        customerId = userId;
        OrderEntity orderEntity = orderRepository.find("order_code", orderNumber).firstResult();

        if(customerId!=null) {
            String token = iamService.generateToken();
            JsonNode customerResponse = serviceClientCustomer.findById(Integer.valueOf(customerId.intValue()),"Bearer "+token);
            ndg = customerResponse.get("data").get("registration_info").get("ndgCode").asText();
            String birthDate = customerResponse.get("data").get("date_of_birth").asText();
            PacketResponse packetResponse = packetClient.findById(Long.valueOf(orderEntity.getPacketId()), token);
            orderItemCheckService.checkAge(packetResponse.getData().getProduct(), ndg, LocalDateTime.now(), birthDate);
        }
        if (ndg!=null) {
            try {
                JsonNode customerResponse = serviceClientCustomer.findByNdg(ndg);
                customerId = customerResponse.get("data").get("id").asLong();

                log.info("{service : OrderService customerResponse getId  " + customerId);
            } catch (Exception ex) {
                throw new Exception(ex.getMessage());
            }
        }
        orderEntity.setCustomerId(customerId.intValue());
        OrderEntity orderAfterUpdate = orderRepository.getEntityManager().merge(orderEntity);
    }





    @Transactional
    @WithSpan("OrderService.updateStatusOrderById")
    public List<OrderItemEntity> updateStatusOrderById(String orderNumber,
                                                       String updateStatus) {
        log.info("{service : OrderService start updateStatusOrderById , orderNumber : " + orderNumber + "updateStatus : " + updateStatus);


        OrderEntity orderEntity = orderRepository.find("order_code", orderNumber).firstResult();
        List<OrderItemEntity> orderItemsEntity = orderItemRepository.find("order_id ", orderEntity.getId()).list();
        orderItemsEntity.forEach(item -> {
            item.setState(updateStatus);
            orderItemRepository.getEntityManager().merge(item);
        });

        return orderItemsEntity;
    }
}
