package it.yolo.service.question;

import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for processing questions with templating and visibility rules.
 * 
 * This service coordinates the application of both templating (placeholder
 * replacement)
 * and visibility rules to a list of questions. It ensures that questions are
 * processed
 * in the correct order: first visibility filtering, then templating on visible
 * questions.
 */
@ApplicationScoped
@Slf4j
public class QuestionProcessorService {

    @Inject
    QuestionTemplatingService templatingService;

    @Inject
    QuestionVisibilityService visibilityService;

    /**
     * Process a list of questions by applying visibility rules and templating.
     * 
     * Processing order:
     * 1. Filter questions based on visibility rules
     * 2. Apply templating to visible questions
     * 
     * @param questions   The list of questions to process
     * @param orderEntity The order entity containing data for processing
     * @return A new list containing only visible questions with processed templates
     */
    public List<Question> processQuestions(List<Question> questions, OrderEntity orderEntity) {
        if (questions == null || questions.isEmpty()) {
            log.debug("No questions to process");
            return new ArrayList<>();
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, returning original questions without processing");
            return new ArrayList<>(questions);
        }

        log.info("Processing {} questions with templating and visibility rules", questions.size());

        // Step 1: Filter questions based on visibility rules
        List<Question> visibleQuestions = filterVisibleQuestions(questions, orderEntity);

        log.info("Visibility filtering: {} out of {} questions are visible",
                visibleQuestions.size(), questions.size());

        // Step 2: Apply templating to visible questions
        List<Question> processedQuestions = applyTemplating(visibleQuestions, orderEntity);

        log.info("Question processing completed: {} questions processed", processedQuestions.size());

        return processedQuestions;
    }

    /**
     * Filter questions based on visibility rules.
     *
     * @param questions   The list of questions to filter
     * @param orderEntity The order entity containing data for rule evaluation
     * @return A list of questions that should be visible
     */
    private List<Question> filterVisibleQuestions(List<Question> questions, OrderEntity orderEntity) {
        log.debug("Filtering questions based on visibility rules");

        List<Question> visibleQuestions = questions.stream()
                .filter(question -> {
                    boolean isVisible = visibilityService.isQuestionVisible(question, orderEntity);
                    log.debug("Question {} (ID: {}) visibility: {}",
                            question.getContent(), question.getId(), isVisible);
                    return isVisible;
                })
                .collect(Collectors.toList());

        log.debug("Visibility filtering completed: {} visible questions", visibleQuestions.size());
        return visibleQuestions;
    }

    /**
     * Apply templating to a list of questions.
     *
     * @param questions   The list of questions to process
     * @param orderEntity The order entity containing data for templating
     * @return A list of questions with processed templates
     */
    private List<Question> applyTemplating(List<Question> questions, OrderEntity orderEntity) {
        log.debug("Applying templating to {} questions", questions.size());

        List<Question> processedQuestions = new ArrayList<>();

        for (Question question : questions) {
            Question processedQuestion = applyTemplatingToQuestion(question, orderEntity);
            processedQuestions.add(processedQuestion);
        }

        log.debug("Templating completed for {} questions", processedQuestions.size());
        return processedQuestions;
    }

    /**
     * Apply templating to a single question.
     * Creates a copy of the question with processed content to avoid modifying the
     * original.
     *
     * @param question    The question to process
     * @param orderEntity The order entity containing data for templating
     * @return A new question instance with processed content
     */
    private Question applyTemplatingToQuestion(Question question, OrderEntity orderEntity) {
        if (question == null) {
            return null;
        }

        String originalContent = question.getContent();
        if (originalContent == null || originalContent.trim().isEmpty()) {
            log.debug("Question {} has no content to process", question.getId());
            return question;
        }

        String processedContent = templatingService.processTemplate(originalContent, orderEntity);

        // Create a new question instance to avoid modifying the original
        Question processedQuestion = new Question();
        processedQuestion.setId(question.getId());
        processedQuestion.setContent(processedContent);
        processedQuestion.setPosition(question.getPosition());
        processedQuestion.setExternalCode(question.getExternalCode());
        processedQuestion.setAnswers(question.getAnswers());
        processedQuestion.setPacketId(question.getPacketId());
        processedQuestion.setRule(question.getRule());

        // Copy additional properties if any
        if (question.getAdditionalProperties() != null) {
            question.getAdditionalProperties().forEach(processedQuestion::setAdditionalProperty);
        }

        log.debug("Question {} templating: '{}' -> '{}'",
                question.getId(), originalContent, processedContent);

        return processedQuestion;
    }

    /**
     * Process questions in-place by modifying the original list.
     * This method modifies the content of existing questions rather than creating
     * new instances.
     * 
     * @param questions   The list of questions to process (will be modified)
     * @param orderEntity The order entity containing data for processing
     */
    public void processQuestionsInPlace(List<Question> questions, OrderEntity orderEntity) {
        if (questions == null || questions.isEmpty()) {
            return;
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, skipping in-place processing");
            return;
        }

        // OTTIMIZZAZIONE 1: Quick check se nessuna question ha template
        boolean hasTemplates = questions.stream()
                .anyMatch(q -> q.getContent() != null && q.getContent().contains("{{"));

        if (!hasTemplates) {
            // Solo visibility filtering, niente templating
            questions.removeIf(question -> !visibilityService.isQuestionVisible(question, orderEntity));
            return;
        }

        // OTTIMIZZAZIONE 2: Single-pass processing invece di doppio loop
        questions.removeIf(question -> {
            // Check visibility prima
            if (!visibilityService.isQuestionVisible(question, orderEntity)) {
                return true; // Rimuovi la question
            }

            // Processa template solo se visibile E ha placeholder
            String originalContent = question.getContent();
            if (originalContent != null && !originalContent.trim().isEmpty() && originalContent.contains("{{")) {
                String processedContent = templatingService.processTemplate(originalContent, orderEntity);
                question.setContent(processedContent);
            }

            return false; // Mantieni la question
        });
    }
}
