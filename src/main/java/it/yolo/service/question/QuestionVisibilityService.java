package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for evaluating question visibility rules.
 * 
 * This service determines whether questions should be visible based on JSON rules
 * stored in the question's rule field. It reuses the rule evaluation logic from
 * PacketRuleEngine to maintain consistency.
 * 
 * Rule format example:
 * {
 *   "exclusionRules": [
 *     {
 *       "field": "company.scoreESG",
 *       "value": "3",
 *       "operator": "gt"
 *     }
 *   ]
 * }
 * 
 * Supported operators: eq, ne, gt, lt, gte, lte, contains
 */
@ApplicationScoped
@Slf4j
public class QuestionVisibilityService {

    /**
     * Determine if a question should be visible based on its visibility rules.
     *
     * @param question The question to evaluate
     * @param orderEntity The order entity containing the data for rule evaluation
     * @return true if the question should be visible, false otherwise
     */
    public boolean isQuestionVisible(Question question, OrderEntity orderEntity) {
        if (question == null) {
            log.debug("Question is null, not visible");
            return false;
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, showing question by default");
            return true;
        }

        JsonNode rule = question.getRule();
        if (rule == null || rule.isNull()) {
            log.debug("Question {} has no visibility rules, showing by default", question.getId());
            return true;
        }

        log.debug("Evaluating visibility rules for question {}", question.getId());

        // Get the first order item for rule evaluation
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available for rule evaluation, showing question by default");
            return true;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);

        // Check exclusion rules first - if any exclusion rule is met, hide the question
        if (rule.has("exclusionRules")) {
            log.debug("Checking exclusion rules for question {}", question.getId());
            ArrayNode exclusionRules = (ArrayNode) rule.get("exclusionRules");
            for (JsonNode exclusionRule : exclusionRules) {
                if (evaluateCondition(exclusionRule, orderEntity)) {
                    log.debug("Question {} hidden by exclusion rule: {}", question.getId(), exclusionRule);
                    return false;
                }
            }
            log.debug("Question {} passed all exclusion rules", question.getId());
        }

        // If there are no inclusion rules, show the question (it passed exclusion rules)
        if (!rule.has("inclusionRules") || rule.get("inclusionRules").isEmpty()) {
            log.debug("Question {} has no inclusion rules, showing after passing exclusion checks", question.getId());
            return true;
        }

        // Check inclusion rules
        log.debug("Checking inclusion rules for question {}", question.getId());
        ArrayNode inclusionRules = (ArrayNode) rule.get("inclusionRules");

        // Determine logic operator (default to AND)
        String logicOperator = rule.has("logicOperator") ?
                              rule.get("logicOperator").asText().toUpperCase() : "AND";

        log.debug("Using logic operator {} for inclusion rules of question {}", logicOperator, question.getId());

        if ("OR".equals(logicOperator)) {
            // OR logic - at least one rule must be satisfied
            for (JsonNode inclusionRule : inclusionRules) {
                if (evaluateCondition(inclusionRule, orderEntity)) {
                    log.debug("Question {} shown by OR inclusion rule: {}", question.getId(), inclusionRule);
                    return true;
                }
            }
            log.debug("Question {} hidden because no OR inclusion rules were satisfied", question.getId());
            return false;
        } else {
            // AND logic - all rules must be satisfied
            for (JsonNode inclusionRule : inclusionRules) {
                if (!evaluateCondition(inclusionRule, orderEntity)) {
                    log.debug("Question {} hidden because AND inclusion rule not satisfied: {}", question.getId(), inclusionRule);
                    return false;
                }
            }
            log.debug("Question {} shown because all AND inclusion rules were satisfied", question.getId());
            return true;
        }
    }

    /**
     * Evaluate a single rule condition.
     * Extended to support new operators: count, in, contains for arrays.
     *
     * @param condition The rule condition to evaluate
     * @param orderEntity The order entity containing the data
     * @return true if the condition is satisfied, false otherwise
     */
    private boolean evaluateCondition(JsonNode condition, OrderEntity orderEntity) {
        if (!condition.has("field") || !condition.has("value")) {
            log.debug("Condition missing required fields, assuming it matches");
            return true;
        }

        String field = condition.get("field").asText();
        JsonNode valueNode = condition.get("value");
        String operator = condition.has("operator") ? condition.get("operator").asText() : "eq";
        boolean negate = condition.has("negate") && condition.get("negate").asBoolean();

        log.debug("Evaluating condition: field={}, value={}, operator={}, negate={}", field, valueNode, operator, negate);

        // Handle special operators that work with arrays or complex data
        if ("count".equals(operator)) {
            return evaluateCountCondition(field, condition, orderEntity, negate);
        } else if ("in".equals(operator)) {
            return evaluateInCondition(field, valueNode, orderEntity, negate);
        } else if ("contains".equals(operator) && field.contains("chosenWarranties")) {
            return evaluateArrayContainsCondition(field, valueNode.asText(), orderEntity, negate);
        }

        // Standard operators - get actual value as string
        String expectedValue = valueNode.asText();
        String actualValue = getFieldValue(field, orderEntity);

        // If actual value is null, condition is not satisfied
        if (actualValue == null) {
            log.debug("Field {} not found in order data", field);
            return negate; // If negated, return true when field is not found
        }

        log.debug("Comparing values: actual={}, expected={}", actualValue, expectedValue);

        boolean result;
        // Evaluate condition based on operator
        switch (operator) {
            case "eq":
                result = actualValue.equals(expectedValue);
                break;
            case "ne":
                result = !actualValue.equals(expectedValue);
                break;
            case "gt":
                try {
                    result = Double.parseDouble(actualValue) > Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lt":
                try {
                    result = Double.parseDouble(actualValue) < Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "gte":
                try {
                    result = Double.parseDouble(actualValue) >= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lte":
                try {
                    result = Double.parseDouble(actualValue) <= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "contains":
                result = actualValue.contains(expectedValue);
                break;
            default:
                log.debug("Unknown operator: {}", operator);
                result = false;
        }

        // Apply negation if specified
        if (negate) {
            result = !result;
        }

        log.debug("Condition evaluation result: {}", result);
        return result;
    }

    /**
     * Get field value from order data with support for chosenWarranties and insuredItem.
     *
     * @param field The field path (e.g., "company.scoreESG", "tipologiaUsoAbitazione", "chosenWarranties.warranties")
     * @param orderEntity The order entity containing the data
     * @return The field value as string, or null if not found
     */
    private String getFieldValue(String field, OrderEntity orderEntity) {
        log.debug("Getting field value for: {}", field);

        // Handle chosenWarranties fields
        if (field.startsWith("chosenWarranties.")) {
            return getChosenWarrantiesFieldValue(field, orderEntity);
        }

        // Handle insuredItem fields (existing logic)
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        JsonNode insuredItem = orderItem.getInsured_item();
        if (insuredItem == null) {
            log.debug("No insured item data available");
            return null;
        }

        // Convert field to JsonPointer format if needed
        String jsonPointer = field;
        if (!jsonPointer.startsWith("/")) {
            // Convert dot notation to JsonPointer format
            jsonPointer = "/" + field.replace(".", "/");
        }

        log.debug("Looking for field {} using JsonPointer {}", field, jsonPointer);

        // Use at() to get the value at the specified path
        JsonNode valueNode = insuredItem.at(jsonPointer);

        // Check if the node exists and is not missing
        if (!valueNode.isMissingNode()) {
            String value = valueNode.asText();
            log.debug("Found value {} for field {}", value, field);
            return value;
        } else {
            log.debug("Field {} not found in insured item", field);
            return null;
        }
    }

    /**
     * Get field value from chosenWarranties data.
     * Uses the same logic as QuestionTemplatingService: orderItem.getInstance().get("chosenWarranties").get("data")
     *
     * @param field The field path starting with "chosenWarranties."
     * @param orderEntity The order entity
     * @return The field value as string, or null if not found
     */
    private String getChosenWarrantiesFieldValue(String field, OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available for chosenWarranties");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        if (orderItem == null) {
            log.debug("First order item is null");
            return null;
        }

        JsonNode instanceNode = orderItem.getInstance();
        if (instanceNode == null || !instanceNode.hasNonNull("chosenWarranties")) {
            log.debug("Instance node is null or chosenWarranties not found");
            return null;
        }

        JsonNode chosenWarrantiesNode = instanceNode.get("chosenWarranties");
        if (!chosenWarrantiesNode.has("data")) {
            log.debug("Data node not found in chosenWarranties");
            return null;
        }

        JsonNode chosenWarranties = chosenWarrantiesNode.get("data");

        // Remove "chosenWarranties." prefix
        String remainingPath = field.substring("chosenWarranties.".length());

        // Convert to JsonPointer format
        String jsonPointer = "/" + remainingPath.replace(".", "/");
        JsonNode valueNode = chosenWarranties.at(jsonPointer);

        if (!valueNode.isMissingNode()) {
            String value = valueNode.asText();
            log.debug("Found chosenWarranties value {} for field {}", value, field);
            return value;
        } else {
            log.debug("Field {} not found in chosenWarranties", field);
            return null;
        }
    }

    /**
     * Get chosenWarranties data using the same logic as QuestionTemplatingService.
     * Accesses: orderItem.getInstance().get("chosenWarranties").get("data")
     *
     * @param orderEntity The order entity
     * @return The chosenWarranties data JsonNode, or null if not found
     */
    private JsonNode getChosenWarrantiesData(OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available for chosenWarranties data");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        if (orderItem == null) {
            log.debug("First order item is null");
            return null;
        }

        JsonNode instanceNode = orderItem.getInstance();
        if (instanceNode == null || !instanceNode.hasNonNull("chosenWarranties")) {
            log.debug("Instance node is null or chosenWarranties not found");
            return null;
        }

        JsonNode chosenWarrantiesNode = instanceNode.get("chosenWarranties");
        if (!chosenWarrantiesNode.has("data")) {
            log.debug("Data node not found in chosenWarranties");
            return null;
        }

        return chosenWarrantiesNode.get("data");
    }

    /**
     * Evaluate count condition for arrays with optional filters.
     * Format: "chosenWarranties.warranties[mandatory=false]" with operator "count", value "0", comparison "gt"
     *
     * @param field The field path with optional filter
     * @param condition The complete condition with comparison operator
     * @param orderEntity The order entity
     * @param negate Whether to negate the result
     * @return true if condition is satisfied
     */
    private boolean evaluateCountCondition(String field, JsonNode condition, OrderEntity orderEntity, boolean negate) {
        log.debug("Evaluating count condition for field: {}", field);

        // Get chosenWarranties using the same logic as templating service
        JsonNode chosenWarranties = getChosenWarrantiesData(orderEntity);
        if (chosenWarranties == null) {
            log.debug("chosenWarranties data is null for count condition");
            return negate; // If negated, return true when data is missing
        }

        int count = 0;

        // Check if field has filter syntax
        if (field.contains("[") && field.contains("]")) {
            count = getFilteredArrayCount(field, chosenWarranties);
        } else {
            // Simple array count
            count = getSimpleArrayCount(field, chosenWarranties);
        }

        log.debug("Array count result: {}", count);

        // Get comparison parameters
        String expectedValue = condition.get("value").asText();
        String comparison = condition.has("comparison") ? condition.get("comparison").asText() : "eq";

        boolean result = compareCount(count, expectedValue, comparison);

        if (negate) {
            result = !result;
        }

        log.debug("Count condition result: {} (count={}, expected={}, comparison={}, negate={})",
                 result, count, expectedValue, comparison, negate);
        return result;
    }

    /**
     * Get count of array elements with filter applied.
     */
    private int getFilteredArrayCount(String field, JsonNode chosenWarranties) {
        // Parse field path and filter
        int bracketStart = field.indexOf('[');
        int bracketEnd = field.indexOf(']');

        if (bracketStart == -1 || bracketEnd == -1) {
            log.debug("Invalid filter syntax in count field: {}", field);
            return 0;
        }

        String arrayPath = field.substring(0, bracketStart);
        String filterExpression = field.substring(bracketStart + 1, bracketEnd);

        // Remove "chosenWarranties." prefix if present
        if (arrayPath.startsWith("chosenWarranties.")) {
            arrayPath = arrayPath.substring("chosenWarranties.".length());
        }

        // Get the array
        String jsonPointer = "/" + arrayPath.replace(".", "/");
        JsonNode arrayNode = chosenWarranties.at(jsonPointer);

        if (arrayNode.isMissingNode() || !arrayNode.isArray()) {
            log.debug("Array not found for count: {}", arrayPath);
            return 0;
        }

        // Parse filter
        String[] filterParts = filterExpression.split("=", 2);
        if (filterParts.length != 2) {
            log.debug("Invalid filter format for count: {}", filterExpression);
            return 0;
        }

        String filterProperty = filterParts[0].trim();
        String filterValue = filterParts[1].trim();

        // Count matching elements
        int count = 0;
        for (JsonNode item : arrayNode) {
            JsonNode propertyNode = item.get(filterProperty);
            if (propertyNode != null && matchesFilterValue(propertyNode.asText(), filterValue)) {
                count++;
            }
        }

        return count;
    }

    /**
     * Get simple count of array elements without filter.
     */
    private int getSimpleArrayCount(String field, JsonNode chosenWarranties) {
        // Remove "chosenWarranties." prefix if present
        String arrayPath = field;
        if (arrayPath.startsWith("chosenWarranties.")) {
            arrayPath = arrayPath.substring("chosenWarranties.".length());
        }

        String jsonPointer = "/" + arrayPath.replace(".", "/");
        JsonNode arrayNode = chosenWarranties.at(jsonPointer);

        if (arrayNode.isMissingNode() || !arrayNode.isArray()) {
            log.debug("Array not found for simple count: {}", arrayPath);
            return 0;
        }

        return arrayNode.size();
    }

    /**
     * Compare count value with expected value using comparison operator.
     */
    private boolean compareCount(int actualCount, String expectedValue, String comparison) {
        try {
            int expectedCount = Integer.parseInt(expectedValue);

            switch (comparison) {
                case "eq":
                    return actualCount == expectedCount;
                case "ne":
                    return actualCount != expectedCount;
                case "gt":
                    return actualCount > expectedCount;
                case "lt":
                    return actualCount < expectedCount;
                case "gte":
                    return actualCount >= expectedCount;
                case "lte":
                    return actualCount <= expectedCount;
                default:
                    log.debug("Unknown comparison operator for count: {}", comparison);
                    return false;
            }
        } catch (NumberFormatException e) {
            log.debug("Invalid numeric value for count comparison: {}", expectedValue);
            return false;
        }
    }

    /**
     * Evaluate 'in' condition - check if field value is in array of values.
     *
     * @param field The field path
     * @param valueNode The array of values to check against
     * @param orderEntity The order entity
     * @param negate Whether to negate the result
     * @return true if condition is satisfied
     */
    private boolean evaluateInCondition(String field, JsonNode valueNode, OrderEntity orderEntity, boolean negate) {
        log.debug("Evaluating 'in' condition for field: {}", field);

        String actualValue = getFieldValue(field, orderEntity);
        if (actualValue == null) {
            log.debug("Field {} not found for 'in' condition", field);
            return negate;
        }

        List<String> expectedValues = new ArrayList<>();

        if (valueNode.isArray()) {
            // Value is an array
            for (JsonNode item : valueNode) {
                expectedValues.add(item.asText());
            }
        } else {
            // Value is a single string, treat as comma-separated list
            String[] values = valueNode.asText().split(",");
            for (String value : values) {
                expectedValues.add(value.trim());
            }
        }

        boolean result = expectedValues.contains(actualValue);

        if (negate) {
            result = !result;
        }

        log.debug("'In' condition result: {} (actual={}, expected={})", result, actualValue, expectedValues);
        return result;
    }

    /**
     * Evaluate 'contains' condition for arrays - check if array contains specific value.
     *
     * @param field The field path to array
     * @param expectedValue The value to look for in array
     * @param orderEntity The order entity
     * @param negate Whether to negate the result
     * @return true if condition is satisfied
     */
    private boolean evaluateArrayContainsCondition(String field, String expectedValue, OrderEntity orderEntity, boolean negate) {
        log.debug("Evaluating array 'contains' condition for field: {}", field);

        // Get chosenWarranties using the same logic as templating service
        JsonNode chosenWarranties = getChosenWarrantiesData(orderEntity);
        if (chosenWarranties == null) {
            log.debug("chosenWarranties data is null for contains condition");
            return negate;
        }

        // Remove "chosenWarranties." prefix if present
        String arrayPath = field;
        if (arrayPath.startsWith("chosenWarranties.")) {
            arrayPath = arrayPath.substring("chosenWarranties.".length());
        }

        String jsonPointer = "/" + arrayPath.replace(".", "/");
        JsonNode arrayNode = chosenWarranties.at(jsonPointer);

        if (arrayNode.isMissingNode() || !arrayNode.isArray()) {
            log.debug("Array not found for contains condition: {}", arrayPath);
            return negate;
        }

        // Check if any item in array has name matching expectedValue
        boolean found = false;
        for (JsonNode item : arrayNode) {
            JsonNode nameNode = item.get("name");
            if (nameNode != null && expectedValue.equals(nameNode.asText())) {
                found = true;
                break;
            }
        }

        boolean result = found;
        if (negate) {
            result = !result;
        }

        log.debug("Array 'contains' condition result: {} (expected={}, found={})", result, expectedValue, found);
        return result;
    }

    /**
     * Check if an item value matches the filter value (reused from templating service).
     */
    private boolean matchesFilterValue(String itemValue, String filterValue) {
        if (itemValue == null || filterValue == null) {
            return false;
        }

        // Direct string comparison
        if (itemValue.equals(filterValue)) {
            return true;
        }

        // Boolean comparison (case insensitive)
        if ("true".equalsIgnoreCase(itemValue) && "true".equalsIgnoreCase(filterValue)) {
            return true;
        }
        if ("false".equalsIgnoreCase(itemValue) && "false".equalsIgnoreCase(filterValue)) {
            return true;
        }

        // Numeric comparison
        try {
            double itemNumeric = Double.parseDouble(itemValue);
            double filterNumeric = Double.parseDouble(filterValue);
            return itemNumeric == filterNumeric;
        } catch (NumberFormatException e) {
            // Not numeric, fall back to string comparison
            return itemValue.equalsIgnoreCase(filterValue);
        }
    }
}
