package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.packet.PacketData;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.common.CeilingsUtility;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.model.CalculateCeilingsResponse;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Service for filtering packets based on filterPackets in order instance.
 *
 * This service provides functionality to filter packets in a product response
 * based on a list of packet IDs stored in the order instance.
 */
@ApplicationScoped
@Slf4j
public class PacketFilterService {

    /**
     * Filter packets in a product response based on filterPackets in order instance.
     *
     * @param entity The order entity containing the instance with filterPackets
     * @param productResponse The product response containing packets to filter
     * @return true if filtering was applied, false otherwise
     */
    public boolean filterProductPackets(OrderEntity entity, ProductResponse productResponse) {
        // Check if all required objects are available
        if (entity == null || entity.getOrderItem() == null || entity.getOrderItem().isEmpty() ||
            entity.getOrderItem().get(0).getInstance() == null ||
            !entity.getOrderItem().get(0).getInstance().has("filterPackets") ||
            productResponse == null || productResponse.getData() == null ||
            productResponse.getData().getPackets() == null) {
            return false;
        }

        log.info("FilterPackets available in instance node");
        JsonNode filterPackets = entity.getOrderItem().get(0).getInstance().get("filterPackets");

        // Extract packet IDs from filterPackets and filter the product packets
        if (filterPackets.isArray()) {
            // Create a set of packet IDs from filterPackets
            Set<Integer> filterPacketIds = StreamSupport.stream(filterPackets.spliterator(), false)
                .filter(packet -> packet.has("id"))
                .map(packet -> packet.get("id").asInt())
                .collect(Collectors.toSet());

            // Filter the packets in the product response
            ArrayNode originalPackets = productResponse.getData().getPackets();
            ArrayNode filteredPackets = JsonNodeFactory.instance.arrayNode();

            // Filter the packets in the product response
            StreamSupport.stream(originalPackets.spliterator(), false)
                .filter(packet -> packet.has("id") && filterPacketIds.contains(packet.get("id").asInt()))
                .forEach(filteredPackets::add);

            // Calculate ceilings for each filtered packet if insured_item is available
            OrderItemEntity orderItem = entity.getOrderItem().get(0);
            JsonNode insuredItem = orderItem.getInsured_item();
            
            if (insuredItem != null) {
                log.info("Calculating ceilings for filtered packets");
                calculatePacketCeilings(orderItem, insuredItem, filteredPackets);
            }

            // Replace the original packets with the filtered ones
            productResponse.getData().setPackets(filteredPackets);

            log.info("Applied packet filtering: kept {} packets out of {} based on filterPackets",
                    filteredPackets.size(), originalPackets.size());
            return true;
        }

        return false;
    }
    
    /**
     * Calculates ceilings for each packet in the filtered packets array.
     *
     * @param orderItem The order item containing insured item data
     * @param insuredItem The insured item data
     * @param filteredPackets Array of filtered packets to calculate ceilings for
     */
    private void calculatePacketCeilings(OrderItemEntity orderItem, JsonNode insuredItem, ArrayNode filteredPackets) {
        List<OrderItemEntity> orderItems = Collections.singletonList(orderItem);
        
        // Process each packet
        for (int i = 0; i < filteredPackets.size(); i++) {
            JsonNode packet = filteredPackets.get(i);
            
            // Extract PacketData from the packet
            PacketData packetData = extractPacketData(packet);
            if (packetData == null || packetData.getWarranties() == null) {
                log.warn("Unable to extract valid packet data or warranties are missing for packet ID: {}", 
                         packet.has("id") ? packet.get("id").asInt() : "unknown");
                continue;
            }

            // Calculate ceilings for this packet
            CalculateCeilingsResponse response = CeilingsUtility.calculatePacketCeilings(orderItems, insuredItem, packetData);
            
            // Update the packet warranties if ceilings were calculated
            if (response.isUpdated()) {
                log.info("Updated ceilings for packet ID: {}", packet.has("id") ? packet.get("id").asInt() : "unknown");
                ((ObjectNode) packet).set("warranties", response.getWarranties());
            }
        }
    }
    
    /**
     * Extracts PacketData from a JsonNode packet.
     *
     * @param packet The packet JsonNode
     * @return PacketData object or null if extraction fails
     */
    private PacketData extractPacketData(JsonNode packet) {
        PacketData packetData = new PacketData();
        
        try {
            // Extract configuration
            if (packet.has("configuration")) {
                packetData.setConfiguration(packet.get("configuration"));
            }
            
            // Extract product
            if (packet.has("product")) {
                packetData.setProduct(packet.get("product"));
            }
            
            // Extract warranties
            if (packet.has("warranties")) {
                packetData.setWarranties(packet.get("warranties"));
            } else {
                return null; // Warranties are required
            }
            
            return packetData;
        } catch (Exception e) {
            log.error("Error extracting packet data: {}", e.getMessage());
            return null;
        }
    }
}
