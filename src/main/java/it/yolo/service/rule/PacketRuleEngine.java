package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Rule engine for filtering packets based on business rules.
 *
 * This engine supports complex filtering logic including:
 * - Inclusion rules: Rules that must be satisfied to include a packet
 * - Exclusion rules: Rules that, if satisfied, will exclude a packet
 * - Rule groups: Groups of rules with their own logical operators
 * - Nested fields: Support for accessing nested JSON fields like "company.scoreESG"
 * - Multiple operators: eq, ne, gt, lt, gte, lte, contains
 *
 * The rule engine evaluates conditions in the following order:
 * 1. Exclusion rules (if any are satisfied, the packet is excluded)
 * 2. Inclusion rules (if present, at least one must be satisfied to include the packet)
 * 3. Rule groups (complex combinations of inclusion and exclusion rules)
 */
@ApplicationScoped
@Slf4j
public class PacketRuleEngine {

    /**
     * Filter packets based on order data and business rules
     *
     * @param packets List of packets to filter
     * @param order Order entity containing order data
     * @return Filtered list of packets
     */
    public List<Packet> filterPackets(List<Packet> packets, OrderEntity order) {
        if (packets == null || packets.isEmpty()) {
            log.info("No packets to filter");
            return new ArrayList<>();
        }

        if (order == null || order.getOrderItem() == null || order.getOrderItem().isEmpty()) {
            log.info("No order data available for filtering");
            return packets;
        }

        log.info("Filtering {} packets based on order data", packets.size());

        // Get the first order item
        OrderItemEntity orderItem = order.getOrderItem().get(0);

        // Apply filters based on order data
        List<Packet> filteredPackets = applyBusinessRules(packets, order, orderItem);

        log.info("Filtered packets: {} out of {}", filteredPackets.size(), packets.size());

        return filteredPackets;
    }

    /**
     * Apply business rules to filter packets
     *
     * @param packets List of packets to filter
     * @param order Order entity containing order data
     * @param orderItem Order item entity containing order item data
     * @return Filtered list of packets
     */
    private List<Packet> applyBusinessRules(List<Packet> packets, OrderEntity order, OrderItemEntity orderItem) {
        // Get insured item data
        JsonNode insuredItem = orderItem.getInsured_item();
        if (insuredItem == null) {
            log.info("No insured item data available for filtering");
            return packets;
        }

        // Apply filters directly without using a filter map
        // We now access the fields directly in the evaluateCondition method
        Map<String, String> emptyMap = new HashMap<>(); // Manteniamo la firma del metodo per compatibilità

        return packets.stream()
                .filter(packet -> matchesFilter(packet, emptyMap, orderItem))
                .collect(Collectors.toList());
    }

    /**
     * Extract filter map from insured item data
     *
     * @param insuredItem Insured item data
     * @return Map of filter keys and values
     */
    private Map<String, String> extractFilterMap(JsonNode insuredItem) {
        // Non utilizziamo più questo metodo per estrarre i valori specifici
        // Ora accediamo direttamente ai campi utilizzando il metodo at() in evaluateCondition
        return new HashMap<>();
    }

    /**
     * Check if packet matches filter criteria
     *
     * @param packet Packet to check
     * @param filterMap Map of filter keys and values
     * @param orderItem Order item entity containing order item data
     * @return True if packet matches filter criteria, false otherwise
     */
    private boolean matchesFilter(Packet packet, Map<String, String> filterMap, OrderItemEntity orderItem) {
        Integer packetId = packet.packetResponse().getData().getId();
        log.debug("Evaluating packet {}: {}", packetId, packet.packetResponse().getData().getName());

        // Get packet condition
        JsonNode packetCondition = packet.packetResponse().getData().getPacketCondition();
        if (packetCondition == null) {
            // If no packetCondition, assume it matches
            log.debug("Packet {} has no packetCondition, including by default", packetId);
            return true;
        }

        // Check if using the new rule groups format
        if (packetCondition.has("ruleGroups")) {
            log.debug("Packet {} using ruleGroups format", packetId);
            return evaluateRuleGroups(packetCondition.get("ruleGroups"),
                                     packetCondition.has("groupLogicOperator") ?
                                     packetCondition.get("groupLogicOperator").asText() : "AND",
                                     filterMap, orderItem, packetId);
        }

        // Check exclusion rules first - if any exclusion rule is met, exclude the packet
        if (packetCondition.has("exclusionRules")) {
            log.debug("Checking exclusion rules for packet {}", packetId);
            ArrayNode exclusionRules = (ArrayNode) packetCondition.get("exclusionRules");
            for (JsonNode rule : exclusionRules) {
                if (evaluateCondition(rule, filterMap, orderItem)) {
                    log.debug("Packet {} excluded by exclusion rule: {}", packetId, rule);
                    return false;
                }
            }
            log.debug("Packet {} passed all exclusion rules", packetId);
        }

        // Rimosso il supporto per il formato legacy filterConditions

        // If there are no inclusion rules, include the packet (it passed exclusion rules)
        if (!packetCondition.has("inclusionRules") || packetCondition.get("inclusionRules").isEmpty()) {
            log.debug("Packet {} has no inclusion rules, including after passing other checks", packetId);
            return true;
        }

        // Check inclusion rules
        log.debug("Checking inclusion rules for packet {}", packetId);
        ArrayNode inclusionRules = (ArrayNode) packetCondition.get("inclusionRules");

        // Determine logic operator (default to AND)
        String logicOperator = packetCondition.has("logicOperator") ?
                              packetCondition.get("logicOperator").asText().toUpperCase() : "AND";

        log.debug("Using logic operator {} for inclusion rules of packet {}", logicOperator, packetId);

        if ("OR".equals(logicOperator)) {
            // OR logic - at least one rule must be satisfied
            for (JsonNode rule : inclusionRules) {
                if (evaluateCondition(rule, filterMap, orderItem)) {
                    log.debug("Packet {} included by OR inclusion rule: {}", packetId, rule);
                    return true;
                }
            }
            log.debug("Packet {} excluded because no OR inclusion rules were satisfied", packetId);
            return false;
        } else {
            // AND logic - all rules must be satisfied
            for (JsonNode rule : inclusionRules) {
                if (!evaluateCondition(rule, filterMap, orderItem)) {
                    log.debug("Packet {} excluded because AND inclusion rule not satisfied: {}", packetId, rule);
                    return false;
                }
            }
            log.debug("Packet {} included because all AND inclusion rules were satisfied", packetId);
            return true;
        }
    }

    /**
     * Evaluate rule groups for a packet
     *
     * @param ruleGroups Array of rule groups to evaluate
     * @param groupLogicOperator Logic operator to apply between groups (AND/OR)
     * @param filterMap Map of filter keys and values
     * @param orderItem Order item entity containing order item data
     * @param packetId ID of the packet being evaluated (for logging)
     * @return True if the packet matches the rule groups criteria, false otherwise
     */
    private boolean evaluateRuleGroups(JsonNode ruleGroups, String groupLogicOperator,
                                      Map<String, String> filterMap, OrderItemEntity orderItem, Integer packetId) {
        boolean result = "AND".equalsIgnoreCase(groupLogicOperator);

        log.debug("Evaluating {} rule groups with {} operator for packet {}",
                 ruleGroups.size(), groupLogicOperator, packetId);

        for (JsonNode group : ruleGroups) {
            String groupType = group.has("type") ? group.get("type").asText() : "inclusion";
            log.debug("Evaluating {} rule group for packet {}", groupType, packetId);

            boolean groupResult = evaluateRuleGroup(group, filterMap, orderItem);

            // For exclusion groups, negate the result
            if ("exclusion".equalsIgnoreCase(groupType)) {
                groupResult = !groupResult;
                log.debug("Exclusion group result after negation: {}", groupResult);
            }

            if ("AND".equalsIgnoreCase(groupLogicOperator)) {
                // For AND, if any group fails, the whole evaluation fails
                if (!groupResult) {
                    log.debug("Packet {} excluded because rule group failed: {}", packetId, group);
                    return false;
                }
            } else {
                // For OR, if any group succeeds, the whole evaluation succeeds
                if (groupResult) {
                    log.debug("Packet {} included because rule group succeeded: {}", packetId, group);
                    return true;
                }
            }
        }

        log.debug("Rule groups final result for packet {}: {}", packetId, result);
        return result;
    }

    /**
     * Evaluate a single rule group
     *
     * @param group Rule group to evaluate
     * @param filterMap Map of filter keys and values
     * @param orderItem Order item entity containing order item data
     * @return True if the rule group is satisfied, false otherwise
     */
    private boolean evaluateRuleGroup(JsonNode group, Map<String, String> filterMap, OrderItemEntity orderItem) {
        String operator = group.has("operator") ? group.get("operator").asText() : "AND";
        JsonNode rules = group.get("rules");

        log.debug("Evaluating rule group with {} operator and {} rules", operator, rules.size());

        if ("OR".equalsIgnoreCase(operator)) {
            // OR logic - at least one rule must be satisfied
            for (JsonNode rule : rules) {
                if (evaluateCondition(rule, filterMap, orderItem)) {
                    log.debug("Rule group satisfied by OR rule: {}", rule);
                    return true;
                }
            }
            log.debug("Rule group not satisfied (no OR rules matched)");
            return false;
        } else {
            // AND logic - all rules must be satisfied
            for (JsonNode rule : rules) {
                if (!evaluateCondition(rule, filterMap, orderItem)) {
                    log.debug("Rule group not satisfied by AND rule: {}", rule);
                    return false;
                }
            }
            log.debug("Rule group satisfied (all AND rules matched)");
            return true;
        }
    }

    /**
     * Evaluate a filter condition
     *
     * @param condition Filter condition to evaluate
     * @param filterMap Map of filter keys and values
     * @param orderItem Order item entity containing order item data
     * @return True if condition is satisfied, false otherwise
     */
    private boolean evaluateCondition(JsonNode condition, Map<String, String> filterMap, OrderItemEntity orderItem) {
        if (!condition.has("field") || !condition.has("value")) {
            // If condition is missing required fields, assume it matches
            log.debug("Condition missing required fields, assuming it matches");
            return true;
        }

        String field = condition.get("field").asText();
        String expectedValue = condition.get("value").asText();
        String operator = condition.has("operator") ? condition.get("operator").asText() : "eq";
        boolean negate = condition.has("negate") && condition.get("negate").asBoolean();

        log.debug("Evaluating condition: field={}, value={}, operator={}, negate={}", field, expectedValue, operator, negate);

        // Get actual value directly from insured item using JsonNode.at() method
        String actualValue = null;
        JsonNode insuredItem = orderItem.getInsured_item();
        if (insuredItem != null) {
            // Convert field to JsonPointer format if needed
            String jsonPointer = field;
            if (!jsonPointer.startsWith("/")) {
                // Convert dot notation to JsonPointer format
                jsonPointer = "/" + field.replace(".", "/");
            }

            log.debug("Looking for field {} using JsonPointer {}", field, jsonPointer);

            // Use at() to get the value at the specified path
            JsonNode valueNode = insuredItem.at(jsonPointer);

            // Check if the node exists and is not missing
            if (!valueNode.isMissingNode()) {
                actualValue = valueNode.asText();
                log.debug("Found value {} for field {}", actualValue, field);
            } else {
                log.debug("Field {} not found in insured item", field);
            }
        }

        // If actual value is still null, condition is not satisfied
        if (actualValue == null) {
            log.debug("Field {} not found in filter map or insured item", field);
            return negate; // If negated, return true when field is not found
        }

        log.debug("Comparing values: actual={}, expected={}", actualValue, expectedValue);

        boolean result;
        // Evaluate condition based on operator
        switch (operator) {
            case "eq":
                result = actualValue.equals(expectedValue);
                break;
            case "ne":
                result = !actualValue.equals(expectedValue);
                break;
            case "gt":
                try {
                    result = Double.parseDouble(actualValue) > Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lt":
                try {
                    result = Double.parseDouble(actualValue) < Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "gte":
                try {
                    result = Double.parseDouble(actualValue) >= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lte":
                try {
                    result = Double.parseDouble(actualValue) <= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "contains":
                result = actualValue.contains(expectedValue);
                break;
            default:
                log.debug("Unknown operator: {}", operator);
                result = false;
        }

        // Apply negation if specified
        if (negate) {
            result = !result;
        }

        log.debug("Condition evaluation result: {}", result);
        return result;
    }

    /**
     * Create a JSON representation of filtered packets for the order item instance
     *
     * @param filteredPackets List of filtered packets
     * @return JSON node containing filtered packets
     */
    public JsonNode createFilterPacketsNode(List<Packet> filteredPackets) {
        ArrayNode packetsArray = JsonNodeFactory.instance.arrayNode();

        for (Packet packet : filteredPackets) {
            ObjectNode packetNode = JsonNodeFactory.instance.objectNode();
            // Add packet ID
            packetNode.put("id", packet.packetResponse().getData().getId());
            // Add packet name
            packetNode.put("name", packet.packetResponse().getData().getName());
            packetsArray.add(packetNode);
            // Add other packet properties as needed
        }

        return packetsArray;
    }
}
