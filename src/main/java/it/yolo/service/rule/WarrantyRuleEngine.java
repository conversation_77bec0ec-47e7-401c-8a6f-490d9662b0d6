package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderItemEntity;
import it.yolo.service.client.ServiceClientCustomer;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.*;

/**
 * Rule engine for filtering warranties based on business rules.
 *
 * This engine supports complex filtering logic including:
 * - Inclusion rules: Rules that must be satisfied to include a warranty
 * - Exclusion rules: Rules that, if satisfied, will exclude a warranty
 * - Max duration rules: Rules that determine the maximum policy duration
 * - Rule groups: Groups of rules with their own logical operators
 * - Nested fields: Support for accessing nested JSON fields like "customer.age"
 * - Multiple operators: eq, ne, gt, lt, gte, lte, contains
 * - Formula evaluation: Support for expressions like "65-age"
 *
 * The rule engine evaluates conditions in the following order:
 * 1. Exclusion rules (if any are satisfied, the warranty is excluded)
 * 2. Inclusion rules (if present, at least one must be satisfied to include the warranty)
 * 3. Max duration rules (calculate maximum policy duration based on customer profile)
 */
@ApplicationScoped
@Slf4j
public class WarrantyRuleEngine {

    @Inject
    ServiceClientCustomer serviceClientCustomer;

    /**
     * Result class to encapsulate filtered warranties and their max durations
     */
    public static class WarrantyFilterResult {
        private final List<JsonNode> filteredWarranties;
        private final Map<String, Integer> maxDurations;

        public WarrantyFilterResult(List<JsonNode> filteredWarranties, Map<String, Integer> maxDurations) {
            this.filteredWarranties = filteredWarranties;
            this.maxDurations = maxDurations;
        }

        public List<JsonNode> getFilteredWarranties() {
            return filteredWarranties;
        }

        public Map<String, Integer> getMaxDurations() {
            return maxDurations;
        }
    }    
    
    /**
     * Filter warranties and calculate max durations based on order data and business rules
     *
     * @param orderItem Order item entity containing order data
     * @param warranties List of warranties to filter
     * @param token Authentication token
     * @return WarrantyFilterResult containing filtered warranties and max durations
     */
    public WarrantyFilterResult filterWarrantiesWithDurations(OrderItemEntity orderItem, List<JsonNode> warranties, String token) {
        return filterWarrantiesWithDurations(orderItem, warranties, token, false);
    }
    
    /**
     * Legacy method for backward compatibility
     * Filters warranties and adds maxDuration to each warranty node, removes rules from response
     */
    public List<JsonNode> filterWarranties(OrderItemEntity order, List<JsonNode> warranties, String token) {
        WarrantyFilterResult result = filterWarrantiesWithDurations(order, warranties, token, true);
        return result.getFilteredWarranties();
    }

    /**
     * Check if warranty matches filter criteria
     *
     * @param warranty Warranty to check
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return True if warranty matches filter criteria, false otherwise
     */
    private boolean matchesWarrantyFilter(JsonNode warranty, OrderItemEntity orderItem, String token) {
        String warrantyId = warranty.has("id") ? warranty.get("id").asText() : "unknown";
        log.debug("Evaluating warranty {}", warrantyId);

        // Get warranty rule
        JsonNode warrantyRule = warranty.get("rule");
        if (warrantyRule == null) {
            // If no rule, assume it matches
            log.debug("Warranty {} has no rule, including by default", warrantyId);
            return true;
        }

        // Check if using the new rule groups format
        if (warrantyRule.has("warrantyRuleGroups")) {
            log.debug("Warranty {} using warrantyRuleGroups format", warrantyId);
            return evaluateWarrantyRuleGroups(warrantyRule.get("warrantyRuleGroups"),
                                             warrantyRule.has("groupLogicOperator") ?
                                             warrantyRule.get("groupLogicOperator").asText() : "AND",
                                             orderItem, token, warrantyId);
        }

        // If no recognizable rule format, include the warranty
        log.debug("Warranty {} has no recognizable rule format, including by default", warrantyId);
        return true;
    }

    /**
     * Evaluate warranty rule groups
     *
     * @param ruleGroups Array of rule groups to evaluate
     * @param groupLogicOperator Logic operator to apply between groups (AND/OR)
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @param warrantyId ID of the warranty being evaluated (for logging)
     * @return True if the warranty matches the rule groups criteria, false otherwise
     */
    private boolean evaluateWarrantyRuleGroups(JsonNode ruleGroups, String groupLogicOperator,
                                              OrderItemEntity orderItem, String token, String warrantyId) {
        boolean result = "AND".equalsIgnoreCase(groupLogicOperator);

        log.debug("Evaluating {} warranty rule groups with {} operator for warranty {}",
                 ruleGroups.size(), groupLogicOperator, warrantyId);

        for (JsonNode group : ruleGroups) {
            String groupType = group.has("type") ? group.get("type").asText() : "inclusion";
            log.debug("Evaluating {} rule group for warranty {}", groupType, warrantyId);

            // Skip maxDuration rules in this filtering phase
            if ("maxDuration".equalsIgnoreCase(groupType)) {
                log.debug("Skipping maxDuration rule group during filtering phase");
                continue;
            }

            boolean groupResult = evaluateWarrantyRuleGroup(group, orderItem, token);

            // For exclusion groups, if the group is satisfied, exclude the warranty
            if ("exclusion".equalsIgnoreCase(groupType)) {
                if (groupResult) {
                    log.debug("Warranty {} excluded by exclusion rule group: {}", warrantyId, group);
                    return false;
                }
                continue; // Exclusion rule not satisfied, continue with other groups
            }

            // For inclusion groups
            if ("AND".equalsIgnoreCase(groupLogicOperator)) {
                // For AND, if any group fails, the whole evaluation fails
                if (!groupResult) {
                    log.debug("Warranty {} excluded because rule group failed: {}", warrantyId, group);
                    return false;
                }
            } else {
                // For OR, if any group succeeds, the whole evaluation succeeds
                if (groupResult) {
                    log.debug("Warranty {} included because rule group succeeded: {}", warrantyId, group);
                    return true;
                }
            }
        }

        log.debug("Warranty rule groups final result for warranty {}: {}", warrantyId, result);
        return result;
    }

    /**
     * Evaluate a single warranty rule group
     *
     * @param group Rule group to evaluate
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return True if the rule group is satisfied, false otherwise
     */
    private boolean evaluateWarrantyRuleGroup(JsonNode group, OrderItemEntity orderItem, String token) {
        String operator = group.has("operator") ? group.get("operator").asText() : "AND";
        JsonNode rules = group.get("rules");

        if (rules == null || !rules.isArray()) {
            log.debug("Rule group has no rules array, assuming satisfied");
            return true;
        }

        log.debug("Evaluating rule group with {} operator and {} rules", operator, rules.size());

        if ("OR".equalsIgnoreCase(operator)) {
            // OR logic - at least one rule must be satisfied
            for (JsonNode rule : rules) {
                if (evaluateWarrantyCondition(rule, orderItem, token)) {
                    log.debug("Rule group satisfied by OR rule: {}", rule);
                    return true;
                }
            }
            log.debug("Rule group not satisfied (no OR rules matched)");
            return false;
        } else {
            // AND logic - all rules must be satisfied
            for (JsonNode rule : rules) {
                if (!evaluateWarrantyCondition(rule, orderItem, token)) {
                    log.debug("Rule group not satisfied by AND rule: {}", rule);
                    return false;
                }
            }
            log.debug("Rule group satisfied (all AND rules matched)");
            return true;
        }
    }   
    
    /**
     * Calculate maximum duration for a warranty based on maxDuration rules
     *
     * @param warranty Warranty to calculate duration for
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return Maximum duration in years, or null if no duration rules apply
     */
    private Integer calculateMaxDuration(JsonNode warranty, OrderItemEntity orderItem, String token) {
        String warrantyId = warranty.has("id") ? warranty.get("id").asText() : "unknown";
        
        log.debug("Calculating max duration for warranty {}", warrantyId);

        JsonNode warrantyRule = warranty.get("rule");
        if (warrantyRule == null || !warrantyRule.has("warrantyRuleGroups")) {
            log.debug("No warranty rules found for duration calculation");
            return null;
        }

        JsonNode ruleGroups = warrantyRule.get("warrantyRuleGroups");
        Integer maxDuration = null;

        for (JsonNode group : ruleGroups) {
            String groupType = group.has("type") ? group.get("type").asText() : "inclusion";
            
            if (!"maxDuration".equalsIgnoreCase(groupType)) {
                continue; // Skip non-maxDuration groups
            }

            log.debug("Evaluating maxDuration rule group for warranty {}", warrantyId);

            // Check if this maxDuration group applies to this warranty
            if (!evaluateWarrantyRuleGroup(group, orderItem, token)) {
                log.debug("MaxDuration rule group does not apply to this warranty");
                continue;
            }

            // Extract duration value or formula from the group
            JsonNode rules = group.get("rules");
            if (rules != null && rules.isArray()) {
                for (JsonNode rule : rules) {
                    Integer ruleDuration = evaluateMaxDurationRule(rule, orderItem, token);
                    if (ruleDuration != null) {
                        maxDuration = maxDuration == null ? ruleDuration : Math.min(maxDuration, ruleDuration);
                        log.debug("Calculated duration from rule: {}", ruleDuration);
                    }
                }
            }
        }

        log.debug("Final max duration for warranty {}: {}", warrantyId, maxDuration);
        return maxDuration;
    }

    /**
     * Evaluate a single max duration rule with support for conditional logic
     *
     * @param rule Max duration rule to evaluate
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return Calculated duration in years, or null if rule cannot be evaluated
     */
    private Integer evaluateMaxDurationRule(JsonNode rule, OrderItemEntity orderItem, String token) {
        log.debug("Evaluating max duration rule: {}", rule);

        // Support for conditional duration rules
        if (rule.has("conditions")) {
            return evaluateConditionalDurationRule(rule, orderItem, token);
        }

        // Support for simple duration formula
        if (rule.has("durationFormula")) {
            String formula = rule.get("durationFormula").asText();
            return evaluateDurationFormula(formula, orderItem, token);
        }

        // Support for fixed duration with optional conditions
        if (rule.has("maxDuration")) {
            Integer fixedDuration = rule.get("maxDuration").asInt();
            
            // Check if there are conditions that must be met for this duration
            if (rule.has("condition")) {
                JsonNode condition = rule.get("condition");
                if (!evaluateWarrantyCondition(condition, orderItem, token)) {
                    log.debug("Condition not met for fixed duration rule");
                    return null;
                }
            }
            
            return fixedDuration;
        }

        log.debug("No recognizable duration rule format");
        return null;
    }

    /**
     * Evaluate conditional duration rules like:
     * {
     *   "conditions": [
     *     {
     *       "if": {"field": "customer.age", "operator": "lte", "value": "55"},
     *       "then": {"maxDuration": 10}
     *     },
     *     {
     *       "if": {"field": "customer.age", "operator": "gt", "value": "55"},
     *       "then": {"durationFormula": "65-customer.age"}
     *     }
     *   ]
     * }
     */
    private Integer evaluateConditionalDurationRule(JsonNode rule, OrderItemEntity orderItem, String token) {
        log.debug("Evaluating conditional duration rule");

        JsonNode conditions = rule.get("conditions");
        if (!conditions.isArray()) {
            log.debug("Conditions must be an array");
            return null;
        }

        for (JsonNode condition : conditions) {
            if (!condition.has("if") || !condition.has("then")) {
                log.debug("Conditional rule must have 'if' and 'then' parts");
                continue;
            }

            JsonNode ifCondition = condition.get("if");
            JsonNode thenAction = condition.get("then");            // Evaluate the if condition
            if (evaluateWarrantyCondition(ifCondition, orderItem, token)) {
                log.debug("Condition matched, applying then action");
                
                // Apply the then action
                if (thenAction.has("maxDuration")) {
                    return thenAction.get("maxDuration").asInt();
                } else if (thenAction.has("durationFormula")) {
                    String formula = thenAction.get("durationFormula").asText();
                    return evaluateDurationFormula(formula, orderItem, token);                
                } else if (thenAction.has("conditions")) {
                    // Handle nested conditional rules
                    log.debug("Processing nested conditions in then action");
                    ObjectNode nestedRule = JsonNodeFactory.instance.objectNode();
                    nestedRule.set("conditions", thenAction.get("conditions"));
                    return evaluateConditionalDurationRule(nestedRule, orderItem, token);
                }
            }
        }

        log.debug("No conditions matched for conditional duration rule");
        return null;
    }

    /**
     * Evaluate a duration formula like "65-age"
     *
     * @param formula Duration formula to evaluate
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return Calculated duration in years, or null if formula cannot be evaluated
     */
    private Integer evaluateDurationFormula(String formula, OrderItemEntity orderItem, String token) {
        log.debug("Evaluating duration formula: {}", formula);

        try {
            // Handle formula like "65-age" or "65-customer.age"
            if (formula.contains("-")) {
                String[] parts = formula.split("-");
                if (parts.length == 2) {
                    int baseValue = Integer.parseInt(parts[0].trim());
                    String field = parts[1].trim();
                    
                    String ageValue = extractValue(field, orderItem, token, true);
                    if (ageValue != null) {
                        int age = Integer.parseInt(ageValue);
                        int result = baseValue - age;
                        log.debug("Formula result: {} - {} = {}", baseValue, age, result);
                        return Math.max(0, result); // Ensure non-negative result
                    }
                }
            }
            
            // Handle direct numeric value
            try {
                return Integer.parseInt(formula.trim());
            } catch (NumberFormatException ignored) {
                // Continue to other parsing attempts
            }
            
        } catch (Exception e) {
            log.warn("Error evaluating duration formula '{}': {}", formula, e.getMessage());
        }

        log.debug("Could not evaluate duration formula: {}", formula);
        return null;
    }

    /**
     * Evaluate a warranty filter condition
     *
     * @param condition Filter condition to evaluate
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @return True if condition is satisfied, false otherwise
     */
    private boolean evaluateWarrantyCondition(JsonNode condition, OrderItemEntity orderItem, String token) {
        if (!condition.has("field") || !condition.has("value")) {
            // If condition is missing required fields, assume it matches
            log.debug("Condition missing required fields, assuming it matches");
            return true;
        }

        String field = condition.get("field").asText();
        String expectedValue = condition.get("value").asText();
        String operator = condition.has("operator") ? condition.get("operator").asText() : "eq";
        boolean negate = condition.has("negate") && condition.get("negate").asBoolean();

        log.debug("Evaluating warranty condition: field={}, value={}, operator={}, negate={}", 
                 field, expectedValue, operator, negate);

        String actualValue = extractValue(field, orderItem, token, true);

        // If actual value is null, condition is not satisfied
        if (actualValue == null) {
            log.debug("Field {} not found in order item", field);
            return negate; // If negated, return true when field is not found
        }

        log.debug("Comparing values: actual={}, expected={}", actualValue, expectedValue);

        boolean result;
        // Evaluate condition based on operator
        switch (operator) {
            case "eq":
                result = actualValue.equals(expectedValue);
                break;
            case "ne":
                result = !actualValue.equals(expectedValue);
                break;
            case "gt":
                try {
                    result = Double.parseDouble(actualValue) > Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lt":
                try {
                    result = Double.parseDouble(actualValue) < Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "gte":
                try {
                    result = Double.parseDouble(actualValue) >= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lte":
                try {
                    result = Double.parseDouble(actualValue) <= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "contains":
                result = actualValue.contains(expectedValue);
                break;
            default:
                log.debug("Unknown operator: {}", operator);
                result = false;
        }

        // Apply negation if specified
        if (negate) {
            result = !result;
        }

        log.debug("Warranty condition evaluation result: {}", result);
        return result;
    }

    // Legacy methods preserved for backward compatibility

    /**
     * Extract value from order item data using field path
     *
     * @param field Field path to extract (e.g., "customer.age", "answers[questionId=3].answerId")
     * @param orderItem Order item entity containing order item data
     * @param token Authentication token
     * @param requiresAge Whether age calculation is required
     * @return Extracted value as string, or null if not found
     */

    private String extractValue(String field, OrderItemEntity orderItem, String token, boolean requiresAge) {
        try {
            if (orderItem == null || orderItem.getInsured_item() == null) {
                return null;
            }
            JsonNode insuredItem = orderItem.getInsured_item();

            // Gestione campo customer.*
            if (field.startsWith("customer.")) {
                String subField = field.substring("customer.".length());
                JsonNode customerNode = insuredItem.path("customer");
                if (!customerNode.isMissingNode() && customerNode.has("id")) {
                    // Se abbiamo già l'età, usala subito
                    if ("age".equals(subField) && customerNode.has("age")) {
                        return customerNode.get("age").asText();
                    }
                    int customerId = customerNode.path("id").asInt();
                    JsonNode customer = serviceClientCustomer.findById(customerId, token);
                    if (customer != null && customer.has("data")) {
                        JsonNode customerData = customer.get("data");
                        if ("age".equals(subField) && customerData.has("date_of_birth")) {
                            String dob = customerData.get("date_of_birth").asText();
                            if (dob != null && !dob.isEmpty()) {
                                java.time.LocalDate birthDate = java.time.LocalDate.parse(dob);
                                java.time.LocalDate now = java.time.LocalDate.now();
                                int age = java.time.Period.between(birthDate, now).getYears();
                                // Aggiorna il nodo customer con l'età calcolata
                                if (customerNode instanceof com.fasterxml.jackson.databind.node.ObjectNode) {
                                    ((com.fasterxml.jackson.databind.node.ObjectNode) customerNode).put("age", age);
                                }
                                return String.valueOf(age);
                            }
                        } else if (customerData.has(subField)) {
                            return customerData.get(subField).asText();
                        }
                    }
                }
                return null;
            }

            // Gestione campo answers[questionId=3].answerId
            if (field.startsWith("answers[") && field.contains("].")) {
                int idxBracket = field.indexOf(']');
                String filterPart = field.substring("answers[".length(), idxBracket); // es: questionId=3
                String[] filterTokens = filterPart.split("=");
                String filterKey = filterTokens[0];
                String filterValue = filterTokens[1];
                String subField = field.substring(idxBracket + 2); // es: answerId

                JsonNode answersNode = insuredItem.get("answers");
                if (answersNode != null && answersNode.isArray()) {
                    for (JsonNode answer : answersNode) {
                        if (answer.has(filterKey) && answer.get(filterKey).asText().equals(filterValue)) {
                            if (answer.has(subField)) {
                                return answer.get(subField).asText();
                            }
                        }
                    }
                }
                return null;
            }

            // Gestione campo semplice o annidato (es: customerId, answers, altro)
            String jsonPointer = "/" + field.replace(".", "/");
            JsonNode valueNode = insuredItem.at(jsonPointer);
            if (!valueNode.isMissingNode() && !valueNode.isNull()) {
                return valueNode.asText();
            }

        } catch (Exception e) {
            log.warn("Error extracting value for field {}: {}", field, e.getMessage());
        }
        return null;
    }

    /**
     * Create a JSON representation of filtered warranties with their max durations
     *
     * @param result WarrantyFilterResult containing filtered warranties and max durations
     * @return JSON node containing filtered warranties with duration information
     */
    public JsonNode createFilterWarrantiesNode(WarrantyFilterResult result) {
        ObjectNode resultNode = JsonNodeFactory.instance.objectNode();
        ArrayNode warrantiesArray = JsonNodeFactory.instance.arrayNode();
        ObjectNode durationsNode = JsonNodeFactory.instance.objectNode();

        for (JsonNode warranty : result.getFilteredWarranties()) {
            ObjectNode warrantyNode = JsonNodeFactory.instance.objectNode();
            
            // Add warranty basic info
            if (warranty.has("id")) {
                warrantyNode.put("id", warranty.get("id").asText());
            }
            if (warranty.has("name")) {
                warrantyNode.put("name", warranty.get("name").asText());
            }
            if (warranty.has("type")) {
                warrantyNode.put("type", warranty.get("type").asText());
            }
            
            // Add max duration if available
            String warrantyId = warranty.has("id") ? warranty.get("id").asText() : "unknown";
            Integer maxDuration = result.getMaxDurations().get(warrantyId);
            if (maxDuration != null) {
                warrantyNode.put("maxDuration", maxDuration);
                durationsNode.put(warrantyId, maxDuration);
            }
            
            warrantiesArray.add(warrantyNode);
        }

        resultNode.set("warranties", warrantiesArray);
        resultNode.set("maxDurations", durationsNode);
        
        return resultNode;
    }

    /**
     * Filter warranties and calculate max durations with option to remove rules from response
     *
     * @param orderItem Order item entity containing order data
     * @param warranties List of warranties to filter
     * @param token Authentication token
     * @param removeRulesFromResponse Whether to remove rule fields from the response
     * @return WarrantyFilterResult containing filtered warranties and max durations
     */
    public WarrantyFilterResult filterWarrantiesWithDurations(OrderItemEntity orderItem, List<JsonNode> warranties, String token, boolean removeRulesFromResponse) {
        if (warranties == null || warranties.isEmpty()) {
            log.info("No warranties to filter");
            return new WarrantyFilterResult(new ArrayList<>(), new HashMap<>());
        }

        log.info("Filtering {} warranties based on order data", warranties.size());

        List<JsonNode> filteredWarranties = new ArrayList<>();
        Map<String, Integer> maxDurations = new HashMap<>();

        for (JsonNode warranty : warranties) {
            String warrantyId = warranty.has("id") ? warranty.get("id").asText() : "unknown";
            String warrantyType = warranty.has("type") ? warranty.get("type").asText() : "unknown";
            
            log.debug("Evaluating warranty {}: type={}", warrantyId, warrantyType);

            if (matchesWarrantyFilter(warranty, orderItem, token)) {
                // Create warranty copy for response
                JsonNode warrantyForResponse = removeRulesFromResponse ? warranty.deepCopy() : warranty;
                
                // Calculate max duration for this warranty
                Integer maxDuration = calculateMaxDuration(warranty, orderItem, token);
                if (maxDuration != null) {
                    maxDurations.put(warrantyId, maxDuration);
                    
                    // Add maxDuration to the warranty if removing rules
                    if (removeRulesFromResponse && warrantyForResponse instanceof ObjectNode) {
                        ((ObjectNode) warrantyForResponse).put("maxDuration", maxDuration);
                    }
                    
                    log.debug("Warranty {} max duration: {}", warrantyId, maxDuration);
                }
                
                // Remove rule field from response if requested
                if (removeRulesFromResponse && warrantyForResponse instanceof ObjectNode && warrantyForResponse.has("rule")) {
                    ((ObjectNode) warrantyForResponse).remove("rule");
                    log.debug("Removed rule field from warranty {}", warrantyId);
                }
                
                filteredWarranties.add(warrantyForResponse);
            }
        }

        log.info("Filtered warranties: {} out of {}", filteredWarranties.size(), warranties.size());
        return new WarrantyFilterResult(filteredWarranties, maxDurations);
    }
}
