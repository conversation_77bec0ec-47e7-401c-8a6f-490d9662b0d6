package it.yolo.service.v2;

import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.common.CommonUtils;
import it.yolo.common.Utility;
import it.yolo.constants.OrderStateEnum;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.*;
import it.yolo.mapper.custom.MapperOrder;
import it.yolo.records.Catalog;
import it.yolo.records.Packet;
import it.yolo.repository.AnagStatesRepository;
import it.yolo.repository.OrderItemRepository;
import it.yolo.repository.OrderRepository;
import it.yolo.service.OrderHistoryService;
import it.yolo.service.client.ServiceClientCatalog;
import it.yolo.service.client.ServiceClientPackets;
import it.yolo.service.client.ServiceClientProduct;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;



/*
 * ORDER-SERVICE V2 --> GUARDARE SEMPRE L'IMPORT
 */

@RequestScoped
public class OrderService {

    @Inject
    OrderRepository repo;

    @Inject
    AnagStatesRepository stateRepo;

    @Inject
    OrderItemRepository itemRepo;

    @Inject
    OrderHistoryService orderHistoryService;

    @Inject
    ServiceClientCatalog serviceClientCatalog;

    @Inject
    ServiceClientProduct serviceClientProduct;

    @Inject
    ServiceClientPackets serviceClientPacket;

    @Inject
    CommonUtils commonUtils;
    @Inject
    JsonWebToken jsonWebToken;

    @ConfigProperty(name = "intermediary.users")
    String intermediaryUser;

    private static String STATE_ORDER_CREATE = "Draft";
    private static String INSURANCE = "insurance_info";
    //variabile uguale a 0 in quanto a oggi la corrispondenza tra order e product è univoca
    private static int ORDER_ITEM = 0;

    @Transactional
    @WithSpan("OrderService.createOrder")
    public OrderEntity createOrder(String token,
                                   @SpanAttribute("arg.entity") OrderEntity entity) {

        Long packetId=Long.valueOf(entity.getPacketId());
        Packet packetResponse= invokePacketByPacketId(Long.valueOf(packetId), token);
        entity.setProductId(packetResponse.packetResponse().getData().getProduct().getId());
        entity.setProductType(packetResponse.packetResponse().getData().getProduct().getProductType());
        entity.setIntermediaryOrder(checkTokenIntermediary());
        List<OrderItemEntity> list= MapperOrder.OrderToOrderItemListV2(packetResponse, INSURANCE, entity);
        entity.setOrderItem(list);
        String orderCode = Utility.generateOrderCode();
        entity.setOrderCode(orderCode);
        AnagStatesEntity stateOrderCreate = stateRepo.find("state", STATE_ORDER_CREATE).firstResult();
        entity.setId(null);
        entity.setAnagStates(stateOrderCreate);
        repo.persist(entity);
        orderHistoryService.createHistoryForOrderInsert(entity);
        entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        return entity;
    }



    @WithSpan("OrderService.readOrder")
    public OrderEntity readOrder(
            @SpanAttribute("arg.id") Long id) {
        OrderEntity entity=repo.findById(id);
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by id=%d not found", id));
        }
        return entity;
    }

    @WithSpan("OrderService.readOrderByOrderCode")
    public OrderEntity readOrderByOrderCode(
            @SpanAttribute("arg.id") String orderCode) {
        OrderEntity entity=repo.find("order_code", orderCode).firstResult();
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by code not found: "+orderCode));
        }
        return entity;
    }

    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity updateOrder(
            @SpanAttribute("arg.id") String order_code,
            @SpanAttribute("arg.entity") OrderEntity entity,
            String token) throws ProductClientException, CatalogClientException, ValidationStructureProductException, PacketException {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }

        if(entity.getPacketId()!=null){
            resultDb.setPacketId(entity.getPacketId());
        }
        Packet packetResponse= invokePacketByPacketId(Long.valueOf(resultDb.getPacketId()), token);
        entity.setProductId(packetResponse.packetResponse().getData().getProduct().getId());
        entity.setPacketId(packetResponse.packetResponse().getData().getId());
        if(!resultDb.getIntermediaryOrder()){
            resultDb.setIntermediaryOrder(checkTokenIntermediary());
        }
        if(entity.getFieldToRecover()!=null){
            resultDb.setFieldToRecover(entity.getFieldToRecover());
        }
        if(entity.getPaymentToken()!=null && !entity.getPaymentToken().isEmpty()){
            resultDb.setPaymentToken(entity.getPaymentToken());
        }
        if(entity.getPaymentTransactionId()!=null) {
            resultDb.setPaymentTransactionId(entity.getPaymentTransactionId());
        }
        if(entity.getPaymentType() !=null && !entity.getPaymentType().isEmpty()){
            resultDb.setPaymentType(entity.getPaymentType());
        }
        if(entity.getCustomerId() != null){
            resultDb.setCustomerId(entity.getCustomerId());
        }

        AnagStatesEntity stateUpdating = stateRepo.find("state", entity.getAnagStates().getState()).firstResult();

        resultDb.setAnagStates(stateUpdating);
        OrderEntity orderUpdated = repo.getEntityManager().merge(resultDb);
        List<OrderItemEntity> resultOrderItem=updateOrderItem(orderUpdated, entity,packetResponse,"");
        if(resultOrderItem!=null){
            orderUpdated.setOrderItem(resultOrderItem);
        }
        orderHistoryService.createHistoryForOrderPut(orderUpdated, null);
        orderUpdated.setStepState(commonUtils.getStepState(entity.getProductId()));
        return orderUpdated;
    }

    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity confirmOrder(
            @SpanAttribute("arg.id") String order_code) throws ProductClientException, ValidationStructureProductException, PacketException {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        OrderItemEntity orderItem = itemRepo.find("order_id", resultDb.getId()).firstResult();

        AnagStatesEntity stateUpdating = stateRepo.findByIdOptional(OrderStateEnum.CONFIRMED.getValue().longValue()).orElseThrow(() -> new EntityNotFoundException(String.format(
                "Entity order by id=%d not found", order_code)));

        resultDb.setAnagStates(stateUpdating);
        OrderEntity order = repo.getEntityManager().merge(resultDb);
        //updateOrderItem(order, null ,null, null);
        orderHistoryService.createHistoryForOrderPut(order, null);
        return order;
    }

    @WithSpan("OrderService.listOrders")
    public List<OrderEntity> listOrders() {
        return repo.listAll();
    }

    @Transactional
    private List<OrderItemEntity> updateOrderItem(OrderEntity updatedEntity,OrderEntity reqEntity,Packet packetResponse,String discount) {
        List<OrderItemEntity> orderItemEntities = itemRepo.find("order_id", updatedEntity.getId()).list();
        JsonNode insuredItem = checkInsuredItem(orderItemEntities, reqEntity, packetResponse);
        orderItemEntities = checkStartDate(orderItemEntities, packetResponse, reqEntity);

        //GESTIONE PER LE VARIANTI CHE INIZIALMENTE VENGONO CREATE CON IL PRODUCT/PACKET BASE
        // AGGIORNAMENTO DEL PRODUCT ID
        //AGGIORNAMENTO DEL PRICE SE DURANTE IL FUNNEL VIENE SCELTO UN PRODUCT/PACKET DIVERSO
        orderItemEntities.forEach(orderItemEntity->{
            reqEntity.getOrderItem().forEach(req ->{
                if(orderItemEntity.getPacketId()!=updatedEntity.getPacketId()){
                    orderItemEntity.setPacketId(updatedEntity.getPacketId());
                    orderItemEntity.setPrice(String.valueOf(packetResponse.packetResponse().getData().getPacketPremium()));
                }
            });
        });



        //SE IL PREZZO RISULTA NULL ALLORA SETTO IL PACKET PREMIUM
        orderItemEntities.forEach(orderItemEntity -> {
            if(orderItemEntity.getPrice().isEmpty()){
                orderItemEntity.setPrice(String.valueOf(packetResponse.packetResponse().getData().getPacketPremium()));
            }
        });



        //SE IN RICHIESTA MI ARRIVA I PREZZO
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(r ->r.getPrice()!=null).count()>0){
            List<OrderItemEntity> itemByPrice=reqEntity.getOrderItem().stream().filter(req ->req.getPrice()!=null).collect(Collectors.toList());
            orderItemEntities.forEach(price -> {
                itemByPrice.forEach(iByPrice -> {
                    price.setPrice(iByPrice.getPrice());
                });
            });
        }

        //paracadute per momentanea scontistica
        orderItemEntities.forEach(orderItemEntity ->{
            if(discount !=null && discount.equalsIgnoreCase("sconto1@@")){
                Integer discountPercentage=100;
                Float price=Float.parseFloat(orderItemEntity.getPrice());
                orderItemEntity.setPrice(String.valueOf(price-((price/100)*discountPercentage)));
            }
        });


        //salvataggio informazioni relavite alla quotation
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).count()>0){
            List<OrderItemEntity> itemByQuotation=reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).collect(Collectors.toList());
            for (OrderItemEntity orderItemByQuotation: itemByQuotation) {
                for (OrderItemEntity orderItemEntity:orderItemEntities) {
                    orderItemEntity.setQuotation(orderItemByQuotation.getQuotation());
                }
            };
        }

        orderItemEntities.forEach(itemIsured ->{
            itemIsured.setInsured_item(insuredItem);
            itemRepo.getEntityManager().merge(itemIsured);
        });
        return orderItemEntities;
    }

    private Catalog invokeCatalogByProductCode(String product_code, JsonNode asset) throws CatalogClientException, ValidationStructureProductException {
        Catalog catalogResponse = serviceClientCatalog.findByProductCode(product_code);
        if(catalogResponse.enabled()){
            Utility.validationProductStructure(catalogResponse.goodsStructure(), asset);
        }

        return catalogResponse;
    }


    /*
     * CHIAMATA A V2 PRODUCT
     */
    private Packet invokePacketByPacketId(Long id, String token) throws PacketException {
        Packet packetResponse = serviceClientPacket.findById(id, token);
        return packetResponse;
    }

    private JsonNode checkInsuredItem(List<OrderItemEntity> orderItemEntities, OrderEntity entity, Packet packetResponse){
        JsonNode insuredItem = null;
        for (OrderItemEntity orderItem : orderItemEntities) {
            if (orderItem.getInsured_item() != null) {
                insuredItem = orderItem.getInsured_item();
            } else if (entity.getOrderItem() != null && !entity.getOrderItem().isEmpty()) {
                insuredItem = entity.getOrderItem().get(ORDER_ITEM).getInsured_item();
                Catalog catalogResponse = invokeCatalogByProductCode(packetResponse.packetResponse().getData().getProduct().getCode(), insuredItem);
            }
        }
        return insuredItem;
    }

    private List<OrderItemEntity> checkStartDate(List<OrderItemEntity> orderItems, Packet packetResponse, OrderEntity entity){
        for(OrderItemEntity orderItem : orderItems) {
            if(!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getInstant()!=null && entity.getOrderItem().get(0).getInstant()){
                orderItem.setStart_date(LocalDateTime.now());
                orderItem.setExpiration_date(orderItem.getStart_date().truncatedTo(ChronoUnit.DAYS)
                        .plusDays(entity.getOrderItem().get(0).getDays()).minus(1, ChronoUnit.MILLIS));
            }
            else if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(ORDER_ITEM).getStart_date() != null) {
                orderItem.setStart_date(entity.getOrderItem().get(ORDER_ITEM).getStart_date());
                if (packetResponse.packetResponse().getData().getFixedEndDate() != null) {
                    orderItem.setExpiration_date(packetResponse.packetResponse().getData().getFixedEndDate());
                } else {
                    if(entity.getOrderItem().get(0).getDays()!=null){
                        orderItem.setExpiration_date(orderItem.getStart_date().truncatedTo(ChronoUnit.DAYS)
                                .plusDays(entity.getOrderItem().get(0).getDays()).minus(1, ChronoUnit.MILLIS));
                    } else if(entity.getOrderItem().get(0).getExpiration_date()!=null && (packetResponse.packetResponse().getData().getSku().startsWith("tim-for-ski") || packetResponse.packetResponse().getData().getSku().startsWith("winter_sport"))){
                        orderItem.setExpiration_date(entity.getOrderItem().get(0).getExpiration_date());
                    }
                    else if (StringUtils.isNotBlank(packetResponse.packetResponse().getData().getDuration())
                            && StringUtils.isNotBlank(packetResponse.packetResponse().getData().getDurationType())) {
                        orderItem.setExpiration_date(
                                Utility.getOrderDuration(
                                        entity.getOrderItem().get(ORDER_ITEM).getStart_date(),
                                        Integer.parseInt(packetResponse.packetResponse().getData().getDuration()),
                                        packetResponse.packetResponse().getData().getDurationType()));
                    }
                }
            }
        }
        return orderItems;
    }

    public Boolean checkTokenIntermediary(){
        //check token appartenente a technical-users o intermediary-users
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        if(groupsJson!=null) {
            for (JsonString group : groupsJson) {
                groups.add(group.getString());
            }
            return groups.contains(intermediaryUser);
        }
        return false;
    }
}
