syntax = "proto3";

option java_multiple_files = true;
option java_package = "it.yolo.grpc";
option java_outer_classname = "CustomerProto";
import "google/protobuf/wrappers.proto";
package it.yolo.grpc;

// The customer service definition.
service ServiceCustomer {
  // getIdByNdg
  rpc getId (CustomerRequest) returns (CustomerResponse) {}
}

message CustomerRequest{
  google.protobuf.StringValue ndg=1;
}

message CustomerResponse{
  google.protobuf.Int64Value id=1;
}
