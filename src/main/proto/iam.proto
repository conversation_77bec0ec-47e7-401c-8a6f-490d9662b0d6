syntax = "proto3";


option java_multiple_files = true;
option java_package = "be.digitech.iadtoken.grpc";
option java_outer_classname = "IamProto";

package be.digitech.iadtoken.grpc;


service IamGrpc {
  rpc ssoRegistration(Credential) returns (TokenResponse);
  rpc refresh(Token) returns (TokenResponse);
  rpc getUserInfo(Token) returns (UserInfoResponse);
  rpc updateUser(UpdateUserRequest) returns (Response);
  rpc retiredRegistration(Credential) returns (TokenResponse);
  rpc registration(Credential) returns (RegistrationResponse);
  rpc getAllUsers(Apikey) returns (Users);
  rpc verifyCaptcha(TokenCaptcha) returns (CaptchaResponseGrpc);
  rpc login(LoginRequest) returns (TokenResponse);
  rpc technicalToken(Empty) returns (Token);
  rpc intermediaryTechnicalToken(Empty) returns (Token);
}

message LoginRequest{
  string username = 1;
  string password = 2;
}

message Apikey{
  string apikey = 1;
}

message Users{
  bytes usersBytes = 1;
  Response response = 2;
}

message RegistrationResponse{
  int32 statusNumber = 1;
}

message UpdateUserRequest{
  Token token = 1;
  Credential credential = 2;
}

message UserInfoResponse{
  Response response = 1;
  Credential credential = 2;
}

message Credential{
  string name = 1;
  string surname = 2;
  string email = 3;
  string phoneNumber = 4;
  string ndgCode = 5;
  bool userSso = 6;
  string username = 7;
  string password = 8;
  string birthDate = 9;
  bool privacy = 10;
  bool commerce = 11;
  bool trace = 12;
  string externalInfo = 13;
  string provincia_nascita=14;
  string provincia=15;
  string countryIso=16;
  string nazione_nascita=17;
  string nazione=18;
  string codice_fiscale=19;
  string citta_nascita=20;
  string citta=21;
  string cap=22;
  string indirizzo=23;
}

message TokenResponse{
  Response response = 1;
  string token = 2;
}

message Response{
  int32 statusNumber = 1;
  string errorMessage = 2;
}

message Token{
  string token = 1;
}

message TokenCaptcha{
  string tokenCaptcha = 1;
}

message CaptchaResponseGrpc{
  bool isSuccess = 1;
}

message Empty {

}
