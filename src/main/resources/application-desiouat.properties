quarkus.datasource.username=desio_uat
quarkus.datasource.password=5n93BwV3yTd5Wv6Zv
quarkus.datasource.jdbc.url=****************************************************************************************
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_6vciiWUmN
#POLICY REST CLIENT
#quarkus.rest-client.policy.url=http://localhost:8081/
quarkus.rest-client.policy.url=https://desio-api.preprod.yoloassicurazioni.it/iad-policy
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=http://localhost:8088
quarkus.rest-client.product.url=https://desio-api.preprod.yoloassicurazioni.it/iad-product
#WARRANTIES REST CLIENT
quarkus.rest-client.warranty-rules.url=http://desio.backoffice.preprod.yoloassicurazioni.it/backoffice/iad
#quarkus.rest-client.warranty-rules.url=http://localhost:8085/iad
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=https://desio-api.preprod.yoloassicurazioni.it/iad-customer
#quarkus.rest-client.iad-customer.url=https://desio-api.preprod.yoloassicurazioni.it/iad-customer
#POLICY REST CLIENT
quarkus.rest-client.iad-policy.url=http://localhost:8087
#quarkus.rest-client.iad-policy.url=https://desio-api.preprod.yoloassicurazioni.it/iad-policy/v1/policies
#POLICY REST CLIENT
quarkus.rest-client.iad-survey.url=http://localhost:8080
#CATALOG REST CLIENT
quarkus.rest-client.catalog.url=http://localhost:8083
#quarkus.rest-client.catalog.url=https://desio.preprod.yoloassicurazioni.it/iad-catalog
#COMUNICATION MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://desio-api.preprod.yoloassicurazioni.it/latest/comunication
order.prefix-code=DY
