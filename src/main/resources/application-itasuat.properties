quarkus.datasource.username=megauser
quarkus.datasource.password=dBWaz7P6pjenJUhE2bwDHpDU8xfK7Ga
quarkus.datasource.jdbc.url=****************************************************************************************
#POLICY REST CLIENT
#quarkus.rest-client.policy.url=http://localhost:8081/
quarkus.rest-client.policy.url=https://itas-api.preprod.yoloassicurazioni.it/latest/policy
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=http://localhost:8088
quarkus.rest-client.product.url=https://itas-api.preprod.yoloassicurazioni.it/latest/product
#WARRANTIES REST CLIENT
quarkus.rest-client.warranty-rules.url=http://itas.backoffice.preprod.yoloassicurazioni.it/backoffice/iad
#quarkus.rest-client.warranty-rules.url=http://localhost:8085/iad
#CUSTOMER REST CLIENT
#quarkus.rest-client.iad-customer.url=http://localhost:8080
quarkus.rest-client.iad-customer.url=https://itas-api.preprod.yoloassicurazioni.it/latest/customer
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=localhost:8083
quarkus.rest-client.iad-policy.url=https://itas-api.preprod.yoloassicurazioni.it/latest/policy
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=http://localhost:8084
quarkus.rest-client.catalog.url=https://itas.preprod.yoloassicurazioni.it/iad-catalog
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://itas-api.preprod.yoloassicurazioni.it/latest/comunication
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_wqeVoFbbv
order.prefix-code=Y-