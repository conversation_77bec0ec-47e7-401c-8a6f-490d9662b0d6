quarkus.datasource.username=megauser
quarkus.datasource.password=W5ZDxien5zuNskAQef8Mnz96gsdMHHo
quarkus.datasource.jdbc.url=***********************************************************************************************
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_YiFnMdwnM
#POLICY REST CLIENT
#quarkus.rest-client.policy.url=http://localhost:8081/
quarkus.rest-client.policy.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-policy
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=http://localhost:8088
quarkus.rest-client.product.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-product
#WARRANTIES REST CLIENT
quarkus.rest-client.warranty-rules.url=http://timcustomer.backoffice.dev.yoloassicurazioni.it/backoffice/iad
#quarkus.rest-client.warranty-rules.url=http://localhost:8085/iad
#CUSTOMER REST CLIENT
#quarkus.rest-client.iad-customer.url=http://localhost:8080
quarkus.rest-client.iad-customer.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-customer
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=localhost:8083
quarkus.rest-client.iad-policy.url=https://timcustomer-api.dev.yoloassicurazioni.it/iad-policy/v1/policies
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=http://localhost:8084
quarkus.rest-client.catalog.url=https://timcustomer.dev.yoloassicurazioni.it/iad-catalog
order.prefix-code=Y-
