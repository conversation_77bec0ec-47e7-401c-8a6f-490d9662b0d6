quarkus.datasource.username=megauser
quarkus.datasource.password=W5ZDxien5zuNskAQef8Mnz96gsdMHHo
quarkus.datasource.jdbc.url=***************************************************************************************
#POLICY REST CLIENT
#quarkus.rest-client.policy.url=http://localhost:8081/
quarkus.rest-client.policy.url=https://yolo-api.dev.yoloassicurazioni.it/latest/policy
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=http://localhost:8081
quarkus.rest-client.product.url=https://yolo-api.dev.yoloassicurazioni.it/latest/product
#WARRANTIES REST CLIENT
quarkus.rest-client.warranty-rules.url=http://yolo.backoffice.dev.yoloassicurazioni.it/backoffice/iad
#quarkus.rest-client.warranty-rules.url=http://localhost:8085/iad
#CUSTOMER REST CLIENT
#quarkus.rest-client.iad-customer.url=http://localhost:8080
quarkus.rest-client.iad-customer.url=https://yolo-api.dev.yoloassicurazioni.it/latest/customer
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=localhost:8083
quarkus.rest-client.iad-policy.url=https://yolo-api.dev.yoloassicurazioni.it/latest/policy
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=http://localhost:8084
quarkus.rest-client.catalog.url=https://yolo.dev.yoloassicurazioni.it/iad-catalog
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://yolo-api.dev.yoloassicurazioni.it/latest/comunication
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=https://yolo-api.dev.yoloassicurazioni.it/latest/document
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
order.prefix-code=Y-