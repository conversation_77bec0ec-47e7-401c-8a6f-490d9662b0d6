quarkus.datasource.username=megauser
quarkus.datasource.password=dBWaz7P6pjenJUhE2bwDHpDU8xfK7Ga
quarkus.datasource.jdbc.url=****************************************************************************************
#POLICY REST CLIENT
#quarkus.rest-client.policy.url=http://localhost:8081/
quarkus.rest-client.policy.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/policy
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=http://localhost:8088
quarkus.rest-client.product.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/product
#WARRANTIES REST CLIENT
quarkus.rest-client.warranty-rules.url=http://yolo.backoffice.preprod.yoloassicurazioni.it/backoffice/iad
#quarkus.rest-client.warranty-rules.url=http://localhost:8085/iad
#CUSTOMER REST CLIENT
#quarkus.rest-client.iad-customer.url=http://localhost:8080
quarkus.rest-client.iad-customer.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/customer
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=http://localhost:8084
quarkus.rest-client.catalog.url=https://yolo.preprod.yoloassicurazioni.it/iad-catalog
#POLICY REST CLIENT
#quarkus.rest-client.iad-policy.url=localhost:8083
quarkus.rest-client.iad-policy.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/policy
#COMMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=https://yolo-api.preprod.yoloassicurazioni.it/latest/comunication
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=https://yolo-api.yoloassicurazioni.it/latest/document
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_pdGOaemaj
order.prefix-code=Y-