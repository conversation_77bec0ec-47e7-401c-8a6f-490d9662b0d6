quarkus.banner.enabled=false
quarkus.http.port=8080
#quarkus.http.host=************
# Configure default tenant datasource
quarkus.datasource.yolo.db-kind=postgresql
quarkus.datasource.yolo.username=yolo
quarkus.datasource.yolo.password=yolo
quarkus.datasource.yolo.jdbc.url=************************************************
quarkus.datasource.yolo.jdbc.driver=io.opentelemetry.instrumentation.jdbc.OpenTelemetryDriver
# Configure FCA tenant datasource
quarkus.datasource.fca.db-kind=postgresql
quarkus.datasource.fca.username=yolo
quarkus.datasource.fca.password=yolo
quarkus.datasource.fca.jdbc.url=************************************************
quarkus.datasource.fca.jdbc.driver=io.opentelemetry.instrumentation.jdbc.OpenTelemetryDriver
# Configure test datasource
%test.quarkus.datasource.db-kind=postgresql
%test.quarkus.datasource.username=yolo
%test.quarkus.datasource.password=yolo
%test.quarkus.datasource.jdbc.url=jdbc:tc:postgresql://postgres:5432/fca_bank_db
%test.quarkus.datasource.jdbc.driver=org.testcontainers.jdbc.ContainerDatabaseDriver
# Hibernate
quarkus.hibernate-orm.dialect=org.hibernate.dialect.PostgreSQLDialect
quarkus.hibernate-orm.jdbc.timezone=UTC
quarkus.hibernate-orm.log.sql=false
quarkus.hibernate-orm.multitenant=DATABASE
# Image build
#quarkus.native.container-build=true
#JWT configuration
# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false
# JWT configuration
# JWT disable token
#quarkus.http.auth.proactive=false
#quarkus.oauth2.enabled=false
#quarkus.security.enabled=false
#quarkus.smallrye-jwt.enabled=false
mp.jwt.verify.publickey.location=publicKey.pem
mp.jwt.decrypt.key.location=keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm=RSA_OAEP_256
smallrye.jwt.key-encryption-algorithm=RSA-OAEP-256
# Logging
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Order API
quarkus.smallrye-openapi.info-version=1.0.0
# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true
#POLICY REST CLIENT
quarkus.rest-client.policy.url=https://api.dev.yoloassicurazioni.it/iad-policy
#quarkus.rest-client.policy.url=http://localhost:8082/
quarkus.rest-client.policy.scope=javax.inject.Singleton
#PRODUCT REST CLIENT
#quarkus.rest-client.product.url=https://api.dev.yoloassicurazioni.it/iad-policy
quarkus.rest-client.product.url=http://iad-product:8080/
quarkus.rest-client.product.scope=javax.inject.Singleton
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=https://api.dev.yoloassicurazioni.it/iad-policy
quarkus.rest-client.catalog.url=http://iad-catalog:8080/
quarkus.rest-client.catalog.scope=javax.inject.Singleton
