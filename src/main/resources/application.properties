#quarkus.profile=preprod
quarkus.profile=dev
tenant=cnpsi-polonia
%dev.quarkus.http.port=8081

base-url=https://${tenant}-api.${quarkus.profile}.yoloassicurazioni.it

quarkus.banner.enabled=false

quarkus.rest-client.quote.url=TOBEDEFINED
quarkus.rest-client.quote.scope=javax.inject.Singleton

# Configure default tenant datasource
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=cnpsi_polonia_dev
%dev.quarkus.datasource.password=1IZld4zia3VIgTNyk6uvRow2G955ZRfYCS
%preprod.quarkus.datasource.password=dBWaz7P6pjenJUhE2bwDHpDU8xfK7Ga
%dev.quarkus.datasource.jdbc.url=************************************************************************************************
%preprod.quarkus.datasource.jdbc.url=****************************************************************************************
quarkus.datasource.jdbc.driver=org.postgresql.Driver

# Hibernate
quarkus.hibernate-orm.dialect=org.hibernate.dialect.PostgreSQLDialect
quarkus.hibernate-orm.jdbc.timezone=UTC
quarkus.hibernate-orm.log.sql=false

# JWT configuration
# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false
# JWT configuration
mp.jwt.verify.publickey.location=publicKey.pem
mp.jwt.decrypt.key.location=keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm=RSA_OAEP_256
smallrye.jwt.key-encryption-algorithm=RSA-OAEP-256

# Logging
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{2.}] (%t) %s%e%n

# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Order API
quarkus.smallrye-openapi.info-version=1.0.0

# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true

#POLICY REST CLIENT
quarkus.rest-client.policy.url=${base-url}/iad-policy/
quarkus.rest-client.policy.scope=javax.inject.Singleton

#PRODUCT REST CLIENT
quarkus.rest-client.product.url=${base-url}/iad-product/
quarkus.rest-client.product.scope=javax.inject.Singleton

#CATALOG REST CLIENT
%dev.quarkus.rest-client.catalog.url= http://localhost:8086/
%preprod.quarkus.rest-client.catalog.url=http://iad-catalog-deploy.yep--test.svc.cluster.local:8080/
quarkus.rest-client.catalog.scope=javax.inject.Singleton

#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=${base-url}/iad-customer/
quarkus.rest-client.iad-customer.scope=javax.inject.Singleton

#WARRANTY RULES REST CLIENT
quarkus.rest-client.warranty-rules.url=https://${tenant}.backoffice.${quarkus.profile}.yoloassicurazioni.it/backoffice/iad/
quarkus.rest-client.warranty-rules.scope=javax.inject.Singleton

#COMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=${base-url}/comunication-manager/
quarkus.rest-client.communication-manager.scope=javax.inject.Singleton

#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=${base-url}/iad-document/
quarkus.rest-client.iad-document.scope=javax.inject.Singleton

#IAD-POLICY REST CLIENT
quarkus.rest-client.iad-policy.url=${base-url}/iad-policy/
quarkus.rest-client.iad-policy.scope=javax.inject.Singleton
#IAD-SURVEY REST CLIENT
quarkus.rest-client.iad-survey.url=${base-url}/iad-survey/
quarkus.rest-client.iad-survey.scope=javax.inject.Singleton
#IAD-CONFIGURATION REST CLIENT
quarkus.rest-client.iad-configuration.url=${base-url}/iad-configuration/
quarkus.rest-client.iad-configuration.scope=javax.inject.Singleton

#IAD-TOKEN AUTH REST CLIENT
quarkus.rest-client.authorization-token.url=${base-url}/iad-token/
quarkus.rest-client.authorization-token.scope=javax.inject.Singleton

#REST CLIENT LOG
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=2000
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.level=DEBUG

#Add quarkus.rest-client.pgw.url
quarkus.rest-client.pgw.url=http://providers-gateway-deploy.yep--uat.svc.cluster.local:8080
quarkus.rest-client.pgw.scope=javax.inject.Singleton
quarkus.rest-client.pgw.read-timeout=50000

#OIDC AUTH SERVER VALIDATION TOKEN
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_hWop30twv
intermediary.users=intermediary-users
technical.users=technical-users
order.prefix-code=YYOLO-
tch.usr=tchusr29c23d122s
quarkus.auth.password=1Onc780xA38P7!

#GRPC CLIENT-TOKEN
quarkus.grpc.clients.tokenService.host=localhost
quarkus.grpc.clients.tokenService.port=9000
quarkus.oidc.roles.role-claim-path=cognito:groups