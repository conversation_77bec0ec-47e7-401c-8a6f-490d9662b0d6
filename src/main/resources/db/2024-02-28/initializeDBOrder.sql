CREATE SCHEMA IF NOT EXISTS "order";

CREATE SEQUENCE "order".anag_states_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

CREATE SEQUENCE "order".order_history_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

CREATE SEQUENCE "order".order_item_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

CREATE SEQUENCE "order".orders_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

CREATE SEQUENCE "order".workflow_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

CREATE TABLE IF NOT EXISTS "order".anag_states (
	id serial4 NOT NULL,
	state varchar NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NOT NULL DEFAULT now(),
	CONSTRAINT anag_states_pkey PRIMARY KEY (id),
	CONSTRAINT uk_orders_anag_states UNIQUE (state)
);

CREATE TABLE IF NOT EXISTS "order".order_history (
	id serial4 NOT NULL,
	order_code varchar NULL,
	step_state varchar NULL,
	order_state varchar NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL,
	CONSTRAINT order_history_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS "order".order_item (
	id serial4 NOT NULL,
	price varchar NULL,
	product_id int4 NULL,
	policy_number varchar NULL,
	master_policy_number varchar NULL,
	external_id varchar NULL,
	state varchar NULL,
	start_date timestamp NULL,
	expiration_date timestamp NULL,
	insured_item jsonb NULL DEFAULT '{}'::jsonb,
	order_id int4 NULL,
	quantity int4 NULL,
	quotation jsonb NULL DEFAULT '{}'::jsonb,
	packet_id int4 NULL,
	emission jsonb NULL DEFAULT '{}'::jsonb,
	"instance" jsonb NULL,
	promotion jsonb NULL DEFAULT '{}'::jsonb,
	CONSTRAINT order_item_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS "order".workflow (
	id serial4 NOT NULL,
	product_id int4 NULL,
	workflow varchar NULL,
	"enable" bool NULL DEFAULT true,
	CONSTRAINT workflow_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS "order".orders (
	id serial4 NOT NULL,
	order_code varchar NULL,
	policy_code varchar NULL,
	anag_state_id int4 NOT NULL,
	packet_id int4 NULL,
	product_id int4 NOT NULL,
	broker_id int4 NULL,
	company_id int4 NULL,
	customer_id int4 NOT NULL,
	insurance_premium numeric(10, 4) NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NOT NULL DEFAULT now(),
	payment_transaction_id int4 NULL,
	payment_token varchar NULL,
	product_type varchar NULL,
	payment_type varchar NULL,
	field_to_recover jsonb NULL,
	"language" varchar NULL,
	agenzia_di_riferimento varchar NULL,
	utm_source varchar NULL,
	pid int4 NULL,
	anonymous_user_data jsonb NULL,
	parent_order varchar(255) NULL,
	intermediary_order bool NULL,
	CONSTRAINT orders_pkey PRIMARY KEY (id),
	CONSTRAINT uk_orders_order_code UNIQUE (order_code),
	CONSTRAINT orders_anag_state_id_fkey FOREIGN KEY (anag_state_id) REFERENCES "order".anag_states(id)
);
