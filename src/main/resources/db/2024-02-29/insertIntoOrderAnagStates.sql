INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(1, 'Draft', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(2, 'Deleted', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(3, 'Confirmed', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(4, 'Failed', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(5, 'Elaboration', 'admin', 'admin', '2023-02-09 13:09:16.254', '2023-02-09 13:09:16.254');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(6, 'Completed', 'admin', 'admin', '2023-05-24 15:59:40.826', '2023-05-24 15:59:40.826');
INSERT INTO "order".anag_states
(id, state, created_by, updated_by, created_at, updated_at)
VALUES(7, 'Estimate', 'admin', 'admin', '2023-09-14 16:30:26.359', '2023-09-14 16:30:26.359');