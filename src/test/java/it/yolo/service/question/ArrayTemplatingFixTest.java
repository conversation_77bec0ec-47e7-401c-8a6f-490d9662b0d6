package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test specifico per verificare la correzione della gestione della sintassi non valida nei filtri array.
 */
class ArrayTemplatingFixTest {

    private QuestionTemplatingService templatingService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        templatingService = new QuestionTemplatingService();
        objectMapper = new ObjectMapper();

        // Setup test data
        orderEntity = new OrderEntity();
        orderEntity.setOrderCode("TEST-001");

        OrderItemEntity orderItemEntity = new OrderItemEntity();
        ObjectNode instance = objectMapper.createObjectNode();
        orderItemEntity.setInstance(instance);
        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup chosenWarranties with test data
        setupChosenWarranties();
    }

    private void setupChosenWarranties() {
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Fabbricato");
        warranty1.put("mandatory", true);
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("name", "Contenuto");
        warranty2.put("mandatory", true);
        warranties.add(warranty2);

        ObjectNode warranty3 = objectMapper.createObjectNode();
        warranty3.put("name", "Responsabilità Civile");
        warranty3.put("mandatory", false);
        warranties.add(warranty3);

        ObjectNode warranty4 = objectMapper.createObjectNode();
        warranty4.put("name", "Tutela Legale");
        warranty4.put("mandatory", false);
        warranties.add(warranty4);

        ObjectNode warranty5 = objectMapper.createObjectNode();
        warranty5.put("name", "Furto e Rapina");
        warranty5.put("mandatory", false);
        warranties.add(warranty5);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);
    }

    @Test
    void testInvalidFilterSyntax_NoEquals() {
        String content = "Sintassi non valida: {{chosenWarranties.warranties[invalid syntax]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Il placeholder dovrebbe rimanere invariato perché la sintassi non è valida
        assertEquals("Sintassi non valida: {{chosenWarranties.warranties[invalid syntax]}}.", result);
    }

    @Test
    void testInvalidFilterSyntax_EmptyProperty() {
        String content = "Proprietà vuota: {{chosenWarranties.warranties[=value]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Il placeholder dovrebbe rimanere invariato
        assertEquals("Proprietà vuota: {{chosenWarranties.warranties[=value]}}.", result);
    }

    @Test
    void testInvalidFilterSyntax_EmptyValue() {
        String content = "Valore vuoto: {{chosenWarranties.warranties[property=]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Il placeholder dovrebbe rimanere invariato
        assertEquals("Valore vuoto: {{chosenWarranties.warranties[property=]}}.", result);
    }

    @Test
    void testInvalidFilterSyntax_OnlySpaces() {
        String content = "Solo spazi: {{chosenWarranties.warranties[   ]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Il placeholder dovrebbe rimanere invariato
        assertEquals("Solo spazi: {{chosenWarranties.warranties[   ]}}.", result);
    }

    @Test
    void testValidFilterSyntax_NonexistentProperty() {
        String content = "Proprietà inesistente: {{chosenWarranties.warranties[nonexistent=value]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Il placeholder dovrebbe rimanere invariato perché nessun elemento ha la proprietà
        assertEquals("Proprietà inesistente: {{chosenWarranties.warranties[nonexistent=value]}}.", result);
    }

    @Test
    void testValidFilterSyntax_Wildcard() {
        String content = "Tutte con wildcard: {{chosenWarranties.warranties[*]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Dovrebbe funzionare e mostrare tutte le garanzie
        assertEquals("Tutte con wildcard: Fabbricato, Contenuto, Responsabilità Civile, Tutela Legale e Furto e Rapina.", result);
    }

    @Test
    void testValidFilterSyntax_MandatoryTrue() {
        String content = "Garanzie obbligatorie: {{chosenWarranties.warranties[mandatory=true]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Dovrebbe funzionare e mostrare solo le garanzie obbligatorie
        assertEquals("Garanzie obbligatorie: Fabbricato e Contenuto.", result);
    }

    @Test
    void testValidFilterSyntax_MandatoryFalse() {
        String content = "Garanzie aggiuntive: {{chosenWarranties.warranties[mandatory=false]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Dovrebbe funzionare e mostrare solo le garanzie aggiuntive
        assertEquals("Garanzie aggiuntive: Responsabilità Civile, Tutela Legale e Furto e Rapina.", result);
    }

    @Test
    void testMixedValidAndInvalidSyntax() {
        String content = "Valido: {{chosenWarranties.warranties[mandatory=true]}} e non valido: {{chosenWarranties.warranties[invalid syntax]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        // Solo il placeholder valido dovrebbe essere sostituito
        assertEquals("Valido: Fabbricato e Contenuto e non valido: {{chosenWarranties.warranties[invalid syntax]}}.", result);
    }

    @Test
    void testComplexInvalidSyntax() {
        String content = "Vari errori: {{chosenWarranties.warranties[no=equals=sign]}} e {{chosenWarranties.warranties[just text]}}.";
        String result = templatingService.processTemplate(content, orderEntity);

        // Il primo dovrebbe rimanere invariato (proprietà inesistente), il secondo dovrebbe rimanere invariato (sintassi non valida)
        assertEquals("Vari errori: {{chosenWarranties.warranties[no=equals=sign]}} e {{chosenWarranties.warranties[just text]}}.", result);
    }
}
