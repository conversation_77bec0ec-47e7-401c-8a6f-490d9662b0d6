package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for array templating functionality.
 * Tests the complete workflow of processing questions with array-based placeholders.
 */
class ArrayTemplatingIntegrationTest {

    private QuestionProcessorService processorService;
    private QuestionTemplatingService templatingService;
    private QuestionVisibilityService visibilityService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        // Create real instances for integration testing
        templatingService = new QuestionTemplatingService();
        visibilityService = new QuestionVisibilityService();
        processorService = new QuestionProcessorService();
        
        // Manually inject dependencies
        processorService.templatingService = templatingService;
        processorService.visibilityService = visibilityService;
        
        objectMapper = new ObjectMapper();
        
        setupOrderEntityWithWarranties();
    }

    private void setupOrderEntityWithWarranties() {
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("ORD-ARRAY-001");
        orderEntity.setPolicyCode("POL-ARRAY-001");
        orderEntity.setProductId(456);
        orderEntity.setInsurancePremium(new BigDecimal("2500.00"));
        orderEntity.setProductType("COMPREHENSIVE_INSURANCE");

        OrderItemEntity orderItemEntity = new OrderItemEntity();
        ObjectNode instance = objectMapper.createObjectNode();
        orderItemEntity.setInstance(instance);
        orderItemEntity.setPrice("2500.00");
        orderItemEntity.setPolicy_number("POL-ARRAY-001-ITEM");
        orderItemEntity.setState("ACTIVE");

        // Setup insured_item
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("value", "500000");
        insuredItem.put("propertyType", "VILLA");
        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup comprehensive chosenWarranties
        setupComprehensiveWarranties();
    }

    private void setupComprehensiveWarranties() {
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        // Mandatory warranties
        ObjectNode fabbricato = objectMapper.createObjectNode();
        fabbricato.put("name", "Fabbricato");
        fabbricato.put("mandatory", true);
        fabbricato.put("category", "base");
        fabbricato.put("priority", 1);
        warranties.add(fabbricato);

        ObjectNode contenuto = objectMapper.createObjectNode();
        contenuto.put("name", "Contenuto");
        contenuto.put("mandatory", true);
        contenuto.put("category", "base");
        contenuto.put("priority", 2);
        warranties.add(contenuto);

        // Optional warranties
        ObjectNode rc = objectMapper.createObjectNode();
        rc.put("name", "Responsabilità Civile");
        rc.put("mandatory", false);
        rc.put("category", "optional");
        rc.put("priority", 3);
        rc.put("premium", true);
        warranties.add(rc);

        ObjectNode tutela = objectMapper.createObjectNode();
        tutela.put("name", "Tutela Legale");
        tutela.put("mandatory", false);
        tutela.put("category", "optional");
        tutela.put("priority", 4);
        tutela.put("premium", false);
        warranties.add(tutela);

        ObjectNode furto = objectMapper.createObjectNode();
        furto.put("name", "Furto e Rapina");
        furto.put("mandatory", false);
        furto.put("category", "security");
        furto.put("priority", 5);
        furto.put("premium", true);
        warranties.add(furto);

        // Add nested data structure
        ObjectNode data = objectMapper.createObjectNode();
        data.set("warranties", warranties);
        chosenWarranties.set("data", data);

        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);
    }

    @Test
    void testCompleteArrayProcessing_BasicScenario() {
        List<Question> questions = createBasicArrayQuestions();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(4, result.size());
        
        // Question 1: All warranties
        assertEquals("Il suo pacchetto include: Fabbricato, Contenuto, Responsabilità Civile, Tutela Legale e Furto e Rapina.", 
                    result.get(0).getContent());
        
        // Question 2: Mandatory only
        assertEquals("Le garanzie base sono: Fabbricato e Contenuto.", 
                    result.get(1).getContent());
        
        // Question 3: Optional only
        assertEquals("Le garanzie aggiuntive disponibili sono: Responsabilità Civile, Tutela Legale e Furto e Rapina.", 
                    result.get(2).getContent());
        
        // Question 4: Premium warranties
        assertEquals("Le garanzie premium selezionate sono: Responsabilità Civile e Furto e Rapina.", 
                    result.get(3).getContent());
    }

    @Test
    void testCompleteArrayProcessing_NestedPath() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Garanzie dal percorso nidificato: {{chosenWarranties.warranties}}.");
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Garanzie dal percorso nidificato: Fabbricato, Contenuto, Responsabilità Civile, Tutela Legale e Furto e Rapina.", 
                    result.get(0).getContent());
    }

    @Test
    void testCompleteArrayProcessing_ComplexFiltering() {
        List<Question> questions = createComplexFilteringQuestions();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(3, result.size());
        
        // Question 1: Category filtering
        assertEquals("Garanzie di sicurezza: Furto e Rapina.", result.get(0).getContent());
        
        // Question 2: Priority filtering
        assertEquals("Garanzie prioritarie: Fabbricato.", result.get(1).getContent());
        
        // Question 3: Complex scenario with multiple placeholders
        assertTrue(result.get(2).getContent().contains("ORD-ARRAY-001"));
        assertTrue(result.get(2).getContent().contains("Fabbricato e Contenuto"));
        assertTrue(result.get(2).getContent().contains("Responsabilità Civile, Tutela Legale e Furto e Rapina"));
    }

    @Test
    void testCompleteArrayProcessing_EdgeCases() {
        List<Question> questions = createEdgeCaseQuestions();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(3, result.size());
        
        // Question 1: No matches - should keep original placeholder
        assertEquals("Garanzie inesistenti: {{chosenWarranties.warranties[nonexistent=value]}}.", 
                    result.get(0).getContent());
        
        // Question 2: Invalid syntax - should keep original placeholder
        assertEquals("Sintassi non valida: {{chosenWarranties.warranties[invalid syntax]}}.", 
                    result.get(1).getContent());
        
        // Question 3: Wildcard filter
        assertEquals("Tutte con wildcard: Fabbricato, Contenuto, Responsabilità Civile, Tutela Legale e Furto e Rapina.", 
                    result.get(2).getContent());
    }

    @Test
    void testCompleteArrayProcessing_EmptyAndNullScenarios() {
        // Test con warranties vuote
        ObjectNode emptyChosenWarranties = objectMapper.createObjectNode();
        ArrayNode emptyWarranties = objectMapper.createArrayNode();
        emptyChosenWarranties.set("warranties", emptyWarranties);

        // Modificato per utilizzare il nuovo percorso per chosenWarranties
        ObjectNode data = objectMapper.createObjectNode();
        data.set("warranties", emptyWarranties);
        ObjectNode chosenWarrantiesWrapper = objectMapper.createObjectNode();
        chosenWarrantiesWrapper.set("data", data);

        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarrantiesWrapper);

        List<Question> questions = new ArrayList<>();
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Garanzie vuote: {{chosenWarranties.warranties}}.");
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Garanzie vuote: {{chosenWarranties.warranties}}.", result.get(0).getContent());
        
        // Test con chosenWarranties nulle
        instance.remove("chosenWarranties");

        result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Garanzie vuote: {{chosenWarranties.warranties}}.", result.get(0).getContent());
    }

    @Test
    void testCompleteArrayProcessing_MixedWithRegularTemplating() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Ordine {{order.orderCode}} per {{insuredItem.propertyType}} di valore {{insuredItem.value}} EUR " +
                     "include {{chosenWarranties.warranties[mandatory=true]}} come base e " +
                     "{{chosenWarranties.warranties[mandatory=false]}} come opzioni aggiuntive.");
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        String expected = "Ordine ORD-ARRAY-001 per VILLA di valore 500000 EUR " +
                         "include Fabbricato e Contenuto come base e " +
                         "Responsabilità Civile, Tutela Legale e Furto e Rapina come opzioni aggiuntive.";
        assertEquals(expected, result.get(0).getContent());
    }

    @Test
    void testCompleteArrayProcessing_InPlaceModification() {
        List<Question> questions = createBasicArrayQuestions();
        int originalSize = questions.size();
        
        processorService.processQuestionsInPlace(questions, orderEntity);
        
        assertEquals(originalSize, questions.size());
        
        // Verify in-place modification worked
        assertEquals("Il suo pacchetto include: Fabbricato, Contenuto, Responsabilità Civile, Tutela Legale e Furto e Rapina.", 
                    questions.get(0).getContent());
        assertEquals("Le garanzie base sono: Fabbricato e Contenuto.", 
                    questions.get(1).getContent());
    }

    private List<Question> createBasicArrayQuestions() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Il suo pacchetto include: {{chosenWarranties.warranties}}.");
        questions.add(q1);
        
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Le garanzie base sono: {{chosenWarranties.warranties[mandatory=true]}}.");
        questions.add(q2);
        
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Le garanzie aggiuntive disponibili sono: {{chosenWarranties.warranties[mandatory=false]}}.");
        questions.add(q3);
        
        Question q4 = new Question();
        q4.setId(4);
        q4.setContent("Le garanzie premium selezionate sono: {{chosenWarranties.warranties[premium=true]}}.");
        questions.add(q4);
        
        return questions;
    }

    private List<Question> createComplexFilteringQuestions() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Garanzie di sicurezza: {{chosenWarranties.warranties[category=security]}}.");
        questions.add(q1);
        
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Garanzie prioritarie: {{chosenWarranties.warranties[priority=1]}}.");
        questions.add(q2);
        
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Riepilogo ordine {{order.orderCode}}: garanzie base {{chosenWarranties.warranties[mandatory=true]}} " +
                     "e opzioni {{chosenWarranties.warranties[mandatory=false]}} per un totale di {{chosenWarranties.warranties}}.");
        questions.add(q3);
        
        return questions;
    }

    private List<Question> createEdgeCaseQuestions() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Garanzie inesistenti: {{chosenWarranties.warranties[nonexistent=value]}}.");
        questions.add(q1);
        
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Sintassi non valida: {{chosenWarranties.warranties[invalid syntax]}}.");
        questions.add(q2);
        
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Tutte con wildcard: {{chosenWarranties.warranties[*]}}.");
        questions.add(q3);
        
        return questions;
    }
}
