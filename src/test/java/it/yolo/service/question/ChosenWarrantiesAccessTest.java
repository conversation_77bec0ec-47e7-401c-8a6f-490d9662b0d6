package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that chosenWarranties access is working correctly with the new structure:
 * orderEntity.getOrderItem().get(0).getInstance().get("chosenWarranties").get("data")
 */
class ChosenWarrantiesAccessTest {

    private QuestionVisibilityService visibilityService;
    private QuestionTemplatingService templatingService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        visibilityService = new QuestionVisibilityService();
        templatingService = new QuestionTemplatingService();
        objectMapper = new ObjectMapper();
        
        setupOrderEntityWithCorrectStructure();
    }

    private void setupOrderEntityWithCorrectStructure() {
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("TEST-001");

        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setPrice("1000.00");

        // Setup insured_item
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaProprietario", "P");
        insuredItem.put("tipologiaFabbricato", "VM");
        orderItemEntity.setInsured_item(insuredItem);

        // Setup the correct chosenWarranties structure
        setupChosenWarrantiesWithCorrectStructure(orderItemEntity);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);
    }

    private void setupChosenWarrantiesWithCorrectStructure(OrderItemEntity orderItemEntity) {
        // Create warranties array
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Fabbricato");
        warranty1.put("mandatory", true);
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("name", "Contenuto");
        warranty2.put("mandatory", true);
        warranties.add(warranty2);

        ObjectNode warranty3 = objectMapper.createObjectNode();
        warranty3.put("name", "Furto e Rapina");
        warranty3.put("mandatory", false);
        warranties.add(warranty3);

        // Create the correct structure: instance.chosenWarranties.data.warranties
        ObjectNode chosenWarrantiesData = objectMapper.createObjectNode();
        chosenWarrantiesData.set("warranties", warranties);

        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        chosenWarranties.set("data", chosenWarrantiesData);

        ObjectNode instanceNode = objectMapper.createObjectNode();
        instanceNode.set("chosenWarranties", chosenWarranties);

        orderItemEntity.setInstance(instanceNode);
    }

    @Test
    void testTemplatingService_AccessChosenWarranties() {
        // Test that templating service can access chosenWarranties with the correct structure
        String content = "Garanzie scelte: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        assertEquals("Garanzie scelte: Fabbricato, Contenuto e Furto e Rapina.", result);
    }

    @Test
    void testTemplatingService_FilteredChosenWarranties() {
        // Test filtered access
        String content = "Garanzie obbligatorie: {{chosenWarranties.warranties[mandatory=true]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        
        assertEquals("Garanzie obbligatorie: Fabbricato e Contenuto.", result);
    }

    @Test
    void testVisibilityService_CountOperator() {
        // Test count operator with chosenWarranties
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Rule: show if count of optional warranties > 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because there is 1 optional warranty");
    }

    @Test
    void testVisibilityService_ContainsOperator() {
        // Test contains operator with chosenWarranties
        Question question = new Question();
        question.setId(2);
        question.setContent("Test question");

        // Rule: show if warranties contains "Furto e Rapina"
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties");
        inclusionRule.put("operator", "contains");
        inclusionRule.put("value", "Furto e Rapina");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because 'Furto e Rapina' warranty exists");
    }

    @Test
    void testVisibilityService_ContainsOperator_NotFound() {
        // Test contains operator with non-existent warranty
        Question question = new Question();
        question.setId(3);
        question.setContent("Test question");

        // Rule: show if warranties contains "Kasko"
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties");
        inclusionRule.put("operator", "contains");
        inclusionRule.put("value", "Kasko");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden because 'Kasko' warranty does not exist");
    }

    @Test
    void testCombinedTemplatingAndVisibility() {
        // Test that both templating and visibility work together with the correct structure
        Question question = new Question();
        question.setId(4);
        question.setContent("Garanzie aggiuntive: {{chosenWarranties.warranties[mandatory=false]}}.");

        // Rule: show if count of optional warranties > 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        // Test visibility first
        boolean isVisible = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(isVisible, "Question should be visible");

        // Test templating
        String processedContent = templatingService.processTemplate(question.getContent(), orderEntity);
        assertEquals("Garanzie aggiuntive: Furto e Rapina.", processedContent);
    }

    @Test
    void testMissingChosenWarranties() {
        // Test behavior when chosenWarranties is missing
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        orderItem.setInstance(null);

        // Test templating
        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties}}.", result);

        // Test visibility
        Question question = new Question();
        question.setId(5);
        question.setContent("Test question");

        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean isVisible = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(isVisible, "Question should be hidden when chosenWarranties is missing");
    }

    @Test
    void testMissingDataNode() {
        // Test behavior when data node is missing in chosenWarranties
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        ObjectNode instanceNode = (ObjectNode) orderItem.getInstance();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        // Missing "data" node
        instanceNode.set("chosenWarranties", chosenWarranties);

        // Test templating
        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties}}.", result);

        // Test visibility
        Question question = new Question();
        question.setId(6);
        question.setContent("Test question");

        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean isVisible = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(isVisible, "Question should be hidden when data node is missing");
    }
}
