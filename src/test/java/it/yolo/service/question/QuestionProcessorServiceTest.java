package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class QuestionProcessorServiceTest {

    @Mock
    private QuestionTemplatingService templatingService;

    @Mock
    private QuestionVisibilityService visibilityService;

    @InjectMocks
    private QuestionProcessorService processorService;

    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        objectMapper = new ObjectMapper();
        
        // Setup test data
        orderEntity = new OrderEntity();
        orderEntity.setOrderCode("ORD-001");
        
        orderItemEntity = new OrderItemEntity();
        orderItemEntity.setPrice("100.00");
        
        // Setup insured_item
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("value", "250000");
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        insuredItem.set("company", company);
        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);
    }

    @Test
    void testProcessQuestions_EmptyList() {
        List<Question> questions = new ArrayList<>();
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertTrue(result.isEmpty());
        verifyNoInteractions(templatingService, visibilityService);
    }

    @Test
    void testProcessQuestions_NullList() {
        List<Question> result = processorService.processQuestions(null, orderEntity);
        
        assertTrue(result.isEmpty());
        verifyNoInteractions(templatingService, visibilityService);
    }

    @Test
    void testProcessQuestions_NullOrderEntity() {
        List<Question> questions = createTestQuestions();
        List<Question> result = processorService.processQuestions(questions, null);
        
        assertEquals(questions.size(), result.size());
        verifyNoInteractions(templatingService, visibilityService);
    }

    @Test
    void testProcessQuestions_AllVisible() {
        List<Question> questions = createTestQuestions();
        
        // Mock visibility service to return true for all questions
        when(visibilityService.isQuestionVisible(any(Question.class), eq(orderEntity)))
            .thenReturn(true);
        
        // Mock templating service
        when(templatingService.processTemplate("Question 1 with {{order.orderCode}}", orderEntity))
            .thenReturn("Question 1 with ORD-001");
        when(templatingService.processTemplate("Question 2 with {{insuredItem.value}}", orderEntity))
            .thenReturn("Question 2 with 250000");
        when(templatingService.processTemplate("Question 3 simple", orderEntity))
            .thenReturn("Question 3 simple");

        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(3, result.size());
        assertEquals("Question 1 with ORD-001", result.get(0).getContent());
        assertEquals("Question 2 with 250000", result.get(1).getContent());
        assertEquals("Question 3 simple", result.get(2).getContent());
        
        verify(visibilityService, times(3)).isQuestionVisible(any(Question.class), eq(orderEntity));
        verify(templatingService, times(3)).processTemplate(any(String.class), eq(orderEntity));
    }

    @Test
    void testProcessQuestions_SomeHidden() {
        List<Question> questions = createTestQuestions();
        
        // Mock visibility service: first visible, second hidden, third visible
        when(visibilityService.isQuestionVisible(eq(questions.get(0)), eq(orderEntity)))
            .thenReturn(true);
        when(visibilityService.isQuestionVisible(eq(questions.get(1)), eq(orderEntity)))
            .thenReturn(false);
        when(visibilityService.isQuestionVisible(eq(questions.get(2)), eq(orderEntity)))
            .thenReturn(true);
        
        // Mock templating service for visible questions only
        when(templatingService.processTemplate("Question 1 with {{order.orderCode}}", orderEntity))
            .thenReturn("Question 1 with ORD-001");
        when(templatingService.processTemplate("Question 3 simple", orderEntity))
            .thenReturn("Question 3 simple");

        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(2, result.size());
        assertEquals("Question 1 with ORD-001", result.get(0).getContent());
        assertEquals("Question 3 simple", result.get(1).getContent());
        
        verify(visibilityService, times(3)).isQuestionVisible(any(Question.class), eq(orderEntity));
        verify(templatingService, times(2)).processTemplate(any(String.class), eq(orderEntity));
    }

    @Test
    void testProcessQuestionsInPlace_AllVisible() {
        List<Question> questions = createTestQuestions();
        
        // Mock visibility service to return true for all questions
        when(visibilityService.isQuestionVisible(any(Question.class), eq(orderEntity)))
            .thenReturn(true);
        
        // Mock templating service - solo per le domande con template {{}}
        when(templatingService.processTemplate("Question 1 with {{order.orderCode}}", orderEntity))
            .thenReturn("Question 1 with ORD-001");
        when(templatingService.processTemplate("Question 2 with {{insuredItem.value}}", orderEntity))
            .thenReturn("Question 2 with 250000");
        // Question 3 non ha template, quindi non viene chiamato templatingService

        processorService.processQuestionsInPlace(questions, orderEntity);
        
        assertEquals(3, questions.size());
        assertEquals("Question 1 with ORD-001", questions.get(0).getContent());
        assertEquals("Question 2 with 250000", questions.get(1).getContent());
        assertEquals("Question 3 simple", questions.get(2).getContent()); // Rimane invariata
        
        verify(visibilityService, times(3)).isQuestionVisible(any(Question.class), eq(orderEntity));
        // Solo 2 chiamate al templating service perché la domanda 3 non ha placeholder
        verify(templatingService, times(2)).processTemplate(any(String.class), eq(orderEntity));
    }

    @Test
    void testProcessQuestionsInPlace_SomeHidden() {
        List<Question> questions = createTestQuestions();
        
        // Mock visibility service: first visible, second hidden, third visible
        when(visibilityService.isQuestionVisible(any(Question.class), eq(orderEntity)))
            .thenReturn(true, false, true);
        
        // Mock templating service solo per domande visibili che hanno template
        when(templatingService.processTemplate("Question 1 with {{order.orderCode}}", orderEntity))
            .thenReturn("Question 1 with ORD-001");
        // Question 2 è nascosta, quindi non viene processata
        // Question 3 non ha template, quindi non viene chiamato templatingService

        processorService.processQuestionsInPlace(questions, orderEntity);
        
        assertEquals(2, questions.size());
        assertEquals("Question 1 with ORD-001", questions.get(0).getContent());
        assertEquals("Question 3 simple", questions.get(1).getContent());
        
        verify(visibilityService, times(3)).isQuestionVisible(any(Question.class), eq(orderEntity));
        // Solo 1 chiamata al templating service: domanda 1 (domanda 2 nascosta, domanda 3 senza template)
        verify(templatingService, times(1)).processTemplate(any(String.class), eq(orderEntity));
    }

    @Test
    void testProcessQuestionsInPlace_EmptyList() {
        List<Question> questions = new ArrayList<>();
        processorService.processQuestionsInPlace(questions, orderEntity);
        
        assertTrue(questions.isEmpty());
        verifyNoInteractions(templatingService, visibilityService);
    }

    @Test
    void testProcessQuestionsInPlace_NullOrderEntity() {
        List<Question> questions = createTestQuestions();
        int originalSize = questions.size();
        
        processorService.processQuestionsInPlace(questions, null);
        
        assertEquals(originalSize, questions.size());
        verifyNoInteractions(templatingService, visibilityService);
    }

    @Test
    void testProcessQuestionsInPlace_NullContent() {
        List<Question> questions = new ArrayList<>();
        Question question = new Question();
        question.setId(1);
        question.setContent(null);
        questions.add(question);
        
        when(visibilityService.isQuestionVisible(any(Question.class), eq(orderEntity)))
            .thenReturn(true);

        processorService.processQuestionsInPlace(questions, orderEntity);
        
        assertEquals(1, questions.size());
        assertNull(questions.get(0).getContent());
        
        verify(visibilityService, times(1)).isQuestionVisible(any(Question.class), eq(orderEntity));
        verifyNoInteractions(templatingService);
    }

    private List<Question> createTestQuestions() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Question 1 with {{order.orderCode}}");
        q1.setPosition(1);
        questions.add(q1);
        
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Question 2 with {{insuredItem.value}}");
        q2.setPosition(2);
        
        // Add exclusion rule to q2
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode exclusionRules = objectMapper.createArrayNode();
        ObjectNode exclusionRule = objectMapper.createObjectNode();
        exclusionRule.put("field", "company.scoreESG");
        exclusionRule.put("value", "3");
        exclusionRule.put("operator", "gt");
        exclusionRules.add(exclusionRule);
        rule.set("exclusionRules", exclusionRules);
        q2.setRule(rule);
        
        questions.add(q2);
        
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Question 3 simple");
        q3.setPosition(3);
        questions.add(q3);
        
        return questions;
    }
}
