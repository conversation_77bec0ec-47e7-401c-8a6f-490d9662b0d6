package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QuestionTemplatingServiceTest {

    private QuestionTemplatingService templatingService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;

    @BeforeEach
    void setUp() {
        templatingService = new QuestionTemplatingService();
        objectMapper = new ObjectMapper();
        
        // Setup test data
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("ORD-001");
        orderEntity.setPolicyCode("POL-001");
        orderEntity.setProductId(456);
        orderEntity.setBrokerId(789);
        orderEntity.setCompanyId(101);
        orderEntity.setCustomerId(202);
        orderEntity.setInsurancePremium(new BigDecimal("150.50"));
        orderEntity.setCreatedBy("test-user");
        orderEntity.setUpdatedBy("test-user");
        orderEntity.setProductType("INSURANCE");

        orderItemEntity = new OrderItemEntity();
        ObjectNode instance = objectMapper.createObjectNode();
        orderItemEntity.setInstance(instance);
        orderItemEntity.setPrice("100.00");
        orderItemEntity.setAnnualPrice("1200.00");
        orderItemEntity.setProduct_id(456);
        orderItemEntity.setPolicy_number("POL-123");
        orderItemEntity.setMaster_policy_number("MPOL-123");
        orderItemEntity.setExternal_id("EXT-123");
        orderItemEntity.setState("ACTIVE");
        orderItemEntity.setQuantity(1);
        orderItemEntity.setPacketId(789);

        // Setup insured_item with nested data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("value", "250000");
        insuredItem.put("insuredAge", "35");
        
        // Add nested company object
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        company.put("name", "Test Company");
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup chosenWarranties with array of warranties
        setupChosenWarranties();
    }

    private void setupChosenWarranties() {
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        // Add mandatory warranties
        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Fabbricato");
        warranty1.put("mandatory", true);
        warranty1.put("code", "FAB");
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("name", "Contenuto");
        warranty2.put("mandatory", true);
        warranty2.put("code", "CON");
        warranties.add(warranty2);

        // Add optional warranty
        ObjectNode warranty3 = objectMapper.createObjectNode();
        warranty3.put("name", "Responsabilità Civile");
        warranty3.put("mandatory", false);
        warranty3.put("code", "RC");
        warranties.add(warranty3);

        // Add warranty with numeric property
        ObjectNode warranty4 = objectMapper.createObjectNode();
        warranty4.put("name", "Tutela Legale");
        warranty4.put("mandatory", false);
        warranty4.put("priority", 1);
        warranties.add(warranty4);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);

        // Modifica: Accedi a insured_item e poi a instance per impostare le chosenWarranties
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);
    }

    @Test
    void testProcessTemplate_NoPlaceholders() {
        String content = "This is a simple question without placeholders.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals(content, result);
    }

    @Test
    void testProcessTemplate_OrderFields() {
        String content = "Your order {{order.orderCode}} for product {{order.productId}} has premium {{order.insurancePremium}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Your order ORD-001 for product 456 has premium 150.50.", result);
    }

    @Test
    void testProcessTemplate_OrderItemFields() {
        String content = "Policy {{orderItem.policyNumber}} costs {{orderItem.price}} with quantity {{orderItem.quantity}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Policy POL-123 costs 100.00 with quantity 1.", result);
    }

    @Test
    void testProcessTemplate_InsuredItemFields() {
        String content = "Property value is {{insuredItem.value}} and insured age is {{insuredItem.insuredAge}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Property value is 250000 and insured age is 35.", result);
    }

    @Test
    void testProcessTemplate_NestedInsuredItemFields() {
        String content = "Company {{insuredItem.company.name}} has ESG score {{insuredItem.company.scoreESG}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Company Test Company has ESG score 4.", result);
    }

    @Test
    void testProcessTemplate_MixedPlaceholders() {
        String content = "Order {{order.orderCode}} for {{insuredItem.company.name}} with value {{insuredItem.value}} costs {{orderItem.price}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Order ORD-001 for Test Company with value 250000 costs 100.00.", result);
    }

    @Test
    void testProcessTemplate_UnknownPlaceholder() {
        String content = "Unknown field: {{order.unknownField}} and {{insuredItem.unknownField}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Unknown field: {{order.unknownField}} and {{insuredItem.unknownField}}.", result);
    }

    @Test
    void testProcessTemplate_NullContent() {
        String result = templatingService.processTemplate(null, orderEntity);
        assertNull(result);
    }

    @Test
    void testProcessTemplate_EmptyContent() {
        String result = templatingService.processTemplate("", orderEntity);
        assertEquals("", result);
    }

    @Test
    void testProcessTemplate_NullOrderEntity() {
        String content = "Order {{order.orderCode}} test.";
        String result = templatingService.processTemplate(content, null);
        assertEquals(content, result);
    }

    @Test
    void testProcessTemplate_NoOrderItems() {
        orderEntity.setOrderItem(new ArrayList<>());
        String content = "Price: {{orderItem.price}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Price: {{orderItem.price}}", result);
    }

    @Test
    void testProcessTemplate_NullInsuredItem() {
        orderItemEntity.setInsured_item(null);
        String content = "Value: {{insuredItem.value}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Value: {{insuredItem.value}}", result);
    }

    @Test
    void testProcessTemplate_MultipleSamePlaceholders() {
        String content = "Order {{order.orderCode}} - Code: {{order.orderCode}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Order ORD-001 - Code: ORD-001", result);
    }

    @Test
    void testProcessTemplate_ComplexScenario() {
        String content = "Gentile cliente, la sua polizza {{orderItem.policyNumber}} " +
                        "per l'immobile di valore {{insuredItem.value}} EUR " +
                        "della compagnia {{insuredItem.company.name}} " +
                        "ha un premio annuale di {{orderItem.annualPrice}} EUR. " +
                        "L'età dell'assicurato è {{insuredItem.insuredAge}} anni.";
        
        String expected = "Gentile cliente, la sua polizza POL-123 " +
                         "per l'immobile di valore 250000 EUR " +
                         "della compagnia Test Company " +
                         "ha un premio annuale di 1200.00 EUR. " +
                         "L'età dell'assicurato è 35 anni.";
        
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals(expected, result);
    }

    @Test
    void testProcessTemplate_InvalidPlaceholderFormat() {
        String content = "Invalid: {order.orderCode} and {{order.orderCode} and {order.orderCode}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Invalid: {order.orderCode} and {{order.orderCode} and {order.orderCode}}", result);
    }

    @Test
    void testProcessTemplate_EmptyPlaceholder() {
        String content = "Empty placeholder: {{}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Empty placeholder: {{}}", result);
    }

    // ===== ARRAY PROCESSING TESTS =====

    @Test
    void testProcessTemplate_ChosenWarranties_AllWarranties() {
        String content = "Le garanzie scelte sono: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Le garanzie scelte sono: Fabbricato, Contenuto, Responsabilità Civile e Tutela Legale.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_MandatoryOnly() {
        String content = "Le garanzie obbligatorie sono: {{chosenWarranties.warranties[mandatory=true]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Le garanzie obbligatorie sono: Fabbricato e Contenuto.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_OptionalOnly() {
        String content = "Le garanzie aggiuntive sono: {{chosenWarranties.warranties[mandatory=false]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Le garanzie aggiuntive sono: Responsabilità Civile e Tutela Legale.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_WildcardFilter() {
        String content = "Tutte le garanzie: {{chosenWarranties.warranties[*]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Tutte le garanzie: Fabbricato, Contenuto, Responsabilità Civile e Tutela Legale.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_NumericFilter() {
        String content = "Garanzie prioritarie: {{chosenWarranties.warranties[priority=1]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie prioritarie: Tutela Legale.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_NoMatches() {
        String content = "Garanzie speciali: {{chosenWarranties.warranties[special=true]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie speciali: {{chosenWarranties.warranties[special=true]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_EmptyArray() {
        // Setup empty warranties array
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode emptyWarranties = objectMapper.createArrayNode();
        chosenWarranties.set("warranties", emptyWarranties);
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);

        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_NullChosenWarranties() {
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", null);
        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_MissingNameProperty() {
        // Setup warranties with missing name properties
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Valid Warranty");
        warranty1.put("mandatory", true);
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("mandatory", true);
        // Missing name property
        warranties.add(warranty2);

        ObjectNode warranty3 = objectMapper.createObjectNode();
        warranty3.put("name", "Another Valid");
        warranty3.put("mandatory", true);
        warranties.add(warranty3);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);

        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: Valid Warranty e Another Valid.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_InvalidFilterSyntax() {
        String content = "Garanzie: {{chosenWarranties.warranties[invalid syntax]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties[invalid syntax]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_InvalidFilterSyntax_NoEquals() {
        String content = "Garanzie: {{chosenWarranties.warranties[noequals]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties[noequals]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_InvalidFilterSyntax_EmptyProperty() {
        String content = "Garanzie: {{chosenWarranties.warranties[=value]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties[=value]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_InvalidFilterSyntax_EmptyValue() {
        String content = "Garanzie: {{chosenWarranties.warranties[property=]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties[property=]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_MissingFilterProperty() {
        String content = "Garanzie: {{chosenWarranties.warranties[nonexistent=value]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: {{chosenWarranties.warranties[nonexistent=value]}}.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_SingleItem() {
        // Setup with only one warranty
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Solo Warranty");
        warranty1.put("mandatory", true);
        warranties.add(warranty1);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);

        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);

        String content = "Garanzia: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzia: Solo Warranty.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_TwoItems() {
        // Setup with only two warranties
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "Prima");
        warranty1.put("mandatory", true);
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("name", "Seconda");
        warranty2.put("mandatory", true);
        warranties.add(warranty2);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);
        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);

        String content = "Garanzie: {{chosenWarranties.warranties}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie: Prima e Seconda.", result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_ComplexScenario() {
        String content = "Il pacchetto {{order.orderCode}} include le garanzie {{chosenWarranties.warranties[mandatory=true]}} " +
                        "come base, più le opzioni {{chosenWarranties.warranties[mandatory=false]}} per un totale di " +
                        "{{chosenWarranties.warranties}} garanzie.";

        String expected = "Il pacchetto ORD-001 include le garanzie Fabbricato e Contenuto " +
                         "come base, più le opzioni Responsabilità Civile e Tutela Legale per un totale di " +
                         "Fabbricato, Contenuto, Responsabilità Civile e Tutela Legale garanzie.";

        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals(expected, result);
    }

    @Test
    void testProcessTemplate_ChosenWarranties_BooleanStringComparison() {
        // Test with string "true"/"false" values
        ObjectNode dataNode = objectMapper.createObjectNode();
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        ObjectNode warranty1 = objectMapper.createObjectNode();
        warranty1.put("name", "String True");
        warranty1.put("active", "true");
        warranties.add(warranty1);

        ObjectNode warranty2 = objectMapper.createObjectNode();
        warranty2.put("name", "String False");
        warranty2.put("active", "false");
        warranties.add(warranty2);

        dataNode.set("warranties", warranties);
        chosenWarranties.set("data", dataNode);

        ObjectNode instance = (ObjectNode) orderEntity.getOrderItem().get(0).getInstance();
        instance.set("chosenWarranties", chosenWarranties);

        String content = "Garanzie attive: {{chosenWarranties.warranties[active=true]}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Garanzie attive: String True.", result);
    }
}
