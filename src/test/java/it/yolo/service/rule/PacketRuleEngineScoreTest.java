package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.packet.Data;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for PacketRuleEngine with company.scoreESG rules
 */
@QuarkusTest
public class PacketRuleEngineScoreTest {

    @Inject
    PacketRuleEngine packetRuleEngine;

    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;
    private List<Packet> packets;

    @BeforeEach
    public void setup() {
        objectMapper = new ObjectMapper();

        // Setup order entity
        orderEntity = new OrderEntity();
        orderItemEntity = new OrderItemEntity();

        // Setup insured_item with test data
        ObjectNode insuredItem = objectMapper.createObjectNode();

        // Add nested company object with scoreESG field
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4"); // Default value is 4 (greater than 3)
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup packets
        packets = new ArrayList<>();
    }

    private Packet createPacket(int id, String name, JsonNode packetCondition) {
        PacketResponse packetResponse = new PacketResponse();
        Data data = new Data();
        data.setId(id);
        data.setName(name);
        data.setPacketCondition(packetCondition);
        packetResponse.setData(data);
        return new Packet(packetResponse);
    }

    @Test
    @DisplayName("Test company.scoreESG exclusion rule")
    public void testCompanyScoreESGExclusionRule() {
        // Create packet 1 with exclusion rule: exclude if company.scoreESG < 3
        ObjectNode packetCondition1 = objectMapper.createObjectNode();
        ArrayNode exclusionRules = packetCondition1.putArray("exclusionRules");

        // Add exclusion rule: company.scoreESG < 3
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("field", "company.scoreESG");
        rule.put("value", "3");
        rule.put("operator", "lt");
        exclusionRules.add(rule);

        Packet packet1 = createPacket(1, "Packet with scoreESG exclusion", packetCondition1);

        // Create packet 2 with no conditions
        Packet packet2 = createPacket(2, "Packet with no conditions", null);

        // Add both packets to the list
        packets.add(packet1);
        packets.add(packet2);

        // Test 1: company.scoreESG = 4 (greater than 3)
        ObjectNode insuredItem = (ObjectNode) orderItemEntity.getInsured_item();
        ObjectNode company = (ObjectNode) insuredItem.get("company");
        company.put("scoreESG", "4");

        List<Packet> result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(2, result.size(), "Both packets should be included when scoreESG > 3");
        assertTrue(result.contains(packet1), "Packet 1 should be included when scoreESG > 3");
        assertTrue(result.contains(packet2), "Packet 2 should be included when scoreESG > 3");

        // Test 2: company.scoreESG = 2 (less than 3)
        company.put("scoreESG", "2");

        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Only packet 2 should be included when scoreESG < 3");
        assertFalse(result.contains(packet1), "Packet 1 should be excluded when scoreESG < 3");
        assertTrue(result.contains(packet2), "Packet 2 should be included when scoreESG < 3");

        // Test 3: company.scoreESG = null
        company.putNull("scoreESG");

        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(2, result.size(), "Both packets should be included when scoreESG is null");
        assertTrue(result.contains(packet1), "Packet 1 should be included when scoreESG is null");
        assertTrue(result.contains(packet2), "Packet 2 should be included when scoreESG is null");

        // Test 4: company = null
        insuredItem.remove("company");

        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(2, result.size(), "Both packets should be included when company is null");
        assertTrue(result.contains(packet1), "Packet 1 should be included when company is null");
        assertTrue(result.contains(packet2), "Packet 2 should be included when company is null");

        // Test 5: insured_item = null
        orderItemEntity.setInsured_item(null);

        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(2, result.size(), "Both packets should be included when insured_item is null");
        assertTrue(result.contains(packet1), "Packet 1 should be included when insured_item is null");
        assertTrue(result.contains(packet2), "Packet 2 should be included when insured_item is null");
    }
}
