package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.packet.Data;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for PacketRuleEngine
 */
@QuarkusTest
public class PacketRuleEngineTest {

    @Inject
    PacketRuleEngine packetRuleEngine;

    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;
    private List<Packet> packets;

    @BeforeEach
    public void setup() {
        objectMapper = new ObjectMapper();

        // Setup order entity
        orderEntity = new OrderEntity();
        orderItemEntity = new OrderItemEntity();

        // Setup insured_item with test data
        // Using the real case values:
        // tipologiaUsoAbitazione: "S"
        // tipologiaTitoloAbitazione: "C"
        // tipologiaCostruttivaAbitazione: "A" (will test others in specific tests)
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaCostruttivaAbitazione", "A");

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup packets
        packets = new ArrayList<>();
    }

    private Packet createPacket(int id, String name, JsonNode packetCondition) {
        PacketResponse packetResponse = new PacketResponse();
        Data data = new Data();
        data.setId(id);
        data.setName(name);
        data.setPacketCondition(packetCondition);
        packetResponse.setData(data);
        return new Packet(packetResponse);
    }

    @Test
    @DisplayName("Test empty packets list")
    public void testEmptyPacketsList() {
        List<Packet> result = packetRuleEngine.filterPackets(new ArrayList<>(), orderEntity);
        assertTrue(result.isEmpty(), "Result should be empty for empty packets list");
    }

    @Test
    @DisplayName("Test null order entity")
    public void testNullOrderEntity() {
        List<Packet> testPackets = new ArrayList<>();
        testPackets.add(createPacket(1, "Test Packet", null));

        List<Packet> result = packetRuleEngine.filterPackets(testPackets, null);
        assertEquals(testPackets, result, "Result should be the same as input for null order entity");
    }

    @Test
    @DisplayName("Test packet with no conditions")
    public void testPacketWithNoConditions() {
        Packet packet = createPacket(1, "Test Packet", null);
        packets.add(packet);

        List<Packet> result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Packet with no conditions should be included");
        assertEquals(packet, result.get(0), "Packet with no conditions should be included");
    }

    @Test
    @DisplayName("Test real case scenario with two packets and different conditions")
    public void testRealCaseScenarioWithSinglePacket() {
        // Test case with two packets:
        // Packet 1: {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "C", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"}
        // Packet 2: {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "PL", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"}

        // Create packet 1 condition with rule groups
        ObjectNode packetCondition1 = objectMapper.createObjectNode();
        ArrayNode ruleGroups1 = packetCondition1.putArray("ruleGroups");

        // Create inclusion group with AND operator for the first two conditions
        ObjectNode basicConditionsGroup1 = objectMapper.createObjectNode();
        basicConditionsGroup1.put("type", "inclusion");
        basicConditionsGroup1.put("operator", "AND");

        // Add rules to the basic conditions group
        ArrayNode basicRules1 = basicConditionsGroup1.putArray("rules");

        // Rule 1: tipologiaUsoAbitazione = S
        ObjectNode rule1_1 = objectMapper.createObjectNode();
        rule1_1.put("field", "tipologiaUsoAbitazione");
        rule1_1.put("value", "S");
        rule1_1.put("operator", "eq");
        basicRules1.add(rule1_1);

        // Rule 2: tipologiaTitoloAbitazione = C
        ObjectNode rule1_2 = objectMapper.createObjectNode();
        rule1_2.put("field", "tipologiaTitoloAbitazione");
        rule1_2.put("value", "C");
        rule1_2.put("operator", "eq");
        basicRules1.add(rule1_2);

        // Create inclusion group with OR operator for the tipologiaCostruttivaAbitazione options
        ObjectNode constructionTypeGroup1 = objectMapper.createObjectNode();
        constructionTypeGroup1.put("type", "inclusion");
        constructionTypeGroup1.put("operator", "OR");

        // Add rules to the construction type group
        ArrayNode constructionRules1 = constructionTypeGroup1.putArray("rules");

        // Rule 3a: tipologiaCostruttivaAbitazione = A
        ObjectNode rule1_3a = objectMapper.createObjectNode();
        rule1_3a.put("field", "tipologiaCostruttivaAbitazione");
        rule1_3a.put("value", "A");
        rule1_3a.put("operator", "eq");
        constructionRules1.add(rule1_3a);

        // Rule 3b: tipologiaCostruttivaAbitazione = VSCU
        ObjectNode rule1_3b = objectMapper.createObjectNode();
        rule1_3b.put("field", "tipologiaCostruttivaAbitazione");
        rule1_3b.put("value", "VSCU");
        rule1_3b.put("operator", "eq");
        constructionRules1.add(rule1_3b);

        // Rule 3c: tipologiaCostruttivaAbitazione = VCS
        ObjectNode rule1_3c = objectMapper.createObjectNode();
        rule1_3c.put("field", "tipologiaCostruttivaAbitazione");
        rule1_3c.put("value", "VCS");
        rule1_3c.put("operator", "eq");
        constructionRules1.add(rule1_3c);

        // Add both groups to rule groups
        ruleGroups1.add(basicConditionsGroup1);
        ruleGroups1.add(constructionTypeGroup1);

        // Set group logic operator to AND (both groups must be satisfied)
        packetCondition1.put("groupLogicOperator", "AND");

        // Create packet 1
        Packet packet1 = createPacket(1, "Packet for C", packetCondition1);

        // Create packet 2 condition with rule groups (similar to packet 1 but with PL instead of C)
        ObjectNode packetCondition2 = objectMapper.createObjectNode();
        ArrayNode ruleGroups2 = packetCondition2.putArray("ruleGroups");

        // Create inclusion group with AND operator for the first two conditions
        ObjectNode basicConditionsGroup2 = objectMapper.createObjectNode();
        basicConditionsGroup2.put("type", "inclusion");
        basicConditionsGroup2.put("operator", "AND");

        // Add rules to the basic conditions group
        ArrayNode basicRules2 = basicConditionsGroup2.putArray("rules");

        // Rule 1: tipologiaUsoAbitazione = S
        ObjectNode rule2_1 = objectMapper.createObjectNode();
        rule2_1.put("field", "tipologiaUsoAbitazione");
        rule2_1.put("value", "S");
        rule2_1.put("operator", "eq");
        basicRules2.add(rule2_1);

        // Rule 2: tipologiaTitoloAbitazione = PL
        ObjectNode rule2_2 = objectMapper.createObjectNode();
        rule2_2.put("field", "tipologiaTitoloAbitazione");
        rule2_2.put("value", "PL");
        rule2_2.put("operator", "eq");
        basicRules2.add(rule2_2);

        // Create inclusion group with OR operator for the tipologiaCostruttivaAbitazione options
        ObjectNode constructionTypeGroup2 = objectMapper.createObjectNode();
        constructionTypeGroup2.put("type", "inclusion");
        constructionTypeGroup2.put("operator", "OR");

        // Add rules to the construction type group
        ArrayNode constructionRules2 = constructionTypeGroup2.putArray("rules");

        // Rule 3a: tipologiaCostruttivaAbitazione = A
        ObjectNode rule2_3a = objectMapper.createObjectNode();
        rule2_3a.put("field", "tipologiaCostruttivaAbitazione");
        rule2_3a.put("value", "A");
        rule2_3a.put("operator", "eq");
        constructionRules2.add(rule2_3a);

        // Rule 3b: tipologiaCostruttivaAbitazione = VSCU
        ObjectNode rule2_3b = objectMapper.createObjectNode();
        rule2_3b.put("field", "tipologiaCostruttivaAbitazione");
        rule2_3b.put("value", "VSCU");
        rule2_3b.put("operator", "eq");
        constructionRules2.add(rule2_3b);

        // Rule 3c: tipologiaCostruttivaAbitazione = VCS
        ObjectNode rule2_3c = objectMapper.createObjectNode();
        rule2_3c.put("field", "tipologiaCostruttivaAbitazione");
        rule2_3c.put("value", "VCS");
        rule2_3c.put("operator", "eq");
        constructionRules2.add(rule2_3c);

        // Add both groups to rule groups
        ruleGroups2.add(basicConditionsGroup2);
        ruleGroups2.add(constructionTypeGroup2);

        // Set group logic operator to AND (both groups must be satisfied)
        packetCondition2.put("groupLogicOperator", "AND");

        // Create packet 2
        Packet packet2 = createPacket(2, "Packet for PL", packetCondition2);

        // Add both packets to the list
        packets.add(packet1);
        packets.add(packet2);

        // Set insuredItem values to match packet 1
        ObjectNode insuredItem = (ObjectNode) orderItemEntity.getInsured_item();
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("tipologiaCostruttivaAbitazione", "A");

        // Test that only packet 1 is included
        List<Packet> result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Only packet 1 should be included");
        assertEquals(packet1, result.get(0), "Packet 1 should be included");

        // Change tipologiaTitoloAbitazione to PL to match packet 2
        insuredItem.put("tipologiaTitoloAbitazione", "PL");

        // Test that only packet 2 is included
        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Only packet 2 should be included");
        assertEquals(packet2, result.get(0), "Packet 2 should be included");

        // Change tipologiaCostruttivaAbitazione to VSCU (should still match both packets)
        insuredItem.put("tipologiaCostruttivaAbitazione", "VSCU");

        // Test that only packet 2 is still included (because tipologiaTitoloAbitazione is PL)
        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Only packet 2 should be included with VSCU");
        assertEquals(packet2, result.get(0), "Packet 2 should be included with VSCU");

        // Change tipologiaTitoloAbitazione back to C
        insuredItem.put("tipologiaTitoloAbitazione", "C");

        // Test that only packet 1 is included again
        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertEquals(1, result.size(), "Only packet 1 should be included with VSCU");
        assertEquals(packet1, result.get(0), "Packet 1 should be included with VSCU");

        // Change tipologiaCostruttivaAbitazione to OTHER (should not match any packet)
        insuredItem.put("tipologiaCostruttivaAbitazione", "OTHER");

        // Test that no packets are included
        result = packetRuleEngine.filterPackets(packets, orderEntity);
        assertTrue(result.isEmpty(), "No packets should be included with tipologiaCostruttivaAbitazione = OTHER");

        // Reset values for subsequent tests
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaCostruttivaAbitazione", "A");
    }

}
