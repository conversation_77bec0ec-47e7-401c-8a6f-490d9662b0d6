package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.packet.Data;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.records.Packet;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for verifying the migration from the old checkPacketFromOrderItem method
 * to the new PacketRuleEngine approach.
 */
@QuarkusTest
public class PacketRuleMigrationTest {

    @Inject
    PacketRuleEngine packetRuleEngine;

    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;
    private List<Packet> packets;

    @BeforeEach
    public void setup() {
        objectMapper = new ObjectMapper();

        // Setup order entity
        orderEntity = new OrderEntity();
        orderItemEntity = new OrderItemEntity();

        // Setup insured_item with test data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup packets
        packets = new ArrayList<>();
    }

    private Packet createPacket(int id, String name, JsonNode packetCondition) {
        PacketResponse packetResponse = new PacketResponse();
        Data data = new Data();
        data.setId(id);
        data.setName(name);
        data.setPacketCondition(packetCondition);
        packetResponse.setData(data);
        return new Packet(packetResponse);
    }

    /**
     * Test migration for all 6 packet types mentioned in the requirements:
     * 1) {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "C", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"} 
     * 2) {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "PL", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"} 
     * 3) {"tipologiaUsoAbitazione": "A", "tipologiaTitoloAbitazione": "P", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"} 
     * 4) {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "P", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"} 
     * 5) {"tipologiaUsoAbitazione": "A", "tipologiaTitoloAbitazione": "PL", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"} 
     * 6) {"tipologiaUsoAbitazione": "A", "tipologiaTitoloAbitazione": "C", "tipologiaCostruttivaAbitazione": "A oppure VSCU oppure VCS"}
     */
    @Test
    @DisplayName("Test migration for all 6 packet types")
    public void testMigrationForAllPacketTypes() {
        // Create all 6 packets with their respective conditions
        createAllPackets();

        // Test each combination of values to ensure the correct packet is selected
        testPacketSelection();
    }

    private void createAllPackets() {
        // Create packet 1: S, C, A/VSCU/VCS
        packets.add(createPacketWithConditions(1, "Packet S-C", "S", "C"));

        // Create packet 2: S, PL, A/VSCU/VCS
        packets.add(createPacketWithConditions(2, "Packet S-PL", "S", "PL"));

        // Create packet 3: A, P, A/VSCU/VCS
        packets.add(createPacketWithConditions(3, "Packet A-P", "A", "P"));

        // Create packet 4: S, P, A/VSCU/VCS
        packets.add(createPacketWithConditions(4, "Packet S-P", "S", "P"));

        // Create packet 5: A, PL, A/VSCU/VCS
        packets.add(createPacketWithConditions(5, "Packet A-PL", "A", "PL"));

        // Create packet 6: A, C, A/VSCU/VCS
        packets.add(createPacketWithConditions(6, "Packet A-C", "A", "C"));
    }

    private Packet createPacketWithConditions(int id, String name, String uso, String titolo) {
        ObjectNode packetCondition = objectMapper.createObjectNode();
        ArrayNode ruleGroups = packetCondition.putArray("ruleGroups");

        // Create inclusion group for uso and titolo
        ObjectNode basicConditionsGroup = objectMapper.createObjectNode();
        basicConditionsGroup.put("type", "inclusion");
        basicConditionsGroup.put("operator", "AND");

        // Add rules to the basic conditions group
        ArrayNode basicRules = basicConditionsGroup.putArray("rules");

        // Rule 1: tipologiaUsoAbitazione
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "tipologiaUsoAbitazione");
        rule1.put("value", uso);
        rule1.put("operator", "eq");
        basicRules.add(rule1);

        // Rule 2: tipologiaTitoloAbitazione
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaTitoloAbitazione");
        rule2.put("value", titolo);
        rule2.put("operator", "eq");
        basicRules.add(rule2);

        // Create inclusion group for tipologiaCostruttivaAbitazione with OR operator
        ObjectNode constructionTypeGroup = objectMapper.createObjectNode();
        constructionTypeGroup.put("type", "inclusion");
        constructionTypeGroup.put("operator", "OR");

        // Add rules to the construction type group
        ArrayNode constructionRules = constructionTypeGroup.putArray("rules");

        // Rule 3a: tipologiaCostruttivaAbitazione = A
        ObjectNode rule3a = objectMapper.createObjectNode();
        rule3a.put("field", "tipologiaCostruttivaAbitazione");
        rule3a.put("value", "A");
        rule3a.put("operator", "eq");
        constructionRules.add(rule3a);

        // Rule 3b: tipologiaCostruttivaAbitazione = VSCU
        ObjectNode rule3b = objectMapper.createObjectNode();
        rule3b.put("field", "tipologiaCostruttivaAbitazione");
        rule3b.put("value", "VSCU");
        rule3b.put("operator", "eq");
        constructionRules.add(rule3b);

        // Rule 3c: tipologiaCostruttivaAbitazione = VCS
        ObjectNode rule3c = objectMapper.createObjectNode();
        rule3c.put("field", "tipologiaCostruttivaAbitazione");
        rule3c.put("value", "VCS");
        rule3c.put("operator", "eq");
        constructionRules.add(rule3c);

        // Add both groups to rule groups
        ruleGroups.add(basicConditionsGroup);
        ruleGroups.add(constructionTypeGroup);

        // Set group logic operator to AND (both groups must be satisfied)
        packetCondition.put("groupLogicOperator", "AND");

        return createPacket(id, name, packetCondition);
    }

    private void testPacketSelection() {
        // Get insured_item to set values
        ObjectNode insuredItem = (ObjectNode) orderItemEntity.getInsured_item();

        // Test all combinations of tipologiaUsoAbitazione and tipologiaTitoloAbitazione
        // with each of the valid tipologiaCostruttivaAbitazione values
        String[] usoValues = {"S", "A"};
        String[] titoloValues = {"C", "PL", "P"};
        String[] costruzioneValues = {"A", "VSCU", "VCS"};

        for (String uso : usoValues) {
            for (String titolo : titoloValues) {
                for (String costruzione : costruzioneValues) {
                    // Set values in insured_item
                    insuredItem.put("tipologiaUsoAbitazione", uso);
                    insuredItem.put("tipologiaTitoloAbitazione", titolo);
                    insuredItem.put("tipologiaCostruttivaAbitazione", costruzione);

                    // Filter packets
                    List<Packet> filteredPackets = packetRuleEngine.filterPackets(packets, orderEntity);

                    // Verify that exactly one packet is selected
                    assertEquals(1, filteredPackets.size(), 
                        String.format("Expected exactly one packet for uso=%s, titolo=%s, costruzione=%s", 
                            uso, titolo, costruzione));

                    // Verify that the correct packet is selected
                    int expectedPacketId = getExpectedPacketId(uso, titolo);
                    assertEquals(expectedPacketId, filteredPackets.get(0).packetResponse().getData().getId(),
                        String.format("Expected packet %d for uso=%s, titolo=%s, costruzione=%s", 
                            expectedPacketId, uso, titolo, costruzione));
                }
            }
        }

        // Test with an invalid tipologiaCostruttivaAbitazione value
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("tipologiaCostruttivaAbitazione", "INVALID");

        // Filter packets
        List<Packet> filteredPackets = packetRuleEngine.filterPackets(packets, orderEntity);

        // Verify that no packets are selected
        assertTrue(filteredPackets.isEmpty(), 
            "Expected no packets for invalid tipologiaCostruttivaAbitazione");
    }

    private int getExpectedPacketId(String uso, String titolo) {
        // Map the combination of uso and titolo to the expected packet ID
        if ("S".equals(uso) && "C".equals(titolo)) return 1;
        if ("S".equals(uso) && "PL".equals(titolo)) return 2;
        if ("A".equals(uso) && "P".equals(titolo)) return 3;
        if ("S".equals(uso) && "P".equals(titolo)) return 4;
        if ("A".equals(uso) && "PL".equals(titolo)) return 5;
        if ("A".equals(uso) && "C".equals(titolo)) return 6;
        
        // Should not happen with our test data
        return -1;
    }

    /**
     * Test that simulates the old checkPacketFromOrderItem method and compares its results
     * with the new PacketRuleEngine approach.
     */
    @Test
    @DisplayName("Test comparison between old and new approach")
    public void testComparisonBetweenOldAndNewApproach() {
        // Create all 6 packets with their respective conditions
        createAllPackets();

        // Get insured_item to set values
        ObjectNode insuredItem = (ObjectNode) orderItemEntity.getInsured_item();

        // Test various combinations
        String[][] testCases = {
            // uso, titolo, costruzione
            {"S", "C", "A"},
            {"S", "PL", "VSCU"},
            {"A", "P", "VCS"},
            {"S", "P", "A"},
            {"A", "PL", "VSCU"},
            {"A", "C", "VCS"}
        };

        for (String[] testCase : testCases) {
            String uso = testCase[0];
            String titolo = testCase[1];
            String costruzione = testCase[2];

            // Set values in insured_item
            insuredItem.put("tipologiaUsoAbitazione", uso);
            insuredItem.put("tipologiaTitoloAbitazione", titolo);
            insuredItem.put("tipologiaCostruttivaAbitazione", costruzione);

            // Simulate old approach
            Packet oldApproachPacket = simulateOldApproach(uso, titolo, costruzione);

            // Use new approach
            List<Packet> filteredPackets = packetRuleEngine.filterPackets(packets, orderEntity);
            assertFalse(filteredPackets.isEmpty(), 
                String.format("Expected at least one packet for uso=%s, titolo=%s, costruzione=%s", 
                    uso, titolo, costruzione));
            
            Packet newApproachPacket = filteredPackets.get(0);

            // Compare results
            assertEquals(oldApproachPacket.packetResponse().getData().getId(), 
                         newApproachPacket.packetResponse().getData().getId(),
                String.format("Old and new approach should return the same packet for uso=%s, titolo=%s, costruzione=%s", 
                    uso, titolo, costruzione));
        }
    }

    /**
     * Simulate the old checkPacketFromOrderItem method logic
     */
    private Packet simulateOldApproach(String uso, String titolo, String costruzione) {
        // In the old approach, we would directly find the packet based on the tipologia values
        // Here we simulate that by finding the packet with matching conditions
        
        int expectedPacketId = getExpectedPacketId(uso, titolo);
        
        // Find the packet with the matching ID
        for (Packet packet : packets) {
            if (packet.packetResponse().getData().getId() == expectedPacketId) {
                return packet;
            }
        }
        
        // Should not happen with our test data
        return null;
    }

    /**
     * Test that demonstrates how to handle the case where multiple packets might match
     * and we need to select the best one based on some criteria.
     */
    @Test
    @DisplayName("Test handling multiple matching packets")
    public void testHandlingMultipleMatchingPackets() {
        // Create a more complex scenario where multiple packets might match
        // For example, we could have a general packet that matches all costruzione types
        // and specific packets for each costruzione type
        
        // Create a general packet for S, C that matches any costruzione
        ObjectNode generalPacketCondition = objectMapper.createObjectNode();
        ArrayNode inclusionRules = generalPacketCondition.putArray("inclusionRules");
        
        // Rule 1: tipologiaUsoAbitazione = S
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "tipologiaUsoAbitazione");
        rule1.put("value", "S");
        rule1.put("operator", "eq");
        inclusionRules.add(rule1);
        
        // Rule 2: tipologiaTitoloAbitazione = C
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaTitoloAbitazione");
        rule2.put("value", "C");
        rule2.put("operator", "eq");
        inclusionRules.add(rule2);
        
        // Set logic operator to AND
        generalPacketCondition.put("logicOperator", "AND");
        
        Packet generalPacket = createPacket(100, "General Packet S-C", generalPacketCondition);
        
        // Create a specific packet for S, C, A
        Packet specificPacket = createPacketWithConditions(101, "Specific Packet S-C-A", "S", "C");
        
        // Add both packets to the list
        List<Packet> testPackets = new ArrayList<>();
        testPackets.add(generalPacket);
        testPackets.add(specificPacket);
        
        // Set values in insured_item
        ObjectNode insuredItem = (ObjectNode) orderItemEntity.getInsured_item();
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("tipologiaCostruttivaAbitazione", "A");
        
        // Filter packets
        List<Packet> filteredPackets = packetRuleEngine.filterPackets(testPackets, orderEntity);
        
        // Verify that both packets match
        assertEquals(2, filteredPackets.size(), "Both general and specific packets should match");
        
        // In a real scenario, we would need to select the best packet
        // For example, we could select the packet with the most specific rules
        // Here we demonstrate a simple approach: select the packet with the highest ID
        Packet selectedPacket = filteredPackets.stream()
            .max((p1, p2) -> Integer.compare(
                p1.packetResponse().getData().getId(), 
                p2.packetResponse().getData().getId()))
            .orElse(null);
        
        assertNotNull(selectedPacket, "A packet should be selected");
        assertEquals(specificPacket.packetResponse().getData().getId(), 
                     selectedPacket.packetResponse().getData().getId(),
                     "The specific packet should be selected");
    }
}
