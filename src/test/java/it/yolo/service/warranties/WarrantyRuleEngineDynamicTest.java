package it.yolo.service.warranties;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderItemEntity;
import it.yolo.service.client.ServiceClientCustomer;
import it.yolo.service.rule.WarrantyRuleEngine;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.inject.Inject;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * Dynamic tests for WarrantyRuleEngine showing the new flexible approach
 * 
 * This demonstrates how to define complex business rules dynamically in JSON
 * without hardcoded switch-case statements in the code.
 */
@QuarkusTest
public class WarrantyRuleEngineDynamicTest {

    @Inject
    WarrantyRuleEngine warrantyRuleEngine;

    @InjectMock
    ServiceClientCustomer serviceClientCustomer;

    private ObjectMapper objectMapper;
    private String testToken = "test-token";

    @BeforeEach
    public void setup() {
        objectMapper = new ObjectMapper();
        setupCustomerServiceMock();
    }

    private void setupCustomerServiceMock() {
        try {
            // Mock for customer age 30
            ObjectNode customerResponse30 = objectMapper.createObjectNode();
            ObjectNode customerData30 = objectMapper.createObjectNode();
            customerData30.put("date_of_birth", "1994-06-15"); // Age ~30
            customerResponse30.set("data", customerData30);
            
            // Mock for customer age 60
            ObjectNode customerResponse60 = objectMapper.createObjectNode();
            ObjectNode customerData60 = objectMapper.createObjectNode();
            customerData60.put("date_of_birth", "1964-06-15"); // Age ~60
            customerResponse60.set("data", customerData60);

            Mockito.when(serviceClientCustomer.findById(eq(30), anyString()))
                   .thenReturn(customerResponse30);
            Mockito.when(serviceClientCustomer.findById(eq(60), anyString()))
                   .thenReturn(customerResponse60);
        } catch (Exception e) {
            fail("Mock setup failed: " + e.getMessage());
        }
    }

    private OrderItemEntity createOrderItemWithCustomer(int customerId, int age) {
        OrderItemEntity orderItem = new OrderItemEntity();
        ObjectNode insuredItem = objectMapper.createObjectNode();
        
        ObjectNode customer = objectMapper.createObjectNode();
        customer.put("id", customerId);
        customer.put("age", age);
        insuredItem.set("customer", customer);
        
        orderItem.setInsured_item(insuredItem);
        return orderItem;
    }

    private JsonNode createWarranty(String id, String name, String type, JsonNode rule) {
        ObjectNode warranty = objectMapper.createObjectNode();
        warranty.put("id", id);
        warranty.put("name", name);
        warranty.put("type", type);
        if (rule != null) {
            warranty.set("rule", rule);
        }
        return warranty;
    }

    private JsonNode createWarrantyRule(JsonNode... ruleGroups) {
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode groups = objectMapper.createArrayNode();
        for (JsonNode group : ruleGroups) {
            groups.add(group);
        }
        rule.set("warrantyRuleGroups", groups);
        rule.put("groupLogicOperator", "AND");
        return rule;
    }

    private JsonNode createRuleGroup(String type, String operator, JsonNode... rules) {
        ObjectNode group = objectMapper.createObjectNode();
        group.put("type", type);
        group.put("operator", operator);
        ArrayNode rulesArray = objectMapper.createArrayNode();
        for (JsonNode rule : rules) {
            rulesArray.add(rule);
        }
        group.set("rules", rulesArray);
        return group;
    }

    private JsonNode createCondition(String field, String operator, String value) {
        ObjectNode condition = objectMapper.createObjectNode();
        condition.put("field", field);
        condition.put("operator", operator);
        condition.put("value", value);
        return condition;
    }

    /**
     * Create a conditional duration rule following this format:
     * {
     *   "conditions": [
     *     {
     *       "if": {"field": "customer.age", "operator": "lte", "value": "55"},
     *       "then": {"maxDuration": 10}
     *     },
     *     {
     *       "if": {"field": "customer.age", "operator": "gt", "value": "55"},
     *       "then": {"durationFormula": "65-customer.age"}
     *     }
     *   ]
     * }
     */
    private JsonNode createConditionalDurationRule() {
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode conditions = objectMapper.createArrayNode();

        // Condition 1: if age <= 55 then maxDuration = 10
        ObjectNode condition1 = objectMapper.createObjectNode();
        ObjectNode ifPart1 = objectMapper.createObjectNode();
        ifPart1.put("field", "customer.age");
        ifPart1.put("operator", "lte");
        ifPart1.put("value", "55");
        condition1.set("if", ifPart1);
        
        ObjectNode thenPart1 = objectMapper.createObjectNode();
        thenPart1.put("maxDuration", 10);
        condition1.set("then", thenPart1);
        conditions.add(condition1);

        // Condition 2: if age > 55 then formula = "65-customer.age"
        ObjectNode condition2 = objectMapper.createObjectNode();
        ObjectNode ifPart2 = objectMapper.createObjectNode();
        ifPart2.put("field", "customer.age");
        ifPart2.put("operator", "gt");
        ifPart2.put("value", "55");
        condition2.set("if", ifPart2);
        
        ObjectNode thenPart2 = objectMapper.createObjectNode();
        thenPart2.put("durationFormula", "65-customer.age");
        condition2.set("then", thenPart2);
        conditions.add(condition2);

        rule.set("conditions", conditions);
        return rule;
    }

    /**
     * Create a HOSP-style conditional rule for age < 65 vs >= 65
     */
    private JsonNode createHOSPConditionalDurationRule() {
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode conditions = objectMapper.createArrayNode();

        // For customers < 65: apply TTD/ILOE rules
        ObjectNode condition1 = objectMapper.createObjectNode();
        ObjectNode ifPart1 = objectMapper.createObjectNode();
        ifPart1.put("field", "customer.age");
        ifPart1.put("operator", "lt");
        ifPart1.put("value", "65");
        condition1.set("if", ifPart1);
        
        // Nested conditional for < 65
        ObjectNode thenPart1 = objectMapper.createObjectNode();
        ArrayNode nestedConditions = objectMapper.createArrayNode();
        
        // If age <= 55 then 10 years
        ObjectNode nestedCond1 = objectMapper.createObjectNode();
        ObjectNode nestedIf1 = objectMapper.createObjectNode();
        nestedIf1.put("field", "customer.age");
        nestedIf1.put("operator", "lte");
        nestedIf1.put("value", "55");
        nestedCond1.set("if", nestedIf1);
        ObjectNode nestedThen1 = objectMapper.createObjectNode();
        nestedThen1.put("maxDuration", 10);
        nestedCond1.set("then", nestedThen1);
        nestedConditions.add(nestedCond1);
        
        // If age > 55 then 65-age
        ObjectNode nestedCond2 = objectMapper.createObjectNode();
        ObjectNode nestedIf2 = objectMapper.createObjectNode();
        nestedIf2.put("field", "customer.age");
        nestedIf2.put("operator", "gt");
        nestedIf2.put("value", "55");
        nestedCond2.set("if", nestedIf2);
        ObjectNode nestedThen2 = objectMapper.createObjectNode();
        nestedThen2.put("durationFormula", "65-customer.age");
        nestedCond2.set("then", nestedThen2);
        nestedConditions.add(nestedCond2);
        
        thenPart1.set("conditions", nestedConditions);
        condition1.set("then", thenPart1);
        conditions.add(condition1);

        // For customers >= 65: use HOSP specific rules (80-age formula with constraints)
        ObjectNode condition2 = objectMapper.createObjectNode();
        ObjectNode ifPart2 = objectMapper.createObjectNode();
        ifPart2.put("field", "customer.age");
        ifPart2.put("operator", "gte");
        ifPart2.put("value", "65");
        condition2.set("if", ifPart2);
        
        ObjectNode thenPart2 = objectMapper.createObjectNode();
        thenPart2.put("durationFormula", "80-customer.age");
        condition2.set("then", thenPart2);
        conditions.add(condition2);

        rule.set("conditions", conditions);
        return rule;
    }

    @Test
    @DisplayName("Dynamic TTD - Customer age 30 using conditional rules")
    public void testDynamicTTD_Customer30() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, 30);
        
        // Age eligibility
        JsonNode ageMinCondition = createCondition("customer.age", "gt", "18");
        JsonNode ageMaxCondition = createCondition("customer.age", "lt", "65");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageMinCondition, ageMaxCondition);
        
        // Dynamic duration rules - completely configured in JSON
        JsonNode durationRule = createConditionalDurationRule();
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", durationRule);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("DYNAMIC_TTD", "Dynamic TTD", "ANY_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        assertEquals(1, result.getFilteredWarranties().size(), "Dynamic TTD should be available for age 30");
        assertEquals(Integer.valueOf(10), result.getMaxDurations().get("DYNAMIC_TTD"), 
                    "Dynamic TTD max duration should be 10 years for age 30 (age <= 55 condition)");
    }

    @Test
    @DisplayName("Dynamic TTD - Customer age 60 using conditional rules")
    public void testDynamicTTD_Customer60() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(60, 60);
        
        JsonNode ageMinCondition = createCondition("customer.age", "gt", "18");
        JsonNode ageMaxCondition = createCondition("customer.age", "lt", "65");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageMinCondition, ageMaxCondition);
        
        JsonNode durationRule = createConditionalDurationRule();
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", durationRule);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("DYNAMIC_TTD_60", "Dynamic TTD", "ANY_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        assertEquals(1, result.getFilteredWarranties().size(), "Dynamic TTD should be available for age 60");
        assertEquals(Integer.valueOf(5), result.getMaxDurations().get("DYNAMIC_TTD_60"), 
                    "Dynamic TTD max duration should be 5 years for age 60 (65-60 formula)");
    }

    @Test
    @DisplayName("Dynamic HOSP-style warranty with complex conditional logic")
    public void testDynamicHOSP_ComplexConditional() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, 30);
        
        JsonNode ageMinCondition = createCondition("customer.age", "gt", "18");
        JsonNode ageMaxCondition = createCondition("customer.age", "lt", "80");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageMinCondition, ageMaxCondition);
        
        JsonNode hospDurationRule = createHOSPConditionalDurationRule();
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", hospDurationRule);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("DYNAMIC_HOSP", "Dynamic HOSP", "ANY_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        assertEquals(1, result.getFilteredWarranties().size(), "Dynamic HOSP should be available for age 30");
        assertEquals(Integer.valueOf(10), result.getMaxDurations().get("DYNAMIC_HOSP"), 
                    "Dynamic HOSP max duration should be 10 years for age 30 (< 65 and <= 55 conditions)");
    }

    @Test
    @DisplayName("Multiple maxDuration rules - minimum applies")
    public void testMultipleMaxDurationRules() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, 30);
        
        JsonNode ageCondition = createCondition("customer.age", "gt", "18");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageCondition);
        
        // Multiple duration rules - the minimum should apply
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("maxDuration", 15);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("durationFormula", "65-customer.age"); // 65-30 = 35
        
        ObjectNode rule3 = objectMapper.createObjectNode();
        rule3.put("maxDuration", 12);
        
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", rule1, rule2, rule3);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("MULTI_DURATION", "Multi Duration Test", "ANY_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        assertEquals(1, result.getFilteredWarranties().size(), "Warranty should be available");
        assertEquals(Integer.valueOf(12), result.getMaxDurations().get("MULTI_DURATION"), 
                    "Minimum duration should apply (min(15, 35, 12) = 12)");
    }

    @Test
    @DisplayName("Conditional duration with fixed duration and condition")
    public void testConditionalFixedDuration() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, 30);
        
        JsonNode ageCondition = createCondition("customer.age", "gt", "18");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageCondition);
        
        // Fixed duration with condition
        ObjectNode durationRule = objectMapper.createObjectNode();
        durationRule.put("maxDuration", 8);
        
        // Add condition: only applies if age < 40
        ObjectNode condition = objectMapper.createObjectNode();
        condition.put("field", "customer.age");
        condition.put("operator", "lt");
        condition.put("value", "40");
        durationRule.set("condition", condition);
        
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", durationRule);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("CONDITIONAL_FIXED", "Conditional Fixed Duration", "ANY_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        assertEquals(1, result.getFilteredWarranties().size(), "Warranty should be available");
        assertEquals(Integer.valueOf(8), result.getMaxDurations().get("CONDITIONAL_FIXED"), 
                    "Fixed duration should apply when condition is met (age 30 < 40)");
    }

    @Test
    @DisplayName("JSON output contains all warranty information")
    public void testJSONOutputStructure() {
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, 30);
        
        JsonNode ageCondition = createCondition("customer.age", "gt", "18");
        JsonNode inclusionGroup = createRuleGroup("inclusion", "AND", ageCondition);
        
        JsonNode durationRule = createConditionalDurationRule();
        JsonNode durationGroup = createRuleGroup("maxDuration", "AND", durationRule);
        
        JsonNode rule = createWarrantyRule(inclusionGroup, durationGroup);
        JsonNode warranty = createWarranty("JSON_TEST", "JSON Test Warranty", "TEST_TYPE", rule);
        
        List<JsonNode> warranties = List.of(warranty);
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken);
        
        JsonNode jsonOutput = warrantyRuleEngine.createFilterWarrantiesNode(result);
        
        assertTrue(jsonOutput.has("warranties"), "JSON should have warranties array");
        assertTrue(jsonOutput.has("maxDurations"), "JSON should have maxDurations object");
        
        JsonNode warrantiesArray = jsonOutput.get("warranties");
        assertEquals(1, warrantiesArray.size(), "Should have one warranty");
        
        JsonNode warrantyNode = warrantiesArray.get(0);
        assertTrue(warrantyNode.has("id"), "Warranty should have id");
        assertTrue(warrantyNode.has("name"), "Warranty should have name");
        assertTrue(warrantyNode.has("type"), "Warranty should have type");
        assertTrue(warrantyNode.has("maxDuration"), "Warranty should have maxDuration");
        
        assertEquals("JSON_TEST", warrantyNode.get("id").asText());
        assertEquals("JSON Test Warranty", warrantyNode.get("name").asText());
        assertEquals("TEST_TYPE", warrantyNode.get("type").asText());
        assertEquals(10, warrantyNode.get("maxDuration").asInt());
    }
}
