package it.yolo.service.warranties;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderItemEntity;
import it.yolo.service.client.ServiceClientCustomer;
import it.yolo.service.rule.WarrantyRuleEngine;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.inject.Inject;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * Test per verificare che il WarrantyRuleEngine:
 * 1. Aggiunga maxDuration alle warranties nella response del metodo filterWarranties
 * 2. Rimuova il campo rule dalle warranties nella response
 */
@QuarkusTest
public class WarrantyRuleEngineResponseTest {

    @Inject
    private WarrantyRuleEngine warrantyRuleEngine;

    @InjectMock
    private ServiceClientCustomer serviceClientCustomer;

    private ObjectMapper objectMapper;
    private String testToken = "test-token";    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        
        // Mock del service client customer per evitare chiamate esterne
        ObjectNode mockCustomerResponse = objectMapper.createObjectNode();
        ObjectNode mockCustomerData = objectMapper.createObjectNode();
        mockCustomerData.put("age", 30);
        mockCustomerData.put("employmentType", "PERMANENT_EMPLOYEE");
        mockCustomerData.put("healthProfile", "STANDARD");
        mockCustomerResponse.set("data", mockCustomerData);
        
        // Mock per qualsiasi customer ID
        Mockito.when(serviceClientCustomer.findById(anyInt(), anyString())).thenReturn(mockCustomerResponse);
    }

    @Test
    @DisplayName("filterWarranties should add maxDuration and remove rule field")
    public void testFilterWarrantiesAddsMaxDurationAndRemovesRule() {
        // Arrange
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, "PERMANENT_EMPLOYEE");
        
        // Create a warranty with rules
        JsonNode warranty = createWarrantyWithRule("TTD_TEST", "TTD Test", "TTD");
        List<JsonNode> warranties = List.of(warranty);

        // Act
        List<JsonNode> result = warrantyRuleEngine.filterWarranties(orderItem, warranties, testToken);

        // Assert
        assertEquals(1, result.size(), "Should return one filtered warranty");
        
        JsonNode filteredWarranty = result.get(0);
        
        // Check that maxDuration was added
        assertTrue(filteredWarranty.has("maxDuration"), "Warranty should have maxDuration field");
        assertNotNull(filteredWarranty.get("maxDuration"), "maxDuration should not be null");
        assertTrue(filteredWarranty.get("maxDuration").isInt(), "maxDuration should be an integer");
        
        // Check that rule field was removed
        assertFalse(filteredWarranty.has("rule"), "Warranty should not have rule field in response");
        
        // Check that other fields are preserved
        assertTrue(filteredWarranty.has("id"), "Warranty should still have id field");
        assertTrue(filteredWarranty.has("name"), "Warranty should still have name field");
        assertTrue(filteredWarranty.has("warrantyType"), "Warranty should still have warrantyType field");
    }

    @Test
    @DisplayName("filterWarrantiesWithDurations should preserve rule field when removeRulesFromResponse is false")
    public void testFilterWarrantiesWithDurationsPreservesRuleWhenRequested() {
        // Arrange
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, "PERMANENT_EMPLOYEE");
        JsonNode warranty = createWarrantyWithRule("TTD_TEST", "TTD Test", "TTD");
        List<JsonNode> warranties = List.of(warranty);

        // Act
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken, false);

        // Assert
        assertEquals(1, result.getFilteredWarranties().size(), "Should return one filtered warranty");
        
        JsonNode filteredWarranty = result.getFilteredWarranties().get(0);
        
        // Check that rule field is preserved when removeRulesFromResponse is false
        assertTrue(filteredWarranty.has("rule"), "Warranty should still have rule field when removeRulesFromResponse is false");
        
        // Check that maxDuration is in the map
        assertTrue(result.getMaxDurations().containsKey("TTD_TEST"), "MaxDurations map should contain the warranty ID");
        assertNotNull(result.getMaxDurations().get("TTD_TEST"), "MaxDuration should not be null");
    }

    @Test
    @DisplayName("filterWarrantiesWithDurations should remove rule field and add maxDuration when removeRulesFromResponse is true")
    public void testFilterWarrantiesWithDurationsRemovesRuleWhenRequested() {
        // Arrange
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, "PERMANENT_EMPLOYEE");
        JsonNode warranty = createWarrantyWithRule("TTD_TEST", "TTD Test", "TTD");
        List<JsonNode> warranties = List.of(warranty);

        // Act
        WarrantyRuleEngine.WarrantyFilterResult result = 
            warrantyRuleEngine.filterWarrantiesWithDurations(orderItem, warranties, testToken, true);

        // Assert
        assertEquals(1, result.getFilteredWarranties().size(), "Should return one filtered warranty");
        
        JsonNode filteredWarranty = result.getFilteredWarranties().get(0);
        
        // Check that rule field was removed
        assertFalse(filteredWarranty.has("rule"), "Warranty should not have rule field when removeRulesFromResponse is true");
        
        // Check that maxDuration was added to the warranty node
        assertTrue(filteredWarranty.has("maxDuration"), "Warranty should have maxDuration field in the node");
        assertNotNull(filteredWarranty.get("maxDuration"), "maxDuration should not be null");
        
        // Check that maxDuration is also in the map
        assertTrue(result.getMaxDurations().containsKey("TTD_TEST"), "MaxDurations map should contain the warranty ID");
        assertEquals(filteredWarranty.get("maxDuration").asInt(), 
                    result.getMaxDurations().get("TTD_TEST").intValue(), 
                    "MaxDuration in warranty node should match the one in the map");
    }

    @Test
    @DisplayName("Debug test - verify warranty structure and customer data")
    public void testDebugWarrantyStructure() {
        // Arrange
        OrderItemEntity orderItem = createOrderItemWithCustomer(30, "PERMANENT_EMPLOYEE");
        
        // Log the structure for debugging
        JsonNode insuredItem = orderItem.getInsured_item();
        System.out.println("=== DEBUG: InsuredItem structure ===");
        System.out.println(insuredItem.toPrettyString());
        
        JsonNode warranty = createWarrantyWithRule("TTD_TEST", "TTD Test", "TTD");
        System.out.println("=== DEBUG: Warranty structure ===");
        System.out.println(warranty.toPrettyString());
        
        List<JsonNode> warranties = List.of(warranty);

        // Act
        List<JsonNode> result = warrantyRuleEngine.filterWarranties(orderItem, warranties, testToken);

        // Debug output
        System.out.println("=== DEBUG: Filter result ===");
        System.out.println("Number of filtered warranties: " + result.size());
    }    private OrderItemEntity createOrderItemWithCustomer(int age, String employmentType) {
        OrderItemEntity orderItem = new OrderItemEntity();
        
        ObjectNode insuredItem = objectMapper.createObjectNode();
        
        // Create customer node with both age (for direct access) and id (for service call)
        ObjectNode customer = objectMapper.createObjectNode();
        customer.put("id", 123); // Mock customer ID that matches the service call
        customer.put("age", age);
        customer.put("employmentType", employmentType);
        customer.put("healthProfile", "STANDARD");
        
        // Add customer directly to insured_item
        insuredItem.set("customer", customer);
        orderItem.setInsured_item(insuredItem);
        
        return orderItem;
    }

    private JsonNode createWarrantyWithRule(String id, String name, String warrantyType) {
        ObjectNode warranty = objectMapper.createObjectNode();
        warranty.put("id", id);
        warranty.put("name", name);
        warranty.put("warrantyType", warrantyType);
        
        // Add a rule with inclusion criteria and maxDuration
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("ruleLogicOperator", "AND");
        
        ArrayNode ruleGroups = objectMapper.createArrayNode();
        
        // Inclusion group
        ObjectNode inclusionGroup = objectMapper.createObjectNode();
        inclusionGroup.put("type", "inclusion");
        inclusionGroup.put("logicOperator", "AND");
        
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode ageRule = objectMapper.createObjectNode();
        ageRule.put("field", "customer.age");
        ageRule.put("operator", "gte");
        ageRule.put("value", "18");
        inclusionRules.add(ageRule);
        
        inclusionGroup.set("rules", inclusionRules);
        ruleGroups.add(inclusionGroup);
        
        // MaxDuration group
        ObjectNode durationGroup = objectMapper.createObjectNode();
        durationGroup.put("type", "maxDuration");
        durationGroup.put("logicOperator", "AND");
        
        ArrayNode durationRules = objectMapper.createArrayNode();
        ObjectNode durationRule = objectMapper.createObjectNode();
        durationRule.put("maxDuration", 10);
        durationRules.add(durationRule);
        
        durationGroup.set("rules", durationRules);
        ruleGroups.add(durationGroup);
        
        rule.set("warrantyRuleGroups", ruleGroups);
        warranty.set("rule", rule);
        
        return warranty;
    }
}
